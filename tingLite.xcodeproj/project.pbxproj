// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		046B6F6E272F82E70064AD03 /* Pods_tingLiteAPNSerEx.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6D2D796A99D8D4EDDB6F7115 /* Pods_tingLiteAPNSerEx.framework */; };
		04B99C45279F9BBC00543EF3 /* XMModuleManager+Book.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04B99C44279F9BBC00543EF3 /* XMModuleManager+Book.swift */; };
		0F73ABBF2466819F00946106 /* AppDelegate+APM.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0F73ABBE2466819F00946106 /* AppDelegate+APM.swift */; };
		220BFE5E2746414300A52052 /* XMReaderModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6469352C2738D3B0004EED3B /* XMReaderModule.framework */; };
		22675BD2277EDAE900B903BC /* playing_2.png in Resources */ = {isa = PBXBuildFile; fileRef = 22675BC9277EDAE900B903BC /* playing_2.png */; };
		22675BD3277EDAE900B903BC /* playing_3.png in Resources */ = {isa = PBXBuildFile; fileRef = 22675BCA277EDAE900B903BC /* playing_3.png */; };
		22675BD4277EDAE900B903BC /* playing_1.png in Resources */ = {isa = PBXBuildFile; fileRef = 22675BCB277EDAE900B903BC /* playing_1.png */; };
		22675BD5277EDAE900B903BC /* playing_4.png in Resources */ = {isa = PBXBuildFile; fileRef = 22675BCC277EDAE900B903BC /* playing_4.png */; };
		22675BD6277EDAE900B903BC /* playing_5.png in Resources */ = {isa = PBXBuildFile; fileRef = 22675BCD277EDAE900B903BC /* playing_5.png */; };
		22675BD7277EDAE900B903BC /* playing_7.png in Resources */ = {isa = PBXBuildFile; fileRef = 22675BCE277EDAE900B903BC /* playing_7.png */; };
		22675BD8277EDAE900B903BC /* playing_6.png in Resources */ = {isa = PBXBuildFile; fileRef = 22675BCF277EDAE900B903BC /* playing_6.png */; };
		22675BD9277EDAE900B903BC /* playing_8.png in Resources */ = {isa = PBXBuildFile; fileRef = 22675BD0277EDAE900B903BC /* playing_8.png */; };
		22675BDA277EDAE900B903BC /* playing_9.png in Resources */ = {isa = PBXBuildFile; fileRef = 22675BD1277EDAE900B903BC /* playing_9.png */; };
		229C9E5127184EEC0043895D /* switchToCarfriend.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 229C9E4F27184EEB0043895D /* switchToCarfriend.mp3 */; };
		229C9E5227184EEC0043895D /* switchToClassic.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 229C9E5027184EEB0043895D /* switchToClassic.mp3 */; };
		255CAC2523059A170023A705 /* ShareModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 255CAC2423059A170023A705 /* ShareModule.framework */; };
		27B959D209C224E66BC68706 /* Pods_tingLite.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C18EF409E2CE22DD6A7262DD /* Pods_tingLite.framework */; };
		85D19EB024400CEF0018B1E7 /* XMModuleManager+SceneRadio.swift in Sources */ = {isa = PBXBuildFile; fileRef = 85D19EAF24400CEF0018B1E7 /* XMModuleManager+SceneRadio.swift */; };
		8804FB7E2324F80F007BEB61 /* XMModuleManager+Login.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8804FB7D2324F80F007BEB61 /* XMModuleManager+Login.swift */; };
		8804FB802324F861007BEB61 /* XMModuleManager+Base.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8804FB7F2324F861007BEB61 /* XMModuleManager+Base.swift */; };
		8804FB822324F919007BEB61 /* XMModuleManager+Audio.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8804FB812324F919007BEB61 /* XMModuleManager+Audio.swift */; };
		8804FB842324F989007BEB61 /* XMModuleManager+Listen.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8804FB832324F989007BEB61 /* XMModuleManager+Listen.swift */; };
		8804FB882324FA35007BEB61 /* XMModuleManager+Network.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8804FB872324FA35007BEB61 /* XMModuleManager+Network.swift */; };
		8817FB2022D8464A00DC0084 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8817FB1F22D8464A00DC0084 /* AppDelegate.swift */; };
		8817FB2C22D8464A00DC0084 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8817FB2A22D8464A00DC0084 /* LaunchScreen.storyboard */; };
		8817FC2722D898DF00DC0084 /* XMModuleManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8817FC2622D898DF00DC0084 /* XMModuleManager.swift */; };
		88CAD8112318CDC60058EAE5 /* XMModuleManager+Bridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88CAD8102318CDC60058EAE5 /* XMModuleManager+Bridge.swift */; };
		9507943924920B8F00E054D0 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9507943824920B8E00E054D0 /* GoogleService-Info.plist */; };
		950C2BC325527421004E43F4 /* catguardiana.png in Resources */ = {isa = PBXBuildFile; fileRef = 950C2BC225527421004E43F4 /* catguardiana.png */; };
		950C2BC82552816A004E43F4 /* catguardianb.png in Resources */ = {isa = PBXBuildFile; fileRef = 950C2BC72552816A004E43F4 /* catguardianb.png */; };
		9514EFCB26CBC428006BA316 /* xmubtPrepared.json in Resources */ = {isa = PBXBuildFile; fileRef = 9514EFCA26CBC428006BA316 /* xmubtPrepared.json */; };
		9530A2E52398D9B300C275B4 /* LoginModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 88417FCF2301657800B12190 /* LoginModule.framework */; };
		9537A58B23B0AA92003B7C26 /* XMLDeviceManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9537A58A23B0AA92003B7C26 /* XMLDeviceManager.swift */; };
		954344B526CF5214004DA6BD /* XMModuleManager+Immerse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954344B426CF5214004DA6BD /* XMModuleManager+Immerse.swift */; };
		954AF2E92536E8E20013939F /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 954AF2E82536E8E20013939F /* <EMAIL> */; };
		954AF2F32537E1E70013939F /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 954AF2F22537E1E70013939F /* <EMAIL> */; };
		954AF2F52537E22F0013939F /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 954AF2F42537E22F0013939F /* <EMAIL> */; };
		954AF2F92537E2A30013939F /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 954AF2F82537E2A30013939F /* <EMAIL> */; };
		954AF2FD2537E4B70013939F /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 954AF2FC2537E4B70013939F /* <EMAIL> */; };
		954AF3032537E63B0013939F /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 954AF3022537E63B0013939F /* <EMAIL> */; };
		955C01F824206026003C3645 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95472F1223C5F44200C826F9 /* StoreKit.framework */; };
		956E093427450C5F00690062 /* ImmerseModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 956E093327450C5F00690062 /* ImmerseModule.framework */; };
		956FCD23268AEAEB0098B44B /* tingLiteAPNSerEx.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 956FCD1C268AEAEB0098B44B /* tingLiteAPNSerEx.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		956FCD29268B1E470098B44B /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 956FCD28268B1E470098B44B /* NotificationService.swift */; };
		9573334C2484EE6A00F105C0 /* XMADXModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9573334B2484EE6A00F105C0 /* XMADXModule.framework */; };
		9580802127DC633D004AFE14 /* UMRemoteConfig.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9580801D27DC633D004AFE14 /* UMRemoteConfig.framework */; };
		9580802227DC633D004AFE14 /* UMCommon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9580801E27DC633D004AFE14 /* UMCommon.framework */; };
		9580802327DC633D004AFE14 /* UMAPM.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9580801F27DC633D004AFE14 /* UMAPM.framework */; };
		9580802427DC633D004AFE14 /* UMDevice.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9580802027DC633D004AFE14 /* UMDevice.framework */; };
		95812BE724AC8CBD007EAAE7 /* launchLogo1.png in Resources */ = {isa = PBXBuildFile; fileRef = 95812BE624AC8CBD007EAAE7 /* launchLogo1.png */; };
		95A1C65226FC63A100D6A14B /* XMLDeviceManager+Growth.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A1C65126FC63A100D6A14B /* XMLDeviceManager+Growth.swift */; };
		95B24E14250B645900D50A34 /* XMLMediaLogicManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B24E13250B645900D50A34 /* XMLMediaLogicManager.swift */; };
		95B44F4424485A63000E442F /* XMModuleManager+Ad.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B44F4324485A63000E442F /* XMModuleManager+Ad.swift */; };
		95BF94B9255255ED00F2B197 /* AppDelegate+Wizard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95BF94B8255255ED00F2B197 /* AppDelegate+Wizard.swift */; };
		95C11B392512004F000F31D7 /* XMLAppContext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C11B382512004F000F31D7 /* XMLAppContext.swift */; };
		95C4800926D77EBE00F8095C /* xmlMain.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 95C4800826D77EBE00F8095C /* xmlMain.ttf */; };
		95CB5307274283CF00C25C4C /* iAd.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95CB5306274283CE00C25C4C /* iAd.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		95CB5309274283D700C25C4C /* AdServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95CB5308274283D700C25C4C /* AdServices.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		95CB530A274283DC00C25C4C /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95472F0023C5F3F000C826F9 /* AdSupport.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		95D400A0248F24860093F596 /* CommBusiness.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95D4009F248F24850093F596 /* CommBusiness.framework */; };
		95DB9FA0238E768600BDE6B2 /* XMLTabBarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DB9F9D238E768500BDE6B2 /* XMLTabBarController.swift */; };
		95DB9FA1238E768600BDE6B2 /* XMLTabBarCustomItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DB9F9E238E768500BDE6B2 /* XMLTabBarCustomItem.swift */; };
		95DB9FA2238E768600BDE6B2 /* XMLTabBarButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95DB9F9F238E768500BDE6B2 /* XMLTabBarButton.swift */; };
		95EA9BA5254BC107007F3297 /* AppDelegate+FingerPrint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95EA9BA4254BC107007F3297 /* AppDelegate+FingerPrint.swift */; };
		95EB615E244AFC2100F2E750 /* ApplicationConst.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95EB615D244AFC2100F2E750 /* ApplicationConst.swift */; };
		95EBC2C72750E2580089C909 /* praise.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 95EBC2C62750E2580089C909 /* praise.mp3 */; };
		95EE411725D13DFE00010E51 /* XMLListeningTipContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95EE411625D13DFE00010E51 /* XMLListeningTipContainer.swift */; };
		95F051E124ECC5340051E7DD /* XMICFontMain.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95F051E024ECC5340051E7DD /* XMICFontMain.swift */; };
		95FE7C1C2746231200613F67 /* XMModuleManager+Share.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95FE7C1B2746231200613F67 /* XMModuleManager+Share.swift */; };
		95FFCC4B2671B198001FCF2E /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95472F0423C5F3FF00C826F9 /* CoreTelephony.framework */; };
		95FFCC4C2671B1A2001FCF2E /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 95472EF623C5F3BF00C826F9 /* libz.tbd */; };
		95FFCC4D2671B1B2001FCF2E /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 95472EF423C5F3B400C826F9 /* libsqlite3.tbd */; };
		95FFCC4E2671B1BB001FCF2E /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95472F0223C5F3F800C826F9 /* SystemConfiguration.framework */; };
		B06E0D0322FA9C8600F4B93D /* RouterModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B06E0D0222FA9C8600F4B93D /* RouterModule.framework */; };
		B0B014D02304157000EC7B5C /* MediaModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B014CF2304157000EC7B5C /* MediaModule.framework */; };
		B0B6035522F99148004CE92D /* AudioModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6035422F99148004CE92D /* AudioModule.framework */; };
		B0B6035722F9914E004CE92D /* BaseModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6035622F9914E004CE92D /* BaseModule.framework */; };
		B0B6035922F99151004CE92D /* HomeModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6035822F99151004CE92D /* HomeModule.framework */; };
		B0B6035B22F99155004CE92D /* MineModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6035A22F99155004CE92D /* MineModule.framework */; };
		B0B6035D22F99160004CE92D /* MyListenModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6035C22F99160004CE92D /* MyListenModule.framework */; };
		B0B6035F22F99166004CE92D /* SearchModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6035E22F99166004CE92D /* SearchModule.framework */; };
		B0B6036322F99174004CE92D /* XMConfigModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6036222F99174004CE92D /* XMConfigModule.framework */; };
		B0B6036522F9917D004CE92D /* XMNetworkModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6036422F9917D004CE92D /* XMNetworkModule.framework */; };
		B0B6036722F99184004CE92D /* XMWebModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6036622F99184004CE92D /* XMWebModule.framework */; };
		B0C110A1234C803E00DE3459 /* XMUtilModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0C110A0234C803E00DE3459 /* XMUtilModule.framework */; };
		B6072D5326D6162600E55C8A /* XMLTabBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6072D5226D6162500E55C8A /* XMLTabBar.swift */; };
		B6454B732790231400552757 /* XMShortVideoModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6C7ABB5255CE0DF00F50F3D /* XMShortVideoModule.framework */; };
		B66A940A259475360027C34D /* XMAlertModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B66A9409259475360027C34D /* XMAlertModule.framework */; };
		B66ADC8F242DA19100BF4970 /* AppDelegate+XMNet.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66ADC8E242DA19100BF4970 /* AppDelegate+XMNet.swift */; };
		B66ADC93242DE07F00BF4970 /* AppDelegate+Notification.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66ADC92242DE07F00BF4970 /* AppDelegate+Notification.swift */; };
		B66ADC97242DE74400BF4970 /* AppDelegate+UBT.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66ADC96242DE74400BF4970 /* AppDelegate+UBT.swift */; };
		B66ADC99242DE8E700BF4970 /* AppDelegate+XMPay.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66ADC98242DE8E700BF4970 /* AppDelegate+XMPay.swift */; };
		B66ADC9B242DF12E00BF4970 /* AppDelegate+PerformURL.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66ADC9A242DF12E00BF4970 /* AppDelegate+PerformURL.swift */; };
		B66C0BA32739471300F05AF8 /* XMBookModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B66C0BA22739471300F05AF8 /* XMBookModule.framework */; };
		B677D7252446D6AD0060FEDE /* XMModuleManager+GobalTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = B677D7242446D6AD0060FEDE /* XMModuleManager+GobalTask.swift */; };
		B68109D3273B728C00A43E10 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8817FB2822D8464A00DC0084 /* Assets.xcassets */; };
		B681F3FB248796590056E41D /* morentouxiang.png in Resources */ = {isa = PBXBuildFile; fileRef = B681F3F9248796580056E41D /* morentouxiang.png */; };
		B681F3FD24879B410056E41D /* vipModule.plist in Resources */ = {isa = PBXBuildFile; fileRef = B681F3FC24879B410056E41D /* vipModule.plist */; };
		B682AA6E2477756D0067BD47 /* XMModuleManager+Home.swift in Sources */ = {isa = PBXBuildFile; fileRef = B682AA6D2477756D0067BD47 /* XMModuleManager+Home.swift */; };
		B682AA732477A8480067BD47 /* VipModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B682AA722477A8480067BD47 /* VipModule.framework */; };
		B68AE0BE231CDE6A00422C5A /* AppDelegate+Cipher.swift in Sources */ = {isa = PBXBuildFile; fileRef = B68AE0BD231CDE6A00422C5A /* AppDelegate+Cipher.swift */; };
		B68FDDAA276B598100333F3C /* XMCommentModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B68FDDA9276B598100333F3C /* XMCommentModule.framework */; };
		B6987178230D2FAA00220228 /* XMHistoryModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6987177230D2FAA00220228 /* XMHistoryModule.framework */; };
		B6A264FA24F75F0200DADBCD /* globalCoinAlertDoubleAni.json in Resources */ = {isa = PBXBuildFile; fileRef = B6A264F924F75F0200DADBCD /* globalCoinAlertDoubleAni.json */; };
		B6E5690D29C33C2300695C6B /* libswiftCoreGraphics.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = B6E5690C29C33C2300695C6B /* libswiftCoreGraphics.tbd */; };
		BB1EE54C26FC62E200749C03 /* XMLiveModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BB1EE54B26FC62E200749C03 /* XMLiveModule.framework */; };
		BBD2BFBA270330F000A6BF7F /* XimaZhiboti-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BBD2BFB9270326F400A6BF7F /* XimaZhiboti-Regular.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		956FCD21268AEAEB0098B44B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8817FB1422D8464A00DC0084 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 956FCD1B268AEAEB0098B44B;
			remoteInfo = tingLiteAPNSerEx;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		B0F3059B23176C03006C4356 /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				956FCD23268AEAEB0098B44B /* tingLiteAPNSerEx.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		033E51A82D2D09BD0041BBEF /* xm_injection_config.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = xm_injection_config.sh; sourceTree = SOURCE_ROOT; };
		04B99C44279F9BBC00543EF3 /* XMModuleManager+Book.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Book.swift"; sourceTree = "<group>"; };
		0F73ABBE2466819F00946106 /* AppDelegate+APM.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+APM.swift"; sourceTree = "<group>"; };
		0FBCACA00E943B7F12B7BC0B /* Pods-tingLite.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-tingLite.alpha.xcconfig"; path = "Target Support Files/Pods-tingLite/Pods-tingLite.alpha.xcconfig"; sourceTree = "<group>"; };
		220BFE5A2746412200A52052 /* Pods_XMReaderModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Pods_XMReaderModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		22675BC9277EDAE900B903BC /* playing_2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playing_2.png; sourceTree = "<group>"; };
		22675BCA277EDAE900B903BC /* playing_3.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playing_3.png; sourceTree = "<group>"; };
		22675BCB277EDAE900B903BC /* playing_1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playing_1.png; sourceTree = "<group>"; };
		22675BCC277EDAE900B903BC /* playing_4.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playing_4.png; sourceTree = "<group>"; };
		22675BCD277EDAE900B903BC /* playing_5.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playing_5.png; sourceTree = "<group>"; };
		22675BCE277EDAE900B903BC /* playing_7.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playing_7.png; sourceTree = "<group>"; };
		22675BCF277EDAE900B903BC /* playing_6.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playing_6.png; sourceTree = "<group>"; };
		22675BD0277EDAE900B903BC /* playing_8.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playing_8.png; sourceTree = "<group>"; };
		22675BD1277EDAE900B903BC /* playing_9.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = playing_9.png; sourceTree = "<group>"; };
		229C9E4F27184EEB0043895D /* switchToCarfriend.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = switchToCarfriend.mp3; sourceTree = "<group>"; };
		229C9E5027184EEB0043895D /* switchToClassic.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = switchToClassic.mp3; sourceTree = "<group>"; };
		255CAC2423059A170023A705 /* ShareModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ShareModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3D55D4932302921B0041747F /* RNKitModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = RNKitModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3D8866C30413C14294B0BB3C /* Pods-tingLite.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-tingLite.debug.xcconfig"; path = "Target Support Files/Pods-tingLite/Pods-tingLite.debug.xcconfig"; sourceTree = "<group>"; };
		6469352C2738D3B0004EED3B /* XMReaderModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMReaderModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6D2D796A99D8D4EDDB6F7115 /* Pods_tingLiteAPNSerEx.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_tingLiteAPNSerEx.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		777C9FB67350629A1D4269ED /* Pods-tingLiteAPNSerEx.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-tingLiteAPNSerEx.debug.xcconfig"; path = "Target Support Files/Pods-tingLiteAPNSerEx/Pods-tingLiteAPNSerEx.debug.xcconfig"; sourceTree = "<group>"; };
		85D19EAF24400CEF0018B1E7 /* XMModuleManager+SceneRadio.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+SceneRadio.swift"; sourceTree = "<group>"; };
		8804FB7D2324F80F007BEB61 /* XMModuleManager+Login.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Login.swift"; sourceTree = "<group>"; };
		8804FB7F2324F861007BEB61 /* XMModuleManager+Base.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Base.swift"; sourceTree = "<group>"; };
		8804FB812324F919007BEB61 /* XMModuleManager+Audio.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Audio.swift"; sourceTree = "<group>"; };
		8804FB832324F989007BEB61 /* XMModuleManager+Listen.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Listen.swift"; sourceTree = "<group>"; };
		8804FB872324FA35007BEB61 /* XMModuleManager+Network.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Network.swift"; sourceTree = "<group>"; };
		8817FB1C22D8464A00DC0084 /* tingLite.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = tingLite.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8817FB1F22D8464A00DC0084 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		8817FB2822D8464A00DC0084 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8817FB2B22D8464A00DC0084 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8817FB2D22D8464A00DC0084 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8817FC2622D898DF00DC0084 /* XMModuleManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMModuleManager.swift; sourceTree = "<group>"; };
		88417FCF2301657800B12190 /* LoginModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = LoginModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		888EEC8722EC6E020081DE63 /* BaseModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = BaseModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		88B63BF222DC5F97004A700F /* tingLite-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "tingLite-Bridging-Header.h"; sourceTree = "<group>"; };
		88CAD8102318CDC60058EAE5 /* XMModuleManager+Bridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Bridge.swift"; sourceTree = "<group>"; };
		88FA6E6422E34A6E00842042 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		9507943824920B8E00E054D0 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		950C2BC225527421004E43F4 /* catguardiana.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = catguardiana.png; sourceTree = "<group>"; };
		950C2BC72552816A004E43F4 /* catguardianb.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = catguardianb.png; sourceTree = "<group>"; };
		9514EFCA26CBC428006BA316 /* xmubtPrepared.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = xmubtPrepared.json; sourceTree = "<group>"; };
		9537A58A23B0AA92003B7C26 /* XMLDeviceManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLDeviceManager.swift; sourceTree = "<group>"; };
		954344B426CF5214004DA6BD /* XMModuleManager+Immerse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Immerse.swift"; sourceTree = "<group>"; };
		954344BC26CF86E7004DA6BD /* ImmerseModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ImmerseModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95472EF423C5F3B400C826F9 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		95472EF623C5F3BF00C826F9 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		95472EF823C5F3CB00C826F9 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		95472EFA23C5F3D600C826F9 /* libresolv.9.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.9.tbd; path = usr/lib/libresolv.9.tbd; sourceTree = SDKROOT; };
		95472EFC23C5F3DF00C826F9 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		95472EFE23C5F3E800C826F9 /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		95472F0023C5F3F000C826F9 /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		95472F0223C5F3F800C826F9 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		95472F0423C5F3FF00C826F9 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		95472F0623C5F40800C826F9 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		95472F0823C5F41700C826F9 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		95472F0A23C5F42300C826F9 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		95472F0C23C5F42A00C826F9 /* MediaPlayer.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaPlayer.framework; path = System/Library/Frameworks/MediaPlayer.framework; sourceTree = SDKROOT; };
		95472F0E23C5F43200C826F9 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		95472F1023C5F43900C826F9 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		95472F1223C5F44200C826F9 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		95472F1423C5F44C00C826F9 /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = System/Library/Frameworks/ImageIO.framework; sourceTree = SDKROOT; };
		95472F1723C5F49500C826F9 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		954AF2E62536DD860013939F /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		954AF2E82536E8E20013939F /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		954AF2F22537E1E70013939F /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		954AF2F42537E22F0013939F /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		954AF2F82537E2A30013939F /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		954AF2FC2537E4B70013939F /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		954AF3022537E63B0013939F /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		956E093327450C5F00690062 /* ImmerseModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ImmerseModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		956FCD1C268AEAEB0098B44B /* tingLiteAPNSerEx.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = tingLiteAPNSerEx.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		956FCD20268AEAEB0098B44B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		956FCD28268B1E470098B44B /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		9573334B2484EE6A00F105C0 /* XMADXModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMADXModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9573334D2484EED100F105C0 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		9573334F2484EED600F105C0 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		9580801D27DC633D004AFE14 /* UMRemoteConfig.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UMRemoteConfig.framework; sourceTree = "<group>"; };
		9580801E27DC633D004AFE14 /* UMCommon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UMCommon.framework; sourceTree = "<group>"; };
		9580801F27DC633D004AFE14 /* UMAPM.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UMAPM.framework; sourceTree = "<group>"; };
		9580802027DC633D004AFE14 /* UMDevice.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = UMDevice.framework; sourceTree = "<group>"; };
		95812BE624AC8CBD007EAAE7 /* launchLogo1.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = launchLogo1.png; sourceTree = "<group>"; };
		95A1C65126FC63A100D6A14B /* XMLDeviceManager+Growth.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMLDeviceManager+Growth.swift"; sourceTree = "<group>"; };
		95B24E13250B645900D50A34 /* XMLMediaLogicManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLMediaLogicManager.swift; sourceTree = "<group>"; };
		95B44F4324485A63000E442F /* XMModuleManager+Ad.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Ad.swift"; sourceTree = "<group>"; };
		95BF94B8255255ED00F2B197 /* AppDelegate+Wizard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+Wizard.swift"; sourceTree = "<group>"; };
		95C11B382512004F000F31D7 /* XMLAppContext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLAppContext.swift; sourceTree = "<group>"; };
		95C4800826D77EBE00F8095C /* xmlMain.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = xmlMain.ttf; sourceTree = "<group>"; };
		95CB5306274283CE00C25C4C /* iAd.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = iAd.framework; path = System/Library/Frameworks/iAd.framework; sourceTree = SDKROOT; };
		95CB5308274283D700C25C4C /* AdServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdServices.framework; path = System/Library/Frameworks/AdServices.framework; sourceTree = SDKROOT; };
		95D25D3126CF942100379278 /* ImmerseModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ImmerseModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95D4009F248F24850093F596 /* CommBusiness.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = CommBusiness.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95DB9F9D238E768500BDE6B2 /* XMLTabBarController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLTabBarController.swift; sourceTree = "<group>"; };
		95DB9F9E238E768500BDE6B2 /* XMLTabBarCustomItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLTabBarCustomItem.swift; sourceTree = "<group>"; };
		95DB9F9F238E768500BDE6B2 /* XMLTabBarButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLTabBarButton.swift; sourceTree = "<group>"; };
		95EA9BA4254BC107007F3297 /* AppDelegate+FingerPrint.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+FingerPrint.swift"; sourceTree = "<group>"; };
		95EB615D244AFC2100F2E750 /* ApplicationConst.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ApplicationConst.swift; sourceTree = "<group>"; };
		95EBC2C62750E2580089C909 /* praise.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = praise.mp3; sourceTree = "<group>"; };
		95EE411625D13DFE00010E51 /* XMLListeningTipContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLListeningTipContainer.swift; sourceTree = "<group>"; };
		95F051E024ECC5340051E7DD /* XMICFontMain.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMICFontMain.swift; sourceTree = "<group>"; };
		95FE7C1B2746231200613F67 /* XMModuleManager+Share.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Share.swift"; sourceTree = "<group>"; };
		A8F9AA8F0CEE14DE1EDA766C /* Pods-tingLite.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-tingLite.release.xcconfig"; path = "Target Support Files/Pods-tingLite/Pods-tingLite.release.xcconfig"; sourceTree = "<group>"; };
		B06E0D0222FA9C8600F4B93D /* RouterModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = RouterModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0932D70230C046D0046A6FD /* tingLite.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = tingLite.entitlements; sourceTree = "<group>"; };
		B0932D71230C046D0046A6FE /* tingLite-Release.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "tingLite-Release.entitlements"; sourceTree = "<group>"; };
		B0B014CF2304157000EC7B5C /* MediaModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MediaModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6035422F99148004CE92D /* AudioModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = AudioModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6035622F9914E004CE92D /* BaseModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = BaseModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6035822F99151004CE92D /* HomeModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = HomeModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6035A22F99155004CE92D /* MineModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MineModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6035C22F99160004CE92D /* MyListenModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MyListenModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6035E22F99166004CE92D /* SearchModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = SearchModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6036222F99174004CE92D /* XMConfigModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMConfigModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6036422F9917D004CE92D /* XMNetworkModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMNetworkModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6036622F99184004CE92D /* XMWebModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMWebModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0C110A0234C803E00DE3459 /* XMUtilModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMUtilModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0F3058B23176C02006C4356 /* NotificationCenter.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = NotificationCenter.framework; path = System/Library/Frameworks/NotificationCenter.framework; sourceTree = SDKROOT; };
		B6072D5226D6162500E55C8A /* XMLTabBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLTabBar.swift; sourceTree = "<group>"; };
		B6404288245822740027D6E1 /* VIPModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = VIPModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B66A9409259475360027C34D /* XMAlertModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMAlertModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B66ADC8E242DA19100BF4970 /* AppDelegate+XMNet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+XMNet.swift"; sourceTree = "<group>"; };
		B66ADC92242DE07F00BF4970 /* AppDelegate+Notification.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+Notification.swift"; sourceTree = "<group>"; };
		B66ADC96242DE74400BF4970 /* AppDelegate+UBT.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+UBT.swift"; sourceTree = "<group>"; };
		B66ADC98242DE8E700BF4970 /* AppDelegate+XMPay.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+XMPay.swift"; sourceTree = "<group>"; };
		B66ADC9A242DF12E00BF4970 /* AppDelegate+PerformURL.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+PerformURL.swift"; sourceTree = "<group>"; };
		B66C0BA22739471300F05AF8 /* XMBookModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMBookModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B677D7242446D6AD0060FEDE /* XMModuleManager+GobalTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+GobalTask.swift"; sourceTree = "<group>"; };
		B681F3F9248796580056E41D /* morentouxiang.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = morentouxiang.png; sourceTree = "<group>"; };
		B681F3FC24879B410056E41D /* vipModule.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = vipModule.plist; sourceTree = "<group>"; };
		B682AA6D2477756D0067BD47 /* XMModuleManager+Home.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMModuleManager+Home.swift"; sourceTree = "<group>"; };
		B682AA722477A8480067BD47 /* VipModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = VipModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B68AE0BD231CDE6A00422C5A /* AppDelegate+Cipher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+Cipher.swift"; sourceTree = "<group>"; };
		B68FDDA9276B598100333F3C /* XMCommentModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMCommentModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6987177230D2FAA00220228 /* XMHistoryModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMHistoryModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6A264F924F75F0200DADBCD /* globalCoinAlertDoubleAni.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = globalCoinAlertDoubleAni.json; sourceTree = "<group>"; };
		B6B2EE5E24583C630088443B /* VIPModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = VIPModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6C7ABB5255CE0DF00F50F3D /* XMShortVideoModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMShortVideoModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6E21F9524580EE600E08A88 /* VIPModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = VIPModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6E5690C29C33C2300695C6B /* libswiftCoreGraphics.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libswiftCoreGraphics.tbd; path = usr/lib/swift/libswiftCoreGraphics.tbd; sourceTree = SDKROOT; };
		BB1EE54B26FC62E200749C03 /* XMLiveModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMLiveModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BBD2BFB9270326F400A6BF7F /* XimaZhiboti-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "XimaZhiboti-Regular.ttf"; sourceTree = "<group>"; };
		C18EF409E2CE22DD6A7262DD /* Pods_tingLite.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_tingLite.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D0C2664D07714439E97CF3AA /* Pods-tingLiteAPNSerEx.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-tingLiteAPNSerEx.alpha.xcconfig"; path = "Target Support Files/Pods-tingLiteAPNSerEx/Pods-tingLiteAPNSerEx.alpha.xcconfig"; sourceTree = "<group>"; };
		EB3AB33559A68814B2B6AD3C /* Pods-tingLiteAPNSerEx.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-tingLiteAPNSerEx.release.xcconfig"; path = "Target Support Files/Pods-tingLiteAPNSerEx/Pods-tingLiteAPNSerEx.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8817FB1922D8464A00DC0084 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B6E5690D29C33C2300695C6B /* libswiftCoreGraphics.tbd in Frameworks */,
				9580802427DC633D004AFE14 /* UMDevice.framework in Frameworks */,
				95CB530A274283DC00C25C4C /* AdSupport.framework in Frameworks */,
				95CB5309274283D700C25C4C /* AdServices.framework in Frameworks */,
				95CB5307274283CF00C25C4C /* iAd.framework in Frameworks */,
				95FFCC4E2671B1BB001FCF2E /* SystemConfiguration.framework in Frameworks */,
				95FFCC4B2671B198001FCF2E /* CoreTelephony.framework in Frameworks */,
				95FFCC4D2671B1B2001FCF2E /* libsqlite3.tbd in Frameworks */,
				95FFCC4C2671B1A2001FCF2E /* libz.tbd in Frameworks */,
				B0B6036522F9917D004CE92D /* XMNetworkModule.framework in Frameworks */,
				B0C110A1234C803E00DE3459 /* XMUtilModule.framework in Frameworks */,
				B06E0D0322FA9C8600F4B93D /* RouterModule.framework in Frameworks */,
				B0B6036322F99174004CE92D /* XMConfigModule.framework in Frameworks */,
				955C01F824206026003C3645 /* StoreKit.framework in Frameworks */,
				B6987178230D2FAA00220228 /* XMHistoryModule.framework in Frameworks */,
				B0B6035722F9914E004CE92D /* BaseModule.framework in Frameworks */,
				9530A2E52398D9B300C275B4 /* LoginModule.framework in Frameworks */,
				9573334C2484EE6A00F105C0 /* XMADXModule.framework in Frameworks */,
				9580802327DC633D004AFE14 /* UMAPM.framework in Frameworks */,
				9580802127DC633D004AFE14 /* UMRemoteConfig.framework in Frameworks */,
				220BFE5E2746414300A52052 /* XMReaderModule.framework in Frameworks */,
				95D400A0248F24860093F596 /* CommBusiness.framework in Frameworks */,
				B0B6036722F99184004CE92D /* XMWebModule.framework in Frameworks */,
				255CAC2523059A170023A705 /* ShareModule.framework in Frameworks */,
				9580802227DC633D004AFE14 /* UMCommon.framework in Frameworks */,
				B0B014D02304157000EC7B5C /* MediaModule.framework in Frameworks */,
				BB1EE54C26FC62E200749C03 /* XMLiveModule.framework in Frameworks */,
				B0B6035522F99148004CE92D /* AudioModule.framework in Frameworks */,
				B0B6035D22F99160004CE92D /* MyListenModule.framework in Frameworks */,
				B66A940A259475360027C34D /* XMAlertModule.framework in Frameworks */,
				B682AA732477A8480067BD47 /* VipModule.framework in Frameworks */,
				B0B6035F22F99166004CE92D /* SearchModule.framework in Frameworks */,
				B6454B732790231400552757 /* XMShortVideoModule.framework in Frameworks */,
				B0B6035B22F99155004CE92D /* MineModule.framework in Frameworks */,
				B68FDDAA276B598100333F3C /* XMCommentModule.framework in Frameworks */,
				B66C0BA32739471300F05AF8 /* XMBookModule.framework in Frameworks */,
				956E093427450C5F00690062 /* ImmerseModule.framework in Frameworks */,
				B0B6035922F99151004CE92D /* HomeModule.framework in Frameworks */,
				27B959D209C224E66BC68706 /* Pods_tingLite.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		956FCD19268AEAEB0098B44B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				046B6F6E272F82E70064AD03 /* Pods_tingLiteAPNSerEx.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		22675BC8277EDAE900B903BC /* playing */ = {
			isa = PBXGroup;
			children = (
				22675BCB277EDAE900B903BC /* playing_1.png */,
				22675BC9277EDAE900B903BC /* playing_2.png */,
				22675BCA277EDAE900B903BC /* playing_3.png */,
				22675BCC277EDAE900B903BC /* playing_4.png */,
				22675BCD277EDAE900B903BC /* playing_5.png */,
				22675BCE277EDAE900B903BC /* playing_7.png */,
				22675BCF277EDAE900B903BC /* playing_6.png */,
				22675BD0277EDAE900B903BC /* playing_8.png */,
				22675BD1277EDAE900B903BC /* playing_9.png */,
			);
			path = playing;
			sourceTree = "<group>";
		};
		229C9E4E27184ED60043895D /* sound */ = {
			isa = PBXGroup;
			children = (
				95EBC2C62750E2580089C909 /* praise.mp3 */,
				229C9E4F27184EEB0043895D /* switchToCarfriend.mp3 */,
				229C9E5027184EEB0043895D /* switchToClassic.mp3 */,
			);
			path = sound;
			sourceTree = "<group>";
		};
		250A85F5233E0A280067D143 /* ThirdParty */ = {
			isa = PBXGroup;
			children = (
				9580801C27DC633D004AFE14 /* UM_7.3.0 */,
			);
			path = ThirdParty;
			sourceTree = "<group>";
		};
		2FBFA53039052B48945DBFCA /* Pods */ = {
			isa = PBXGroup;
			children = (
				3D8866C30413C14294B0BB3C /* Pods-tingLite.debug.xcconfig */,
				A8F9AA8F0CEE14DE1EDA766C /* Pods-tingLite.release.xcconfig */,
				0FBCACA00E943B7F12B7BC0B /* Pods-tingLite.alpha.xcconfig */,
				777C9FB67350629A1D4269ED /* Pods-tingLiteAPNSerEx.debug.xcconfig */,
				EB3AB33559A68814B2B6AD3C /* Pods-tingLiteAPNSerEx.release.xcconfig */,
				D0C2664D07714439E97CF3AA /* Pods-tingLiteAPNSerEx.alpha.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		6578C7311CC990753098A5DC /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B6E5690C29C33C2300695C6B /* libswiftCoreGraphics.tbd */,
				B68FDDA9276B598100333F3C /* XMCommentModule.framework */,
				220BFE5A2746412200A52052 /* Pods_XMReaderModule.framework */,
				6469352C2738D3B0004EED3B /* XMReaderModule.framework */,
				956E093327450C5F00690062 /* ImmerseModule.framework */,
				95CB5308274283D700C25C4C /* AdServices.framework */,
				95CB5306274283CE00C25C4C /* iAd.framework */,
				B66C0BA22739471300F05AF8 /* XMBookModule.framework */,
				BB1EE54B26FC62E200749C03 /* XMLiveModule.framework */,
				95D25D3126CF942100379278 /* ImmerseModule.framework */,
				954344BC26CF86E7004DA6BD /* ImmerseModule.framework */,
				B66A9409259475360027C34D /* XMAlertModule.framework */,
				B6C7ABB5255CE0DF00F50F3D /* XMShortVideoModule.framework */,
				95D4009F248F24850093F596 /* CommBusiness.framework */,
				9573334F2484EED600F105C0 /* Foundation.framework */,
				9573334D2484EED100F105C0 /* UIKit.framework */,
				9573334B2484EE6A00F105C0 /* XMADXModule.framework */,
				B682AA722477A8480067BD47 /* VipModule.framework */,
				B6B2EE5E24583C630088443B /* VIPModule.framework */,
				B6404288245822740027D6E1 /* VIPModule.framework */,
				B6E21F9524580EE600E08A88 /* VIPModule.framework */,
				95472F1423C5F44C00C826F9 /* ImageIO.framework */,
				95472F1223C5F44200C826F9 /* StoreKit.framework */,
				95472F1023C5F43900C826F9 /* MobileCoreServices.framework */,
				95472F0E23C5F43200C826F9 /* WebKit.framework */,
				95472F0C23C5F42A00C826F9 /* MediaPlayer.framework */,
				95472F0A23C5F42300C826F9 /* CoreMedia.framework */,
				95472F0823C5F41700C826F9 /* CoreLocation.framework */,
				95472F0623C5F40800C826F9 /* AVFoundation.framework */,
				95472F0423C5F3FF00C826F9 /* CoreTelephony.framework */,
				95472F0223C5F3F800C826F9 /* SystemConfiguration.framework */,
				95472F0023C5F3F000C826F9 /* AdSupport.framework */,
				95472EFE23C5F3E800C826F9 /* CoreMotion.framework */,
				95472EFC23C5F3DF00C826F9 /* Accelerate.framework */,
				95472EFA23C5F3D600C826F9 /* libresolv.9.tbd */,
				95472EF823C5F3CB00C826F9 /* libc++.tbd */,
				95472EF623C5F3BF00C826F9 /* libz.tbd */,
				95472EF423C5F3B400C826F9 /* libsqlite3.tbd */,
				B0C110A0234C803E00DE3459 /* XMUtilModule.framework */,
				3D55D4932302921B0041747F /* RNKitModule.framework */,
				B6987177230D2FAA00220228 /* XMHistoryModule.framework */,
				255CAC2423059A170023A705 /* ShareModule.framework */,
				B0B014CF2304157000EC7B5C /* MediaModule.framework */,
				88417FCF2301657800B12190 /* LoginModule.framework */,
				B06E0D0222FA9C8600F4B93D /* RouterModule.framework */,
				B0B6036622F99184004CE92D /* XMWebModule.framework */,
				B0B6036422F9917D004CE92D /* XMNetworkModule.framework */,
				B0B6036222F99174004CE92D /* XMConfigModule.framework */,
				B0B6035E22F99166004CE92D /* SearchModule.framework */,
				B0B6035C22F99160004CE92D /* MyListenModule.framework */,
				B0B6035A22F99155004CE92D /* MineModule.framework */,
				B0B6035822F99151004CE92D /* HomeModule.framework */,
				B0B6035622F9914E004CE92D /* BaseModule.framework */,
				B0B6035422F99148004CE92D /* AudioModule.framework */,
				888EEC8722EC6E020081DE63 /* BaseModule.framework */,
				88FA6E6422E34A6E00842042 /* SwiftUI.framework */,
				B0F3058B23176C02006C4356 /* NotificationCenter.framework */,
				C18EF409E2CE22DD6A7262DD /* Pods_tingLite.framework */,
				6D2D796A99D8D4EDDB6F7115 /* Pods_tingLiteAPNSerEx.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		8804FB7C2324F7EE007BEB61 /* XMModuleManager+Bridge */ = {
			isa = PBXGroup;
			children = (
				88CAD8102318CDC60058EAE5 /* XMModuleManager+Bridge.swift */,
				8804FB7F2324F861007BEB61 /* XMModuleManager+Base.swift */,
				8804FB7D2324F80F007BEB61 /* XMModuleManager+Login.swift */,
				8804FB812324F919007BEB61 /* XMModuleManager+Audio.swift */,
				8804FB832324F989007BEB61 /* XMModuleManager+Listen.swift */,
				8804FB872324FA35007BEB61 /* XMModuleManager+Network.swift */,
				95B44F4324485A63000E442F /* XMModuleManager+Ad.swift */,
				85D19EAF24400CEF0018B1E7 /* XMModuleManager+SceneRadio.swift */,
				B677D7242446D6AD0060FEDE /* XMModuleManager+GobalTask.swift */,
				B682AA6D2477756D0067BD47 /* XMModuleManager+Home.swift */,
				954344B426CF5214004DA6BD /* XMModuleManager+Immerse.swift */,
				95FE7C1B2746231200613F67 /* XMModuleManager+Share.swift */,
				04B99C44279F9BBC00543EF3 /* XMModuleManager+Book.swift */,
			);
			path = "XMModuleManager+Bridge";
			sourceTree = "<group>";
		};
		8817FB1322D8464A00DC0084 = {
			isa = PBXGroup;
			children = (
				8817FB1E22D8464A00DC0084 /* tingLite */,
				956FCD1D268AEAEB0098B44B /* tingLiteAPNSerEx */,
				8817FB1D22D8464A00DC0084 /* Products */,
				2FBFA53039052B48945DBFCA /* Pods */,
				6578C7311CC990753098A5DC /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		8817FB1D22D8464A00DC0084 /* Products */ = {
			isa = PBXGroup;
			children = (
				8817FB1C22D8464A00DC0084 /* tingLite.app */,
				956FCD1C268AEAEB0098B44B /* tingLiteAPNSerEx.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8817FB1E22D8464A00DC0084 /* tingLite */ = {
			isa = PBXGroup;
			children = (
				8817FB1F22D8464A00DC0084 /* AppDelegate.swift */,
				B68AE0BD231CDE6A00422C5A /* AppDelegate+Cipher.swift */,
				B66ADC8E242DA19100BF4970 /* AppDelegate+XMNet.swift */,
				B66ADC92242DE07F00BF4970 /* AppDelegate+Notification.swift */,
				B66ADC96242DE74400BF4970 /* AppDelegate+UBT.swift */,
				B66ADC98242DE8E700BF4970 /* AppDelegate+XMPay.swift */,
				B66ADC9A242DF12E00BF4970 /* AppDelegate+PerformURL.swift */,
				0F73ABBE2466819F00946106 /* AppDelegate+APM.swift */,
				95EA9BA4254BC107007F3297 /* AppDelegate+FingerPrint.swift */,
				95BF94B8255255ED00F2B197 /* AppDelegate+Wizard.swift */,
				95EB615D244AFC2100F2E750 /* ApplicationConst.swift */,
				8817FC2622D898DF00DC0084 /* XMModuleManager.swift */,
				8804FB7C2324F7EE007BEB61 /* XMModuleManager+Bridge */,
				953E107623D6FE4A00210C55 /* Class */,
				250A85F5233E0A280067D143 /* ThirdParty */,
				B681F3F5248796580056E41D /* Resources */,
				8817FB2822D8464A00DC0084 /* Assets.xcassets */,
				8817FB2A22D8464A00DC0084 /* LaunchScreen.storyboard */,
				95812BE624AC8CBD007EAAE7 /* launchLogo1.png */,
				8817FB2D22D8464A00DC0084 /* Info.plist */,
				9507943824920B8E00E054D0 /* GoogleService-Info.plist */,
				B0932D70230C046D0046A6FD /* tingLite.entitlements */,
				B0932D71230C046D0046A6FE /* tingLite-Release.entitlements */,
				88B63BF222DC5F97004A700F /* tingLite-Bridging-Header.h */,
				033E51A82D2D09BD0041BBEF /* xm_injection_config.sh */,
			);
			path = tingLite;
			sourceTree = "<group>";
		};
		953E107523D6FE0100210C55 /* TabBar */ = {
			isa = PBXGroup;
			children = (
				95DB9F9D238E768500BDE6B2 /* XMLTabBarController.swift */,
				95DB9F9E238E768500BDE6B2 /* XMLTabBarCustomItem.swift */,
				95DB9F9F238E768500BDE6B2 /* XMLTabBarButton.swift */,
				B6072D5226D6162500E55C8A /* XMLTabBar.swift */,
			);
			path = TabBar;
			sourceTree = "<group>";
		};
		953E107623D6FE4A00210C55 /* Class */ = {
			isa = PBXGroup;
			children = (
				95C11B382512004F000F31D7 /* XMLAppContext.swift */,
				95F051E024ECC5340051E7DD /* XMICFontMain.swift */,
				95B24E13250B645900D50A34 /* XMLMediaLogicManager.swift */,
				9537A58A23B0AA92003B7C26 /* XMLDeviceManager.swift */,
				95A1C65126FC63A100D6A14B /* XMLDeviceManager+Growth.swift */,
				95EE411625D13DFE00010E51 /* XMLListeningTipContainer.swift */,
				953E107523D6FE0100210C55 /* TabBar */,
			);
			path = Class;
			sourceTree = "<group>";
		};
		954AF2E72536E85A0013939F /* webp */ = {
			isa = PBXGroup;
			children = (
				954AF2E82536E8E20013939F /* <EMAIL> */,
				954AF2F22537E1E70013939F /* <EMAIL> */,
				954AF2F42537E22F0013939F /* <EMAIL> */,
				954AF2F82537E2A30013939F /* <EMAIL> */,
				954AF2FC2537E4B70013939F /* <EMAIL> */,
				954AF3022537E63B0013939F /* <EMAIL> */,
			);
			path = webp;
			sourceTree = "<group>";
		};
		956FCD1D268AEAEB0098B44B /* tingLiteAPNSerEx */ = {
			isa = PBXGroup;
			children = (
				956FCD28268B1E470098B44B /* NotificationService.swift */,
				956FCD20268AEAEB0098B44B /* Info.plist */,
			);
			path = tingLiteAPNSerEx;
			sourceTree = "<group>";
		};
		9580801C27DC633D004AFE14 /* UM_7.3.0 */ = {
			isa = PBXGroup;
			children = (
				9580801D27DC633D004AFE14 /* UMRemoteConfig.framework */,
				9580801E27DC633D004AFE14 /* UMCommon.framework */,
				9580801F27DC633D004AFE14 /* UMAPM.framework */,
				9580802027DC633D004AFE14 /* UMDevice.framework */,
			);
			path = UM_7.3.0;
			sourceTree = "<group>";
		};
		95F051DD24ECC33C0051E7DD /* font */ = {
			isa = PBXGroup;
			children = (
				BBD2BFB9270326F400A6BF7F /* XimaZhiboti-Regular.ttf */,
				95C4800826D77EBE00F8095C /* xmlMain.ttf */,
			);
			path = font;
			sourceTree = "<group>";
		};
		B681F3F5248796580056E41D /* Resources */ = {
			isa = PBXGroup;
			children = (
				9514EFCA26CBC428006BA316 /* xmubtPrepared.json */,
				22675BC8277EDAE900B903BC /* playing */,
				229C9E4E27184ED60043895D /* sound */,
				95F051DD24ECC33C0051E7DD /* font */,
				B681F3F6248796580056E41D /* eventStatistic */,
				B681F3F8248796580056E41D /* image */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		B681F3F6248796580056E41D /* eventStatistic */ = {
			isa = PBXGroup;
			children = (
				B681F3FC24879B410056E41D /* vipModule.plist */,
			);
			path = eventStatistic;
			sourceTree = "<group>";
		};
		B681F3F8248796580056E41D /* image */ = {
			isa = PBXGroup;
			children = (
				954AF2E72536E85A0013939F /* webp */,
				B6A264F924F75F0200DADBCD /* globalCoinAlertDoubleAni.json */,
				B681F3F9248796580056E41D /* morentouxiang.png */,
				950C2BC225527421004E43F4 /* catguardiana.png */,
				950C2BC72552816A004E43F4 /* catguardianb.png */,
			);
			path = image;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8817FB1B22D8464A00DC0084 /* tingLite */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8817FB3022D8464A00DC0084 /* Build configuration list for PBXNativeTarget "tingLite" */;
			buildPhases = (
				AFC829A79AC5348BFADC0B45 /* [CP] Check Pods Manifest.lock */,
				CDBDBC8B2E09511700F4C81C /* Strip CryptoSwift Bitcode */,
				8817FB1822D8464A00DC0084 /* Sources */,
				8817FB1922D8464A00DC0084 /* Frameworks */,
				8817FB1A22D8464A00DC0084 /* Resources */,
				B0F3059B23176C03006C4356 /* Embed App Extensions */,
				B674B80D250115F500ABD651 /* Copy Cache Framework Resources */,
				292D075CF53D4C07126A1119 /* [CP] Embed Pods Frameworks */,
				E0866742F391C8F944C06F40 /* [CP] Copy Pods Resources */,
				952F4BFF23BD889E0065CF25 /* ShellScript */,
				B645342D250B1BD5001C6203 /* [debug] bugly Symbol upload */,
				959468F326CBC19500CCECD6 /* Run Script */,
				B6AAB6132AD28BAF0046458A /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				956FCD22268AEAEB0098B44B /* PBXTargetDependency */,
			);
			name = tingLite;
			productName = tingLite;
			productReference = 8817FB1C22D8464A00DC0084 /* tingLite.app */;
			productType = "com.apple.product-type.application";
		};
		956FCD1B268AEAEB0098B44B /* tingLiteAPNSerEx */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 956FCD27268AEAEC0098B44B /* Build configuration list for PBXNativeTarget "tingLiteAPNSerEx" */;
			buildPhases = (
				E3A813A34855290A03D63E60 /* [CP] Check Pods Manifest.lock */,
				956FCD18268AEAEB0098B44B /* Sources */,
				956FCD19268AEAEB0098B44B /* Frameworks */,
				956FCD1A268AEAEB0098B44B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = tingLiteAPNSerEx;
			productName = tingLiteAPNSerEx;
			productReference = 956FCD1C268AEAEB0098B44B /* tingLiteAPNSerEx.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8817FB1422D8464A00DC0084 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				DefaultBuildSystemTypeForWorkspace = Original;
				LastSwiftUpdateCheck = 1210;
				LastUpgradeCheck = 1100;
				ORGANIZATIONNAME = ximalaya;
				TargetAttributes = {
					8817FB1B22D8464A00DC0084 = {
						CreatedOnToolsVersion = 11.0;
						LastSwiftMigration = 1100;
					};
					956FCD1B268AEAEB0098B44B = {
						CreatedOnToolsVersion = 12.1;
					};
				};
			};
			buildConfigurationList = 8817FB1722D8464A00DC0084 /* Build configuration list for PBXProject "tingLite" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 8817FB1322D8464A00DC0084;
			productRefGroup = 8817FB1D22D8464A00DC0084 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8817FB1B22D8464A00DC0084 /* tingLite */,
				956FCD1B268AEAEB0098B44B /* tingLiteAPNSerEx */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8817FB1A22D8464A00DC0084 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BBD2BFBA270330F000A6BF7F /* XimaZhiboti-Regular.ttf in Resources */,
				954AF2F92537E2A30013939F /* <EMAIL> in Resources */,
				22675BDA277EDAE900B903BC /* playing_9.png in Resources */,
				22675BD6277EDAE900B903BC /* playing_5.png in Resources */,
				229C9E5227184EEC0043895D /* switchToClassic.mp3 in Resources */,
				9514EFCB26CBC428006BA316 /* xmubtPrepared.json in Resources */,
				22675BD9277EDAE900B903BC /* playing_8.png in Resources */,
				B68109D3273B728C00A43E10 /* Assets.xcassets in Resources */,
				95C4800926D77EBE00F8095C /* xmlMain.ttf in Resources */,
				950C2BC325527421004E43F4 /* catguardiana.png in Resources */,
				8817FB2C22D8464A00DC0084 /* LaunchScreen.storyboard in Resources */,
				229C9E5127184EEC0043895D /* switchToCarfriend.mp3 in Resources */,
				B681F3FD24879B410056E41D /* vipModule.plist in Resources */,
				95EBC2C72750E2580089C909 /* praise.mp3 in Resources */,
				22675BD8277EDAE900B903BC /* playing_6.png in Resources */,
				B681F3FB248796590056E41D /* morentouxiang.png in Resources */,
				950C2BC82552816A004E43F4 /* catguardianb.png in Resources */,
				22675BD3277EDAE900B903BC /* playing_3.png in Resources */,
				954AF2FD2537E4B70013939F /* <EMAIL> in Resources */,
				954AF2F52537E22F0013939F /* <EMAIL> in Resources */,
				22675BD5277EDAE900B903BC /* playing_4.png in Resources */,
				95812BE724AC8CBD007EAAE7 /* launchLogo1.png in Resources */,
				B6A264FA24F75F0200DADBCD /* globalCoinAlertDoubleAni.json in Resources */,
				954AF3032537E63B0013939F /* <EMAIL> in Resources */,
				954AF2F32537E1E70013939F /* <EMAIL> in Resources */,
				22675BD2277EDAE900B903BC /* playing_2.png in Resources */,
				9507943924920B8F00E054D0 /* GoogleService-Info.plist in Resources */,
				954AF2E92536E8E20013939F /* <EMAIL> in Resources */,
				22675BD7277EDAE900B903BC /* playing_7.png in Resources */,
				22675BD4277EDAE900B903BC /* playing_1.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		956FCD1A268AEAEB0098B44B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		292D075CF53D4C07126A1119 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-tingLite/Pods-tingLite-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-tingLite/Pods-tingLite-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-tingLite/Pods-tingLite-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		952F4BFF23BD889E0065CF25 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/XMFirebase/XMFirebase/FirebaseCrashlytics/run\"\n";
		};
		959468F326CBC19500CCECD6 /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\nKEYWORDS=\"TODO:|FIXME:|\\?\\?\\?:|\\!\\!\\!:\"\nfind \"${SRCROOT}\" \\( -name \"*.h\" -or -name \"*.m\" -or -name \"*.swift\" \\) -print0 | xargs -0 egrep --with-filename --line-number --only-matching \"($KEYWORDS).*\\$\" | perl -p -e \"s/($KEYWORDS)/ warning: \\$1/\"\n";
		};
		AFC829A79AC5348BFADC0B45 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-tingLite-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B645342D250B1BD5001C6203 /* [debug] bugly Symbol upload */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[debug] bugly Symbol upload";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n# debug 模式下才使用bugly 上报(方便) release模式使用firebase\nif [[ \"$CONFIGURATION\" == \"Debug\" ]]; then\n    # 上传时间很长，影响打包，先去掉，用到的时候再手动上传\n    #sh $SRCROOT/increment_build/dSYMUpload.sh\n    echo \"DEBUG 不上传符号表\"\nfi\n";
		};
		B674B80D250115F500ABD651 /* Copy Cache Framework Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Copy Cache Framework Resources";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\nappPath=${BUILT_PRODUCTS_DIR}\"/\"${TARGET_NAME}\".app\"\npython3 ./increment_build/install_resource.py -buildPath \"${BUILT_PRODUCTS_DIR}\" -destPath \"${appPath}\"\n";
		};
		B6AAB6132AD28BAF0046458A /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 8;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 1;
			shellPath = /bin/sh;
			shellScript = "buildNumber=$APP_BUILD\necho \"update build number to $buildNumber\"\nxcrun agvtool new-version -all $buildNumber\n";
		};
		CDBDBC8B2E09511700F4C81C /* Strip CryptoSwift Bitcode */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Strip CryptoSwift Bitcode";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/bash\n# 移除CryptoSwift中的Bitcode\nFRAMEWORK=\"${SRCROOT}/Pods/XM3rdParty/XM3rdParty/Products/iOS/CryptoSwift.framework/CryptoSwift\"\n\nif [ -f \"$FRAMEWORK\" ] && xcrun otool -l \"$FRAMEWORK\" | grep -q \"__LLVM\"; then\n    echo \"🔧 移除CryptoSwift中的Bitcode...\"\n    xcrun bitcode_strip -r \"$FRAMEWORK\" -o \"$FRAMEWORK\"\n    echo \"✅ 完成\"\nfi\n";
		};
		E0866742F391C8F944C06F40 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-tingLite/Pods-tingLite-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-tingLite/Pods-tingLite-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-tingLite/Pods-tingLite-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E3A813A34855290A03D63E60 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-tingLiteAPNSerEx-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8817FB1822D8464A00DC0084 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95C11B392512004F000F31D7 /* XMLAppContext.swift in Sources */,
				B66ADC97242DE74400BF4970 /* AppDelegate+UBT.swift in Sources */,
				8804FB7E2324F80F007BEB61 /* XMModuleManager+Login.swift in Sources */,
				95B24E14250B645900D50A34 /* XMLMediaLogicManager.swift in Sources */,
				9537A58B23B0AA92003B7C26 /* XMLDeviceManager.swift in Sources */,
				8804FB842324F989007BEB61 /* XMModuleManager+Listen.swift in Sources */,
				B66ADC8F242DA19100BF4970 /* AppDelegate+XMNet.swift in Sources */,
				95FE7C1C2746231200613F67 /* XMModuleManager+Share.swift in Sources */,
				95A1C65226FC63A100D6A14B /* XMLDeviceManager+Growth.swift in Sources */,
				95EE411725D13DFE00010E51 /* XMLListeningTipContainer.swift in Sources */,
				B677D7252446D6AD0060FEDE /* XMModuleManager+GobalTask.swift in Sources */,
				95EA9BA5254BC107007F3297 /* AppDelegate+FingerPrint.swift in Sources */,
				8804FB822324F919007BEB61 /* XMModuleManager+Audio.swift in Sources */,
				95DB9FA0238E768600BDE6B2 /* XMLTabBarController.swift in Sources */,
				95BF94B9255255ED00F2B197 /* AppDelegate+Wizard.swift in Sources */,
				B682AA6E2477756D0067BD47 /* XMModuleManager+Home.swift in Sources */,
				95F051E124ECC5340051E7DD /* XMICFontMain.swift in Sources */,
				B66ADC93242DE07F00BF4970 /* AppDelegate+Notification.swift in Sources */,
				0F73ABBF2466819F00946106 /* AppDelegate+APM.swift in Sources */,
				88CAD8112318CDC60058EAE5 /* XMModuleManager+Bridge.swift in Sources */,
				B68AE0BE231CDE6A00422C5A /* AppDelegate+Cipher.swift in Sources */,
				B66ADC99242DE8E700BF4970 /* AppDelegate+XMPay.swift in Sources */,
				8817FB2022D8464A00DC0084 /* AppDelegate.swift in Sources */,
				95EB615E244AFC2100F2E750 /* ApplicationConst.swift in Sources */,
				95DB9FA2238E768600BDE6B2 /* XMLTabBarButton.swift in Sources */,
				954344B526CF5214004DA6BD /* XMModuleManager+Immerse.swift in Sources */,
				8817FC2722D898DF00DC0084 /* XMModuleManager.swift in Sources */,
				8804FB882324FA35007BEB61 /* XMModuleManager+Network.swift in Sources */,
				8804FB802324F861007BEB61 /* XMModuleManager+Base.swift in Sources */,
				85D19EB024400CEF0018B1E7 /* XMModuleManager+SceneRadio.swift in Sources */,
				04B99C45279F9BBC00543EF3 /* XMModuleManager+Book.swift in Sources */,
				B6072D5326D6162600E55C8A /* XMLTabBar.swift in Sources */,
				B66ADC9B242DF12E00BF4970 /* AppDelegate+PerformURL.swift in Sources */,
				95DB9FA1238E768600BDE6B2 /* XMLTabBarCustomItem.swift in Sources */,
				95B44F4424485A63000E442F /* XMModuleManager+Ad.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		956FCD18268AEAEB0098B44B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				956FCD29268B1E470098B44B /* NotificationService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		956FCD22268AEAEB0098B44B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 956FCD1B268AEAEB0098B44B /* tingLiteAPNSerEx */;
			targetProxy = 956FCD21268AEAEB0098B44B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		8817FB2A22D8464A00DC0084 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8817FB2B22D8464A00DC0084 /* Base */,
				95472F1723C5F49500C826F9 /* zh-Hans */,
				954AF2E62536DD860013939F /* en */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		8817FB2E22D8464A00DC0084 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APP_BUILD = 250321;
				APP_VER = 3.0.37;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer: xie xiao (2RJT426KK4)";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 250321;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.2;
				MARKETING_VERSION = "$(APP_VER)";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		8817FB2F22D8464A00DC0084 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APP_BUILD = 250321;
				APP_VER = 3.0.37;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Xi Da (Shanghai) Network Technology Co., Ltd. (AS4ANJJUVM)";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 250321;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.2;
				MARKETING_VERSION = "$(APP_VER)";
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		8817FB3122D8464A00DC0084 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3D8866C30413C14294B0BB3C /* Pods-tingLite.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				APP_BUILD = "${APP_BUILD}";
				APP_CHANNEL = "ios-b1";
				APP_VER = "${APP_VER}";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = tingLite/tingLite.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 250321;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = J32AD5KDR3;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/tingLite/ThirdParty",
					"$(PROJECT_DIR)/tingLite/ThirdParty/UM_7.3.0",
					"\"$(SRCROOT)/Products\"",
				);
				INFOPLIST_FILE = tingLite/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(SDKROOT)/usr/lib/swift",
				);
				MARKETING_VERSION = "$(APP_VER)";
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-fprofile-instr-generate",
					"-weak_framework",
					Combine,
					"-weak_framework",
					SwiftUI,
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.tingLite;
				PRODUCT_NAME = tingLite;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "tingLite/tingLite-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TVOS_DEPLOYMENT_TARGET = 10.0;
				VALID_ARCHS = "armv7 armv7s arm64 x86_64";
			};
			name = Debug;
		};
		8817FB3222D8464A00DC0084 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A8F9AA8F0CEE14DE1EDA766C /* Pods-tingLite.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				APP_BUILD = "${APP_BUILD}";
				APP_CHANNEL = "ios-b1";
				APP_VER = "${APP_VER}";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "tingLite/tingLite-Release.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 250321;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = J32AD5KDR3;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = J32AD5KDR3;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/tingLite/ThirdParty",
					"$(PROJECT_DIR)/tingLite/ThirdParty/UM_7.3.0",
					"\"$(SRCROOT)/Products\"",
				);
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				INFOPLIST_FILE = tingLite/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(SDKROOT)/usr/lib/swift",
				);
				MARKETING_VERSION = "$(APP_VER)";
				ORDER_FILE = "${SRCROOT}/order.order";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-fprofile-instr-generate",
					"-weak_framework",
					Combine,
					"-weak_framework",
					SwiftUI,
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.tingLite;
				PRODUCT_NAME = tingLite;
				PROVISIONING_PROFILE_SPECIFIER = "xmlyLite-Dis";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "xmlyLite-Dis";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = RELEASE;
				SWIFT_OBJC_BRIDGING_HEADER = "tingLite/tingLite-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TVOS_DEPLOYMENT_TARGET = 10.0;
				VALID_ARCHS = "armv7 armv7s arm64 x86_64";
			};
			name = Release;
		};
		956FCD24268AEAEC0098B44B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 777C9FB67350629A1D4269ED /* Pods-tingLiteAPNSerEx.debug.xcconfig */;
			buildSettings = {
				APP_BUILD = "${APP_BUILD}";
				APP_VER = "${APP_VER}";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 250321;
				DEVELOPMENT_TEAM = J32AD5KDR3;
				INFOPLIST_FILE = tingLiteAPNSerEx/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = "$(APP_VER)";
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.tingLite.tingLiteAPNSerEx;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		956FCD25268AEAEC0098B44B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EB3AB33559A68814B2B6AD3C /* Pods-tingLiteAPNSerEx.release.xcconfig */;
			buildSettings = {
				APP_BUILD = "${APP_BUILD}";
				APP_VER = "${APP_VER}";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 250321;
				DEVELOPMENT_TEAM = J32AD5KDR3;
				INFOPLIST_FILE = tingLiteAPNSerEx/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = "$(APP_VER)";
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.tingLite.tingLiteAPNSerEx;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "xmlyLite-apnsExt-Dis";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		956FCD26268AEAEC0098B44B /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D0C2664D07714439E97CF3AA /* Pods-tingLiteAPNSerEx.alpha.xcconfig */;
			buildSettings = {
				APP_BUILD = "${APP_BUILD}";
				APP_VER = "${APP_VER}";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 250321;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = J32AD5KDR3;
				INFOPLIST_FILE = tingLiteAPNSerEx/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = "$(APP_VER)";
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.tingLite.tingLiteAPNSerEx;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "xmlyLite-apnsExt-dev";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Alpha;
		};
		B0F3057F2316B41A006C4356 /* Alpha */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APP_BUILD = 250321;
				APP_VER = 3.0.37;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Xi Da (Shanghai) Network Technology Co., Ltd. (AS4ANJJUVM)";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 250321;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = "ALPHA=1";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.2;
				MARKETING_VERSION = "$(APP_VER)";
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = ALPHA;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Alpha;
		};
		B0F305802316B41A006C4356 /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0FBCACA00E943B7F12B7BC0B /* Pods-tingLite.alpha.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				APP_BUILD = "${APP_BUILD}";
				APP_CHANNEL = "ios-b1";
				APP_VER = "${APP_VER}";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = tingLite/tingLite.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 250321;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = J32AD5KDR3;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/tingLite/ThirdParty",
					"$(PROJECT_DIR)/tingLite/ThirdParty/UM_7.3.0",
					"\"$(SRCROOT)/Products\"",
				);
				INFOPLIST_FILE = tingLite/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(SDKROOT)/usr/lib/swift",
				);
				MARKETING_VERSION = "$(APP_VER)";
				ORDER_FILE = "${SRCROOT}/order.order";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-fprofile-instr-generate",
					"-weak_framework",
					Combine,
					"-weak_framework",
					SwiftUI,
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.tingLite;
				PRODUCT_NAME = tingLite;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.ximalaya.tingLite";
				SWIFT_OBJC_BRIDGING_HEADER = "tingLite/tingLite-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TVOS_DEPLOYMENT_TARGET = 10.0;
				VALID_ARCHS = "armv7 armv7s arm64 x86_64";
			};
			name = Alpha;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8817FB1722D8464A00DC0084 /* Build configuration list for PBXProject "tingLite" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8817FB2E22D8464A00DC0084 /* Debug */,
				8817FB2F22D8464A00DC0084 /* Release */,
				B0F3057F2316B41A006C4356 /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8817FB3022D8464A00DC0084 /* Build configuration list for PBXNativeTarget "tingLite" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8817FB3122D8464A00DC0084 /* Debug */,
				8817FB3222D8464A00DC0084 /* Release */,
				B0F305802316B41A006C4356 /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		956FCD27268AEAEC0098B44B /* Build configuration list for PBXNativeTarget "tingLiteAPNSerEx" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				956FCD24268AEAEC0098B44B /* Debug */,
				956FCD25268AEAEC0098B44B /* Release */,
				956FCD26268AEAEC0098B44B /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8817FB1422D8464A00DC0084 /* Project object */;
}
