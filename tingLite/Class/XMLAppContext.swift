//
//  XMLAppContext.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/9/16.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import XMADXModule
import CommBusiness
import RxSwift
import XMBase
import XMConfigModule
import XMUtilModule
import class BaseModule.XMLClockManager
import XMSocial
import LoginModule

#if DEBUG
import Bugly
#else
import FirebaseCrashlytics
import FirebaseAnalytics
import FirebaseCore
//import class KillBug.KBAutoTrackManager
#endif
import class XMWebModule.XMHybridVC
import class MediaModule.XMMediaPlayer
import class MediaModule.XMPlayStatisticManager
import XMConfigCenter
import class XMBXMSDK.DHCWSDKConfig
import XMXUID

public class XMLAppContext: NSObject, XMXUIDDelegate {
    // MARK: - 静态成员变量
    static let shared = XMLAppContext()
    // 是否已经延时加载
    private var _hasCalledDelayLaunch = false
    private var _hasRegisterMainTread = false
    // 是否已经初始化SDK
    private var _hasRegisterSDK       = false
    // 友盟ID
    public var umid: String { UMConfigure.umidString() ?? "57314777e63da6bc648741875a15414" }
    
    override init() {
        super.init()
        // 配置基础环境
        setupBaseEnvironment()
    }
    
    // 延时加载配置
    func delayLaunchWhenTabAppear() {
        guard _hasCalledDelayLaunch == false else { return }
        _hasCalledDelayLaunch = true
        // 延时配置服务端时间环境
        XMLTimeStamp.shared().refreshIfNeed(true) { if $1 { XMLClockManager.shared.setup($0, isReset: true) } }
        // 延时配置付费解锁免费声音次数环境
        XMLPaidUnlockCounter.shared().setupEnvironment()
        // 延时配置付费解锁免费解锁音频集合环境
        XMLPaidUnlockRecorder.shared().setupEnvironment()
        // 延时配置付费解锁管理器环境
        XMLPaidUnlockManager.shared().setupEnvironment()
        XMHybridVC.updateWKUserAgent()
        // 延时设置，防止网络还没请求完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            let `switch` = XMRemote.boolValue(group: XMRemote.kLiteGroupName, item: "playerStatisticSwitch", defaultValue: true)
            XMMediaPlayer.shared().statisticManager.enable = `switch`
            // Spotlight注册
            XMLSpotlightManager.shared().register()
            // 根据远程配置更新Spotlight
            XMLSpotlightManager.shared().updateRemoteConfigIfNeed()
        }
        // 延时二次设置APM(防止后台配置中心开关状态异常)
        (UIApplication.shared.delegate as? AppDelegate)?.setupAPMModule()
        DHCWSDKConfig.configManagerAppId("5864137b0da2441297c00c6ca34424c7")
        #if DEBUG
        DHCWSDKConfig.setDebugLog()
        #endif
    }
    
    // 主线程配置基础SDK
    func setupSDKOnMainThread() {
        guard _hasRegisterMainTread == false else { return }
        _hasRegisterMainTread = true
        // 获取CAID
        XMCAIDProvider.register()
        #if DEBUG
        #else
        FirebaseApp.configure()
        if XMSettings.shared().isLoggedIn {
            Crashlytics.crashlytics().setUserID("\(XMSettings.shared().currentUid ?? 0)")
        }
        // 避免影响启动做了延时
//        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
//            KBAutoTrackManager.shared().debugInfoHandler { (info) in
//                Crashlytics.crashlytics().log(info)
//            }
//        }
        #endif
        // 配置友盟 当前版本：7.3.0更新日期：2021年06月09日(UMAPM初始化需要后置在其他崩溃组件之后)
        // 下一个runloop 启动
        DispatchQueue.main.async {
            let appKey = "5df9f39c570df39bc8000684"
            UMConfigure.initWithAppkey(appKey, channel: XMConfig.shared().channelId)
        }
    }
    
    // 配置基础SDK
    func setupSDKonBackThread() {
        guard _hasRegisterSDK == false else { return }
        _hasRegisterSDK = true
        // 配置社会化分享平台
        self.registerSharePlatforms()
        // 配置崩溃收集 Debug使用Bugly, Aplha/Release 使用Firebase
        #if DEBUG
        let config = BuglyConfig()
        config.reportLogLevel = .warn
        Bugly.start(withAppId: "9f121310cd", config: config)
        #endif
    }
    
    public func xuidStart() {
        XMXUID.shared().setDebug(!XMEnvironment.onlineEnvironment())
        XMXUID.shared().setChannel(XMConfig.shared().channelId)
        XMXUID.shared().setCustomer("vx9bz_shuzilm_cn")
        XMXUID.shared().delegate = self
        XMXUID.shared().xuidGetConf()
    }
    
    public func xuidAgree() {
        DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(10)) {
            self.xuidSwitch()
        }
        let xopen = UserDefaults.standard.object(forKey: "xuid_ios_open")
        let xduopen = UserDefaults.standard.object(forKey: "xuid_ios_du")
        let xevery = UserDefaults.standard.object(forKey: "xuid_ios_every")
        
        let xuid = xopen as? Bool ?? true
        let duopen = xduopen as? Bool ?? true
        let every = xevery as? Bool ?? true
        
        self.xuidswitchLocalWithUpdateConfig(open: xuid, du: duopen, every: every)
        
        let deviceId = XMConfig.shared().uuid
        XMXUID.shared().setDevice(deviceId)
        XMXUID.shared().xuidAgree()
    }
    
    private func xuidSwitch() {
        let xuid_open = XMRemote.boolValue(group: XMRemote.kLiteGroupName, item: "xuid_ios_open", defaultValue: true)
        let xuid_du = XMRemote.boolValue(group: XMRemote.kLiteGroupName, item: "xuid_ios_du", defaultValue: true)
        let xuid_every = XMRemote.boolValue(group: XMRemote.kLiteGroupName, item: "xuid_ios_every", defaultValue: true)
        
        self.xuidswitchLocalWithUpdateConfig(open: xuid_open, du: xuid_du, every: xuid_every)
        
        UserDefaults.standard.setValue(xuid_open, forKey: "xuid_ios_open")
        UserDefaults.standard.setValue(xuid_du, forKey: "xuid_ios_du")
        UserDefaults.standard.setValue(xuid_every, forKey: "xuid_ios_every")
    }
    
    private func xuidswitchLocalWithUpdateConfig(open: Bool, du: Bool, every: Bool) {
        // 更新sdk开关
        XMXUID.shared().enable(open)
        XMXUID.shared().digitalUnionEnable(du)
        XMXUID.shared().everydayReport = every
    }
    
    public func xmxuid_log(_ msg: String!) {
        XMLAPMLogger.info("xmxuid, \(msg ?? "")")
    }
}

// MARK: - 辅助Method
extension XMLAppContext {
    // 配置基础环境
    fileprivate func setupBaseEnvironment() {
        _ = NotificationCenter.default.rx.notification(wifiAvailableNotification).take(until: self.rx.deallocated).subscribe(onNext: { [weak self] _ in
            self?.mobileNetReconnectEventHandle()
        })
        _ = NotificationCenter.default.rx.notification(wifiUnavailableNotification).take(until: self.rx.deallocated).subscribe(onNext: { [weak self] _ in
            if XMConfig.shared().networkAvailable { self?.mobileNetReconnectEventHandle() }
        })
    }
    
    // 当前设备重新链接到网络
    fileprivate func mobileNetReconnectEventHandle() {
        // 延时配置付费解锁管理器环境
        XMLPaidUnlockManager.shared().setupEnvironment()
    }
    
    // 注册社会化分享平台
    fileprivate func registerSharePlatforms() {
        let sina = XMCSocialPlatform()
        sina.type = .typeSinaWeibo
        
        let qq = XMCSocialPlatform()
        qq.type = .typeQQ
        
        let wx = XMCSocialPlatform()
        wx.type = .typeWechat
        
        XMCSocialManager.registerActivePlatforms([sina, qq, wx]) { (platformType, appInfo) in
            let url = "http://www.ximalaya.com"
            let type = platformType
            switch type {
            case .typeSinaWeibo:
                appInfo?[kXMCSocialInitAppKey] = kSocialWeiboKey
                appInfo?[kXMCSocialInitAppSecretKey] = "77d4460b6d12c174e839d698b4341453"
                appInfo?[kXMCSocialInitRedirectUriKey] = url
                appInfo?[kXMCSocialInitUniversalLink] = "https://m.ximalaya.com/weibosdk/"
            case .typeQQ:
                appInfo?[kXMCSocialInitAppKey] = kSocialQQKey
                appInfo?[kXMCSocialInitAppSecretKey] = "59f3b1572dab5825431e7627919d2725"
                appInfo?[kXMCSocialInitRedirectUriKey] = url
                // XMSocial 0.1.7  XMThirdParty/Wechat 0.3.2 使用
                 appInfo?[kXMCSocialInitUniversalLink] = "https://www.ximalaya.com/qq_conn/101586639"
            case .typeWechat:
                appInfo?[kXMCSocialInitAppKey] = kSocialWXKey
                appInfo?[kXMCSocialInitAppSecretKey] = "6f671b738b24f6961858a13790c009d8"
                appInfo?[kXMCSocialInitRedirectUriKey] = url
                // XMSocial 0.1.7  XMThirdParty/Wechat 0.3.2 使用
                 appInfo?[kXMCSocialInitUniversalLink] = "https://www.ximalaya.com/"
            default:
                print(appInfo ?? "")
            }
        }
    }
}
