//
//  XMLDeviceManager.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2019/12/23.
//  Copyright © 2019 ximalaya. All rights reserved.
//

import Foundation
import XMNetworkModule
import XMConfigModule
import XMBase
import SwiftyUserDefaults
import RxSwift
import XMWebModule
import AudioModule
import SwiftyJSON
import RouterModule
import BaseModule
import XMUtilModule
import AVFoundation
import AdServices
import XMAccount
import UIKit

///时间戳
func ts() -> String {"\(Int64(Date().timeIntervalSince1970 * 1000))"}

///最新的主要的激活接口
fileprivate let outadBacktoDomain = "\(xmEnvironmentUrl(kAdServerUrl)!)outadservice/v1/outad/backto"

///串码接口
fileprivate let serialCodeDealDomain = "\(xmEnvironmentUrl(kMobileServerUrl)!)thirdparty-share/shareCommand"

public class XMLDeviceManager {
    
    fileprivate static let instance = XMLDeviceManager()
    
    public static func shared() -> XMLDeviceManager { return instance }
    
    fileprivate var disposeBag = DisposeBag()
    
    open var isActived            : Bool = false   // 是否启动激活上报
    open var isActiveRetryReported: Bool = false   // 是否激活失败上报
    
    init() {
        // 配置通知
        configNotification()
    }
    
    public func uploadBackToInfo(_ callback: ((Bool) -> Void)? = nil) {
        guard UserDefaults.standard.bool(forKey: "kCurrentDeviceIsActiveSaveKey") == false else { callback?(false);return }
        UIPasteboard.detectPastboardCommand { [weak self] (command) in
            UIPasteboard.general.string = ""
            self?.p_uploadBackToInfoInside(command ?? "", callback: callback)
        }
    }
    
    fileprivate func p_uploadBackToInfoInside(_ command: String, callback: ((Bool) -> Void)? = nil) {
        let dateFormate = DateFormatter()
        dateFormate.dateFormat = "yyyy-MM-dd"
        let timeValue = dateFormate.string(from: Date())
        let cacheIDFAInfo = XMConfig.shared().cachedIDFA()
        let params: [String : Any] = ["bundleId": XMSYSTEM_BUNDLEID,
                                      "deviceTypeFromUa":"",
                                      "newStatus": 0,
                                      "primary": false,
                                      "memory": XMConfig.shared().totalDiskSizeGB(),
                                      "app": "1488281962",
                                      "command": command,
                                      "source": "appstore",
                                      "udid": XMConfig.shared().uuid,
                                      "deviceType": "ios",
                                      "deviceVersion": XMSYSTEM_BUNDLEVERSION,
                                      "ip": XMConfig.localIP ?? "",
                                      "useragent": XMConfig.shared().reqHeaderUserAgent() ?? "",
                                      "systemUserAgent": XMConfig.systemUserAgent(),
                                      "sysIDFA": cacheIDFAInfo.value,
                                      "idfaLimit": cacheIDFAInfo.status,
                                      "index": "",
                                      "returnFormat": 0,
                                      "securityToken": "",
                                      "version": XMSYSTEM_BUNDLEVERSION,
                                      "device": XMConfig.shared().simpleModel,
                                      "rip": "",
                                      "xid": "",
                                      "umid": XMLAppContext.shared.umid,
                                      "networkMode": XMConfig.shared().deviceNetMode(true),
                                      "time": timeValue,
                                      "isFirstInstalled": XMConfig.shared().isFirstLaunch]
        _ = XMNetwork.get(outadBacktoDomain, parameters: params).subscribe(onNext: { [weak self] (network, success) in
            guard let wself = self else { callback?(false);return }
            var noti = Notification(name: Notification.Name.init("currentDeviceIsActiveSave"), object: nil, userInfo: nil)
            if success {
                UserDefaults.standard.setValue(true, forKey: "kCurrentDeviceIsActiveSaveKey")
                UserDefaults.standard.synchronize()
                noti.object = NSNumber(value: true)
                NotificationCenter.default.post(noti)
                if let data = network.content {
                    let dataJSON: JSON = JSON(data)
                    let urlStr: String = dataJSON["data"]["itingUrl"].string ?? ""
                    let originUrl: String = dataJSON["data"]["originalItingUrl"].string ?? ""
                    if !command.isEmpty, (urlStr.isEmpty || originUrl.isEmpty) {
                        wself.sendStartAppLoggerBackground(true, command: command)
                        wself.p_handleShareCommand(command) { (result) in
                            if result { UIPasteboard.general.string = "" }
                            callback?(result)
                        }
                    } else {
                        wself.handleOutbackResult(urlStr, originUrl: originUrl, command: command, callback: callback)
                    }
                } else {
                    callback?(false)
                }
                return
            }
            NotificationCenter.default.post(noti)
            UIPasteboard.general.string = command
            callback?(false)
        })
    }
    
    func handleOutbackResult(_ urlStr: String, originUrl: String, command: String, callback: ((Bool) -> Void)? = nil) {
        // 清除空格
        let urlStr   : String = urlStr.replacingOccurrences(of: " ", with: "")
        let originUrl: String = originUrl.replacingOccurrences(of: " ", with: "")
        if command.count > 0 {
            XMEventLog.logEventWithId(5141, serviceId: "commandOpenSucceed", properties: ["command": command,
                                                                                          "srcPageUrl": originUrl])
        }
        var openStr: String = urlStr
        if urlStr.hasPrefix("http") {
            let link: String = urlStr
            let utingLink = link.replacingOccurrences(of: "iting", with: "uting")
            if let encodeScheme = utingLink.components(separatedBy: "uting=").last, encodeScheme.count > 0 {
                var scheme: String = encodeScheme.decodeUrl() ?? ""
                scheme = scheme.decodeUrl() ?? ""
                scheme = scheme.decodeUrl() ?? ""
                scheme = scheme.decodeUrl() ?? ""
                scheme = scheme.decodeUrl() ?? ""
                scheme = scheme.decodeUrl() ?? ""
                if RouterScheme.canOpenURLString(scheme) {
                    openStr = scheme
                }
            }
        }
        
        if openStr.count > 0 {
            let handleValue: String = openStr.lowercased().replacingOccurrences(of: "msg_type=", with: "xml_schemaType=1&msg_type=")
            if command.count > 0, RouterScheme.canOpenURLPlayPageString(openStr) {
                XMEventLog.logEventWithId(5142, serviceId: "commandOpenPlayPageSucceed", properties: ["command": command,
                                                                                                      "srcPageUrl": originUrl])
            }
            if RouterScheme.canOpenURLString(openStr) {
                XMEventLog.logEventWithId(14300, serviceId: "openIting", properties: ["itingUrl": openStr])
            }
            RouterBridge(nil).mainTabHandleOpenURLWhenLaunch(handleValue)
            callback?(true)
        } else {
            callback?(false)
        }
    }
    
    func checkSharePassWord(_ fromBackground: Bool = false, completion: @escaping ((_ success: Bool) -> Void) ) {
        DispatchQueue.global(qos: .default).async {
            UIPasteboard.detectPastboardCommand { [weak self] (command) in
                DispatchQueue.main.async {
                    guard let wself = self else { return }
                    wself.sendStartAppLoggerBackground(fromBackground, command: command)
                    wself.p_handleShareCommand(command) { (result) in
                        if result { UIPasteboard.general.string = "" }
                        completion(result)
                    }
                }
            }
        }
    }
    
    func p_handleShareCommand(_ shareCommand: String?, completion: @escaping ((_ success: Bool) -> Void) ) {
        guard var `shareCommand` = shareCommand, `shareCommand`.count > 0 else { return completion(false) }
        if shareCommand.contains("#") {
            let array = shareCommand.components(separatedBy: "#")
            if array.count == 3 {
                shareCommand = array[1]
            }
        }
        
        guard shareCommand.hasPrefix("Xm") else { return completion(false) }
        
        var canNext: Bool = true
        if let urlString = shareCommand.encodeUrl(), urlString != shareCommand { canNext = false }
        
        if shareCommand.contains("/") { canNext = false }
        
        guard canNext else { return completion(false) }
        let params: [String : Any] = ["command": shareCommand]
        _ = XMNetwork.get(serialCodeDealDomain, parameters: params).subscribe(onNext: { (network, success) in
            if success {
                if let data = network.content {
                    if let shareContentType = JSON(data).dictionaryValue["shareContentType"]?.stringValue, shareContentType == "URL",
                        let link = JSON(data).dictionaryValue["link"]?.stringValue, link.count > 0 {
                        XMEventLog.logEventWithId(5141, serviceId: "commandOpenSucceed", properties: ["command": shareCommand,
                                                                                                      "srcPageUrl": link])
                        let utingLink = link.replacingOccurrences(of: "iting", with: "uting")
                        if let encodeScheme = utingLink.components(separatedBy: "uting=").last {
                            var scheme: String = encodeScheme.decodeUrl() ?? ""
                            scheme = scheme.decodeUrl() ?? ""
                            scheme = scheme.decodeUrl() ?? ""
                            scheme = scheme.decodeUrl() ?? ""
                            scheme = scheme.decodeUrl() ?? ""
                            scheme = scheme.decodeUrl() ?? ""
                            if RouterScheme.canOpenURLString(scheme) {
                                if RouterScheme.canOpenURLPlayPageString(scheme) {
                                    XMEventLog.logEventWithId(5142, serviceId: "commandOpenPlayPageSucceed", properties: ["command": shareCommand,
                                                                                                                          "srcPageUrl": link])
                                }
                                let handleValue: String = scheme.lowercased().replacingOccurrences(of: "msg_type=", with: "xml_schemaType=1&msg_type=")
                                let result = RouterScheme.handleOpenURLString(handleValue, fromView: nil)
                                completion(result)
                                return
                            }
                        }
                    }
                }
            }
            completion(false)
        })
    }
    
    func sendStartAppLoggerBackground(_ fromBackground: Bool, command: String?) {
        var pasteCommand: String? = nil
        if pasteCommand == nil || pasteCommand!.isEmpty {
            pasteCommand = UIPasteboard.lastPastboardCommand()
        }
        XMCAIDProvider.get { (_, caidCode) in
            XMEventLog.logEventWithId(22629, serviceId: "startApp", properties: ["fromBack": fromBackground ? 1 : 0,
                                                                                 "command": pasteCommand ?? "",
                                                                                 "CAID": caidCode ?? ""])
        }
    }
    
    // 加载服务器时间
    public func syncServiceTimeData() {
        XMLTimeStamp.shared().refreshIfNeed(true) { if $1 { XMLClockManager.shared.setup($0, isReset: true) } }
    }
}

// MARK: - 辅助 Method
extension XMLDeviceManager {
    // 配置通知
    fileprivate func configNotification() {
        _ = NotificationCenter.default.rx.notification(wifiAvailableNotification).subscribe(onNext: { [weak self] (_) in
            if XMConfig.shared().networkAvailable { self?.syncServiceTimeData() }
        }).disposed(by: self.disposeBag)
        _ = NotificationCenter.default.rx.notification(wifiUnavailableNotification).subscribe(onNext: { [weak self] (_) in
            if XMConfig.shared().networkAvailable { self?.syncServiceTimeData() }
        }).disposed(by: self.disposeBag)
    }
}

// MARK: - 音量监控业务
extension XMLDeviceManager {
    // 上报设备音量状态
    func uploadVolumeStatusIfNeed(_ trackId: Int64?, ignorePlayStatus: Bool = false) {
        // 保证设备正在播放
        guard ignorePlayStatus || RouterBridge(nil).playerIsPlaying() else { return }
        // 保证音量处于静音状态(<=0.05)
        guard AVAudioSession.sharedInstance().outputVolume <= 0.05 else { return }
        XMEventLog.logEventWithId(31961, serviceId: "others", properties: ["trackId": trackId ?? 0])
    }
}

// MARK: - ASA监控业务上报
extension XMLDeviceManager {
    // 检查并且上报ASA相关信息
    public func uploadASAIfNeed() {
        // 仅在Release环境进行处理，防止DEBUG/ALPHA环境影响线上统计(跟主站统一)
        // ADClient不能抓包测试
        XMLAPMLogger.info("ASA 逻辑开始", moduler: .main)
        #if RELEASE
        var overDay: Int = -1
        if let deniedVal: TimeInterval = UserDefaults.standard.value(forKey: "kXMLASAInfoUploadSaveDate") as? TimeInterval {
            overDay = Date().days(form: Date(timeIntervalSince1970: deniedVal))
        }
        let limitDay: Int = 28
        XMLAPMLogger.info("ASA 时间处理: overDay \(overDay) ", moduler: .main)
        guard overDay >= limitDay || overDay < 0 else { return }
        XMLAPMLogger.info("ASA 开始延时5s", moduler: .main)
        DispatchQueue.global(qos: .userInteractive).asyncAfter(deadline: .now() + 5) { [weak self] in
            XMLAPMLogger.info("ASA 延时5s结束", moduler: .main)
            if #available(iOS 14.3, *) {
                do {
                    self?.sendASAUploadState(0, isSuccess: true, isFirst: overDay == -1)
                    let token: String = try AAAttribution.attributionToken()
                    self?.sendASAUploadState(1, isSuccess: true, isFirst: overDay == -1)
                    DispatchQueue.main.async { [weak self] in
                        self?.sendASAUploadState(2, isSuccess: true, isFirst: overDay == -1)
                        self?.sendASAUploadState(3, isSuccess: true, isFirst: overDay == -1)
                        self?.reportToServer(token, retryCount: 50)
                    }
                } catch let pattern {
                    self?.sendASAUploadState(1, isSuccess: false, isFirst: overDay == -1, errorMsg: pattern.localizedDescription)
                    XMLAPMLogger.info("ASA 14.3 获取方法异常", moduler: .main)
                }
            } else {
                // iOS 14.3 以下版本：ADClient API 已被废弃，无法获取 ASA 归因信息
                self?.sendASAUploadState(0, isSuccess: true, isFirst: overDay == -1)
                self?.sendASAUploadState(1, isSuccess: false, isFirst: overDay == -1, errorMsg: "iOS 版本过低，ADClient API 已被废弃")
                XMLAPMLogger.info("ASA iOS 版本过低，ADClient API 已被废弃", moduler: .main)
            }
        }
        #endif
    }
    
    // 上报到服务端
    private func reportToServer(_ token: String, retryCount: Int, clickData: String = "") {
        // 重试失败次数用尽后，直接返回
        guard retryCount >= 0 else { return }
        let domain: String = xmEnvironmentUrl(kOpenAPIServerUrl) + "growth/feedback/asa/3/collect"
        let params: [String: Any] = ["dcid"     : XMConfig.shared().uuid,
                                     "xmdf"     : XMConfig.shared().idfa,
                                     "xima_type": "store",
                                     "xima_OS"  : "IOS",
                                     "xima_UA"  : XMConfig.systemUserAgent(),
                                     "token"    : token,
                                     "clickData": clickData]
        var paramsValue: String = ""
        if let data = NSDictionary(dictionary: params).jsonString() {
            paramsValue = data
        }
        self.sendASAUploadState(4, isSuccess: true, isFirst: retryCount == 50, text: paramsValue)
        // 确保参数有效
        guard !token.isEmpty || !clickData.isEmpty else {
            self.sendASAUploadState(5, isSuccess: false, isFirst: retryCount == 50, text: paramsValue, errorMsg: "token/clickDate为空")
            return
        }
        self.sendASAUploadState(5, isSuccess: true, isFirst: retryCount == 50, text: paramsValue)
        _ = XMNetwork.get(domain, parameters: params).subscribe(onNext: { [weak self] (network, success) in
            self?.sendASAUploadState(6, isSuccess: success, isFirst: retryCount == 50, text: paramsValue, errorMsg: network.error?.localizedDescription ?? "")
            XMEventLog.logEventWithId(39037, serviceId: "others", properties: ["success": success, "isFirst": retryCount == 50, "deviceVersion": XMSYSTEM_BUNDLEVERSION])
            if success {
                UserDefaults.standard.setValue(Date().timeIntervalSince1970, forKey: "kXMLASAInfoUploadSaveDate")
                UserDefaults.standard.synchronize()
            } else {
                self?.sendASAUploadState(7, isSuccess: false, isFirst: retryCount == 50, text: paramsValue, errorMsg: network.error?.localizedDescription ?? "")
                // 10s延时上报
                DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [weak self] in
                    self?.reportToServer(token, retryCount: retryCount - 1, clickData: clickData)
                }
            }
        })
    }
    
    // ASA上报日志
    private func sendASAUploadState(_ state: Int, isSuccess: Bool, isFirst: Bool, text: String = "", errorMsg: String = "") {
        DispatchQueue.main.async {
            let params: [String: Any] = ["state"        : state,
                                         "success"      : isSuccess,
                                         "isFirst"      : isFirst,
                                         "text"         : text,
                                         "deviceVersion": UIDevice.current.systemVersion,
                                         "source"       : XMSYSTEM_BUNDLEVERSION,
                                         "sdkErrorMsg"  : errorMsg]
            XMEventLog.logEventWithId(39292, serviceId: "others", properties: params)
            let description: String = "state: \(state) success: \(isSuccess) isFirst: \(isFirst) deviceVersion: \(UIDevice.current.systemVersion) source: \(XMSYSTEM_BUNDLEVERSION) text: \(text) errorMsg: \(errorMsg)"
            XMLAPMLogger.info("ASA 状态统计: \(description)", moduler: .main)
        }
    }
}
