//
//  XMLADXConfig.swift
//  XMADXModule
//
//  Created by yangle<PERSON> on 2020/6/4.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import XMConfigModule

// ADX后台配置数据管理中心
@objcMembers
public class XMLADXConfig: NSObject {
    // 信息流视频是否自动播放(穿山甲SDK不支持此功能，暂时不接入)
    static func isFeedVideoADAutoPlay() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "FlowAdAutoPlay", defaultValue: false)
    }
    
    // 当广告SDK触发声音时，是否暂停本地声音，广告结束后是否恢复现场
    static func isPauseAudioWhenADSound() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite", item: "ad_playcontrol_2", defaultValue: true)
    }
    
    // 喜马激励视频广告触发声音时，是否暂停本地声音，广告结束后是否恢复现场
    static func isPauseAudioInRewardVideoAd() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite", item: "ad_playcontrol", defaultValue: true)
    }
    
    // 是否开启穿山甲开屏预加载逻辑
    static func enableBUSplashPretreat() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "TTLoadingPreRequest", defaultValue: true)
    }
    
    // 热启动开屏最小时间间隔
    static func kAppLaunchLimitMinInterval() -> TimeInterval {
        return max(TimeInterval(XMRemote.intValue(group: "ximalaya_lite", item: "wakeTime", defaultValue: 60)), 10.0)
    }
    
    // 缓存的最大保留时间
    static func kADXLoaderItemValidMaxTime() -> Double {
        return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "adShowCacheTime", defaultValue: 15)) * 60.0
    }
    
    // SDK最大加载超时时间
    static func kADXLoaderMaxLimitTime(_ type: XMLADXLoaderType) -> Double {
        switch type {
        case .splash: return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "LoadingadRequestBackTime", defaultValue: 5000)) / 1000.0
        case .flow  : return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "FlowAdRequestBackTime"   , defaultValue: 5000)) / 1000.0
        case .video : return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "VideoAdRequestBackTime"  , defaultValue: 5000)) / 1000.0
        case .plaque: return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "FlowAdRequestBackTime"  , defaultValue: 5000)) / 1000.0
        }
    }
    
    // 预加载开屏最大超时时间
    static func kPreTreatLimitMaxTimeSplash() -> Double {
        return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "LoadingPreRequestTime", defaultValue: 5000)) / 1000.0
    }
    
    // 预加载激励视频最大超时时间
    static func kPreTreatLimitMaxTimeVideo() -> Double {
        return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "VideoPreRequestTime", defaultValue: 5000)) / 1000.0
    }
    
    // 预加载信息流最大超时时间
    static func kPreTreatLimitMaxTimeFlow() -> Double {
        return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "FlowPreRequestTime", defaultValue: 5000)) / 1000.0
    }
    
    // 免广告引导蒙层配置(true点击关闭按钮时出引导蒙层，false点击关闭按钮时不出引导蒙层)
    public static func kAdvertisingFree() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "AdvertisingFree", defaultValue: false)
    }
    
    // 免广告时长(看完视频免广告的时长，单位：秒)
    public static func kAdvertisingFreeTimes() -> Double {
        return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "AdvertisingFreeTimes", defaultValue: 1800))
    }
    
    // 免广告引导文案(免广告蒙层上的看视频引导提示文案)
    public static func kAdvertisingFreePrompt() -> String {
        return XMRemote.stringValue(group: "ximalaya_lite_ad", item: "AdvertisingFreePrompt", defaultValue: "看小视频全场免广告30分钟")
    }
    
    // 免广告蒙层UI(1 半透明蒙层 2 实心蒙层)
    public static func kAdvertisingFreeUI() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "AdvertisingFreeUI", defaultValue: 1)
    }
    
    // 免广告激励视频时长配置(单位秒，免广告的激励视频倒计时时间)
    public static func kAdvertisingFreeVideoTimes() -> Double {
        return Double(XMRemote.intValue(group: "ximalaya_lite_ad", item: "SoundPatchAdFreeVideoTime", defaultValue: 30))
    }
    
    // 贴片免广告入口配置(贴片免广告入口的文案、是否出现入口的状态配置)
    public static func kAdvertisingFreeConfig() -> String? {
        return XMRemote.dictionaryValue(group: "ximalaya_lite_ad", item: "SoundPatchAdFree") as? String
    }
    
    // 贴片免广告入口类型(贴片免广告入口显示会员（状态true），或者看视频（状态false），默认会员)
    public static func enableSoundPatchAdFree() -> Bool {
        return !XMRemote.boolValue(group: "ximalaya_lite_ad", item: "SoundPatchAdFreeStyle", defaultValue: true)
    }
    
    // 是否开启开屏跳过延迟逻辑
    static func enableLoadingDelaySkip() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "LoadingDelaySkip", defaultValue: false)
    }
    
    // 视频前贴广告可跳过时间
    public static func kVideoPasterSkipTime() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "VideoPasterAdSkipTime", defaultValue: 5)
    }
    
    // 图文广告可跳过时间
    public static func kPasterSkipTime() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "PasterAdSkipTime", defaultValue: 3)
    }
    
    // 全屏视频顶部遮罩时长(默认30秒)
    static func kFullVideoMaskHiddenTime() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "FullScreenVideoTime", defaultValue: 30)
    }
    
    // 喜马开机广告摇一摇幅度（加速度值）
    public static func kShakeAcceleratedSpeed() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "iOSShakeSpeed", defaultValue: 1)
    }
    
    // 喜马开机广告摇一摇震动反馈
    public static func enableShakeVibration() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "splashHasVibration", defaultValue: false)
    }
    
    // 播放页贴片是否自动关闭逻辑
    public static func enableSoundPatchAutoClose() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "soundPatchAutoClose", defaultValue: false)
    }
    
    // 播放页贴片自动关闭时间
    public static func kSoundPatchAutoCloseTime() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "soundPatchAutoCloseTime", defaultValue: 6)
    }
    
    // 播放页沉浸式皮肤静态图展示后自动关闭的时间
    public static func kPlaySkinCloseTime() -> Int {
        return max(XMRemote.intValue(group: "ximalaya_lite_ad", item: "playSkinCloseTime", defaultValue: 5), 1)
    }
     
    // 播放页沉浸式皮肤关闭按钮出现的时间，0表示0秒就出现关闭按钮，3表示广告展示3s时出现关闭按钮，默认3s
    public static func kPlayskinCloseButton() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "PlayskinCloseButton", defaultValue: 0)
    }
    
    // 激励视频遮盖蒙层的倒计时，默认显示30s
    public static func kVideoMantleCountDown() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "videoMantleCountDown", defaultValue: 30)
    }
    
    // 激励视频蒙层上关闭按钮出现的时间，单位秒，配置0表示进入激励视频即跟随蒙层出现，配置5表示进入激励视频5秒时蒙层上出现关闭按钮
    public static func kVideoSkipAppearTime() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "videoSkipAppearTime", defaultValue: 0)
    }
    
    // 激励视频点击关闭时是否出现挽留弹窗，默认false（不出现）
    public static func enableVideoRetainPopup() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "videoRetainPopup", defaultValue: false)
    }
    
    // 激励视频是否出蒙层遮罩，以及关闭按钮和挽留弹窗的控制，皆有该配置控制是否打开，默认 true 打开
    public static func enableVideoSuperviseControl() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "videoSuperviseControl", defaultValue: true)
    }
    
    // 首焦关闭按钮出现的时间
    public static func kIndexFocusCloseButton() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "indexFocusCloseButton", defaultValue: 5)
    }
    
    // 开机提示条UI控制，true表示新版带动效UI，false表示老版不带动效UI
    public static func kLoadingPromptUIswitch() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "loadingPromptUIswitch", defaultValue: false)
    }
    
    // 我页大图广告关闭按钮倒计时，0表示0秒就出现关闭按钮，3表示广告倒计时3s后出现关闭按钮，默认3s
    public static func kMineflowClosebutton() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "mineflowClosebutton", defaultValue: 3)
    }
    
    // 历史页广告关闭按钮倒计时，0表示0秒就出现关闭按钮，3表示广告倒计时3s后出现关闭按钮，默认3s
    public static func kHistoreflowClosebutton() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "historeflowClosebutton", defaultValue: 3)
    }
    
    // 实时竞价兜底配置(客户端首次请求广告，或请求adx失败时，读取兜底配置来决定广告请求是否走bidding)
    public static func kBiddinConfig() -> String? {
        return XMRemote.dictionaryValue(group: "ximalaya_lite_ad", item: "biddinConfig") as? String
    }
    
    // 是否启用客户端竞价
    public static func isBiddingEnabled() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "clientBiddingEnabled", defaultValue: true)
    }
    
    // 播放页贴片自动关闭AI文稿逻辑
    public static func enableSoundPatchAILogicAutoClose() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "soundPatchAILogicAutoClose", defaultValue: false)
    }
    
    // 播放页贴片自动关闭时间AI文稿逻辑
    public static func kSoundPatchAILogicAutoCloseTime() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "soundPatchAILogicAutoCloseTime", defaultValue: 5)
    }
    
    // 三方监测是否上报cookie，false为不上报，默认false
    public static func kEnableThirdStatCookie() -> Bool {
        return XMRemote.boolValue(group: "ximalaya_lite_ad", item: "thirdStatCookie", defaultValue: false)
    }
    
    // 拉新拉活任务完成有效时间, 默认5秒
    public static func kTaskValidTime() -> TimeInterval {
        return TimeInterval(XMRemote.intValue(group: "ximalaya_lite_ad", item: "taskValidTime", defaultValue: 5))
    }
    
    // 滑动开屏 从底部往上计算屏幕滑动区域比例 默认：0.7
    public static func kLoadingSlideRegionnew() -> Double {
        return Double(XMRemote.stringValue(group: "ximalaya_lite_ad", item: "loadingSlideRegionnew", defaultValue: "0.7")) ?? Double(0.7)
    }
    
    // 滑动开屏 滑动距离 默认：5
    public static func kLoadingSlideDistance() -> Int {
        return XMRemote.intValue(group: "ximalaya_lite_ad", item: "loadingSlideDistance", defaultValue: 60)
    }
}
