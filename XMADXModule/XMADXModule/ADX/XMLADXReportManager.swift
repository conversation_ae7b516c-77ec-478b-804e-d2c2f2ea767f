//
//  XMLADXReportManager.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/5/13.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import XMConfigModule
import XMNetworkModule
import XMBase
import SwiftyJSON
import BUAdSDK
import GDTMobSDK
import BaiduMobAdSDK
import <PERSON><PERSON><PERSON><PERSON>
import XMAPM

public enum XMLAdUploadStaticType: String {
    case requestAd
    case showReady
    case show
    case click
}

public enum XMLAdUploadScene: Int {
    case exposure = 1
    case click    = 2
}

// MARK: - ADX SDK统计 
public class XMLADXReportManager: NSObject {
    // SDK错误上报
    public static func sendSDKErrorInfo(_ item: XMLAdTypeItemModel?, error: Error?) {
        let positionName: String = item?.xml_posiName ?? ""
        let isFromAdx: Bool      = !(item?.adid ?? "").isEmpty
        let dspId: String        = item?.dspPositionId ?? ""
        let sdkType: Int         = item?.xml_AdType().sdkType ?? 0
        let sdkErrorCode: Int    = (error as NSError?)?.code ?? 0
        let sdkErrorMsg: String  = (error as NSError?)?.description ?? ""
        let properties: [String: Any] = ["positionName": positionName,
                                         "isFromAdx": isFromAdx,
                                         "dspId": dspId,
                                         "sdkType": sdkType,
                                         "sdkErrorCode": sdkErrorCode,
                                         "sdkErrorMsg": sdkErrorMsg + "ErrorTime: \(Date().timeIntervalSince1970)"]
        XMLAPMLogger.error("SDK渲染失败, platform:\(item?.adtype ?? -1) slotId:\(item?.dspPositionId ?? "-") \(properties)", moduler: .adx)
        XMEventLog.logEventWithId(17369, serviceId: "dspErrorCode", properties: properties)
    }
    
    // 上报广告素材(三方SDK专用)
    public static func sendADMaterial(_ item: XMLADXLoaderItem?, scene: XMLAdUploadScene) {
        guard let `item` = item, let isSDKAd = item.item?.xml_isSDKAd(), isSDKAd else { return }
        var materials: [String: Any] = [String: Any]()
        var goal: Int = 2
        if let obj = item.f_buAd {
            materials = obj.xml_material()
            goal = obj.xml_promotionGoal()
        } else if let obj = item.f_gdtAd {
            materials = obj.xml_material()
            goal = obj.xml_promotionGoal()
        } else if let obj = item.f_bdAd {
            materials = obj.xml_material()
            goal = obj.xml_promotionGoal()
        } else if let obj = item.f_jdAd {
            materials = obj.xml_material()
            goal = obj.xml_promotionGoal()
        }
        // 上报素材为空则不进行上报
        guard materials.keys.count > 0 else { return }
        let uploaderParams: [String: Any] = item.item?.toMaterialsUploaderParams(materials, goal: goal, type: scene.rawValue) ?? [String: Any]()
        XMBehaviorManager.shared().postOnlineData(dic: uploaderParams, param: nil, type: .adMaterial)
    }
    
    // SDK上报逻辑(旧版)
    public static func sendSDKReportInfo(_ item: XMLAdTypeItemModel?, type: XMLAdUploadStaticType) {
        guard let `item` = item else { return }
        XMBehaviorManager.shared().postOnlineData(dic: ["adSource": "\(item.adtype)",
                                                        "appId": "1463",
                                                        "toutiaoType": type.rawValue,
                                                        "positionName": item.xml_posiName,
                                                        "vender": item.xml_AdType().sdkType],
                                                  param: nil, type: .ad)
    }
    
    // SDK上报逻辑(旧版)
    public static func sendSDKReportInfo(_ positionName: String, adSource: Int, vender: Int, type: XMLAdUploadStaticType) {
        XMBehaviorManager.shared().postOnlineData(dic: ["adSource": "\(adSource)",
                                                        "appId": "1463",
                                                        "toutiaoType": type.rawValue,
                                                        "positionName": positionName,
                                                        "vender": vender],
                                                  param: nil, type: .ad)
    }
    
    // show上报逻辑
    public static func sendShowReportInfo(_ item: XMLAdTypeItemModel?, positionName: String, vender: Int, extraParams: [String: Any] = [:]) {
        #if DEBUG
        NSLog(">|< 广告展示Show: %@, dspId: %@", positionName, item?.dspPositionId ?? "null")
        #endif
        if let isADX = item?.xml_isADX, isADX {
            self.sendTingShowReportInfo(item, extraParams: extraParams)
        } else {
            self.sendSDKReportInfo(positionName, adSource: item?.adtype ?? 10014, vender: vender, type: .show)
        }
        self.sendThirdShowReportInfo(item)
    }
    
    // click上报逻辑
    public static func sendClickReportInfo(_ item: XMLAdTypeItemModel?, positionName: String, vender: Int, extraParams: [String: Any] = [:]) {
        if let isADX = item?.xml_isADX, isADX {
            self.sendTingClickReportInfo(item, extraParams: extraParams)
        } else {
            self.sendSDKReportInfo(positionName, adSource: item?.adtype ?? 10014, vender: vender, type: .click)
        }
        self.sendThirdClickReportInfo(item)
    }
    
    // tingShow上报逻辑
    public static func sendTingShowReportInfo(_ item: XMLAdTypeItemModel?, extraParams: [String: Any] = [:]) {
        guard let `item` = item else { return }
        var dic: [String: Any] = item.toStaticParams("show", logType: "tingShow")
        if item.xml_posiName == "loading" { // 开屏需要附加额外参数
            dic["LoadingDelaySkip"]  = XMLADXConfig.enableLoadingDelaySkip()
            dic["jumpModeType"]      = item.jumpModeType
            dic["clickableAreaType"] = item.clickableAreaType
        } else if item.xml_posiName == "sub_sound_patch" { // 播放页贴片需要附加额外参数
            dic["isAutoClose"] = item.xml_isSilentPatch && XMLADXConfig.enableSoundPatchAutoClose()
            if item.xml_showType().isImmersePatch {        // 沉浸式皮肤样式上报特殊处理
                dic["showForm"] = item.xml_isImmerseVPatch ? 2 : 0
            }
        } else if item.xml_posiName == "lite_read_page_interstitial" { // 阅读页要附加额外参数
            dic["bookId"] = item.xml_bookId
        }
        // 信息合并
        dic.merge(extraParams) { (_, new) in new }
        let tokenInfo = item.xml_validShowTokenInfo()
        if tokenInfo.needMore == false {
            dic["showToken"] = String.adxDecryption(with: tokenInfo.token)
            XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)
        } else {
            XMLADXReportManager.loadMoreValidToken { (token) in
                guard let `token` = token else { return }
                dic["showToken"] = String.adxDecryption(with: token)
                XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)                
            }
        }
    }
    
    // tingClick上报逻辑
    public static func sendTingClickReportInfo(_ item: XMLAdTypeItemModel?, extraParams: [String: Any] = [:]) {
        guard let `item` = item, item.link.count > 0 else { return }
        var parameters: [String: Any] = item.toClickParams("click", logType: "tingClick", positionName: item.xml_posiName)
        if item.xml_posiName == "loading" { // 开屏需要附加额外参数
            parameters["jumpModeType"]      = item.jumpModeType
            parameters["clickableAreaType"] = item.clickableAreaType
        } else if item.xml_posiName == "lite_read_page_interstitial" { // 阅读页要附加额外参数
            parameters["bookId"]      = item.xml_bookId
        }
        // 信息合并
        parameters.merge(extraParams) { (_, new) in new }
        let tokenInfo = item.xml_validClickTokenInfo()
        if tokenInfo.needMore == false {
            parameters["clickToken"] = String.adxDecryption(with: tokenInfo.token)
            item.link = item.link.replacingOccurrences(of: " ", with: "", options: .literal, range: nil)
            _ = XMNetwork.get(item.link, parameters: parameters).subscribe(onNext: { _ in })
        } else {
            XMLADXReportManager.loadMoreValidToken { (token) in
                guard let `token` = token else { return }
                parameters["clickToken"] = String.adxDecryption(with: token)
                _ = XMNetwork.get(item.link, parameters: parameters).subscribe(onNext: { _ in })
            }
        }
    }
    
    // tingShow上报逻辑(外部定制)
    public static func sendTingShowReportInfo(_ params: [String: Any], showTokenEnable: Bool) {
        var dic: [String: Any] = params
        let isHasToken: Bool = ((dic["showToken"] as? String) ?? "").count > 0
        if let token = dic["showToken"] as? String, token.count > 0 {
            dic["showToken"] = String.adxDecryption(with: token)
        }
        if isHasToken || showTokenEnable == false {
            XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)
        } else {
            XMLADXReportManager.loadMoreValidToken { (token) in
                guard let `token` = token else { return }
                dic["showToken"] = String.adxDecryption(with: token)
                XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)
            }
        }
    }
    
    // tingClick上报逻辑(外部定制)
    public static func sendTingClickReportInfo(_ url: String, params: [String: Any], clickTokenEnable: Bool) {
        guard url.count > 0 else { return }
        var parameters: [String: Any] = params
        let isHasToken: Bool = ((parameters["clickToken"] as? String) ?? "").count > 0
        if let token = parameters["clickToken"] as? String, token.count > 0 {
            parameters["clickToken"] = String.adxDecryption(with: token)
        }
        if isHasToken || clickTokenEnable == false {
            _ = XMNetwork.get(url, parameters: parameters).subscribe(onNext: { _ in })
        } else {
            XMLADXReportManager.loadMoreValidToken { (token) in
                guard let `token` = token else { return }
                parameters["clickToken"] = String.adxDecryption(with: token)
                _ = XMNetwork.get(url, parameters: parameters).subscribe(onNext: { _ in })
            }
        }
    }
    
    // ADX返回的第三方展示（thirdStatUrl、thirdShowStatUrls)上报逻辑
    public static func sendThirdShowReportInfo(_ item: XMLAdTypeItemModel?) {
        guard let `item` = item else { return }
        var urls: [String] = []
        urls.append(item.thirdStatUrl)
        urls.append(contentsOf: item.thirdShowStatUrls)
        urls = urls.filter { !$0.isEmpty }
        // 保证序列发送不为空
        guard urls.isEmpty == false else { return }
        // 异步发送事件
        DispatchQueue.global(qos: .background).async {
            for url in urls { XMLADXReportManager.sendThirdURLReport(url, item: nil) }
        }
    }
    
    // ADX返回的第三方点击（thirdClickStatUrls)上报逻辑
    public static func sendThirdClickReportInfo(_ item: XMLAdTypeItemModel?) {
        guard let `item` = item else { return }
        var urls: [String] = []
        urls.append(contentsOf: item.thirdClickStatUrls)
        urls = urls.filter { !$0.isEmpty }
        // 保证序列发送不为空
        guard urls.isEmpty == false else { return }
        // 异步发送事件
        DispatchQueue.global(qos: .background).async {
            for url in urls { XMLADXReportManager.sendThirdURLReport(url, item: item) }
        }
    }
    
    // 链接地址参数替换并发送
    public static func sendThirdURLReport(_ url: String, item: XMLAdTypeItemModel?) {
        guard url.count > 0 else { return }
        let sendURL: String = XMLADXUtils.stringByRepalceADParamsWithURL(url, item: item)
        // 保证待传入地址可以生成URL(预防三方链接过长)
        guard let urlInfos = String.disassemble(sendURL), URL(string: urlInfos.domain) != nil else {
            // 广告三方上报异常  其他事件
            XMEventLog.logEventWithId(42413, serviceId: "others", properties: ["adId"        : item?.adid ?? 0,
                                                                               "slotId"      : item?.dspPositionId ?? 0,
                                                                               "positionName": item?.xml_posiName ?? "",
                                                                               "error_msg"   : "地址拆分有误",
                                                                               "sdkType"     : item?.adtype ?? -1,
                                                                               "itemUrl"     : sendURL,
                                                                               "srcPageUrl"  : url,
                                                                               "type"        : "2"])
            return
        }
        // 三方上报逻辑处理
        if XMLADXConfig.kEnableThirdStatCookie() {
            _ = XMNetwork().customHeader(["User-Agent": XMConfig.systemUserAgent()])
                .get(urlInfos.domain, path: urlInfos.paramString.isEmpty ? "" : "?\(urlInfos.paramString)", parameters: nil)
                .subscribe(onNext: nil, onError: { err in
                    XMEventLog.logEventWithId(42413, serviceId: "others", properties: ["adId"        : item?.adid ?? 0,
                                                                                       "slotId"      : item?.dspPositionId ?? 0,
                                                                                       "positionName": item?.xml_posiName ?? "",
                                                                                       "error_msg"   : err.localizedDescription,
                                                                                       "sdkType"     : item?.adtype ?? -1,
                                                                                       "itemUrl"     : sendURL,
                                                                                       "srcPageUrl"  : url,
                                                                                       "type"        : "1"])
                }, onCompleted: nil, onDisposed: nil)
        } else {
            if let sendValue = URL(string: sendURL) {
                var request: URLRequest = URLRequest(url: sendValue)
                request.httpMethod      = "GET"
                request.setValue(XMConfig.systemUserAgent(), forHTTPHeaderField: "User-Agent")
                let dataTask: URLSessionDataTask = URLSession.shared.dataTask(with: request) { _, _, error in
                    guard let value = error else { return }
                    // 广告三方上报异常  其他事件
                    XMEventLog.logEventWithId(42413, serviceId: "others", properties: ["adId"        : item?.adid ?? 0,
                                                                                       "slotId"      : item?.dspPositionId ?? 0,
                                                                                       "positionName": item?.xml_posiName ?? "",
                                                                                       "error_msg"   : value.localizedDescription,
                                                                                       "sdkType"     : item?.adtype ?? -1,
                                                                                       "itemUrl"     : sendURL,
                                                                                       "srcPageUrl"  : url,
                                                                                       "type"        : "0"])
                }
                dataTask.resume()
            } else {
                // 广告三方上报异常  其他事件
                XMEventLog.logEventWithId(42413, serviceId: "others", properties: ["adId"        : item?.adid ?? 0,
                                                                                   "slotId"      : item?.dspPositionId ?? 0,
                                                                                   "positionName": item?.xml_posiName ?? "",
                                                                                   "error_msg"   : "地址不能转成URL",
                                                                                   "sdkType"     : item?.adtype ?? -1,
                                                                                   "itemUrl"     : sendURL,
                                                                                   "srcPageUrl"  : url,
                                                                                   "type"        : "0"])
            }
        }
    }
    
    // showTime上报逻辑
    public static func sendShowTimeReportInfo(_ item: XMLAdTypeItemModel?, playTime: TimeInterval) {
        guard let `item` = item as? XMLPaidUnlockAdTypeItemModel else { return }
        var dic: [String: Any] = item.toShowTimeParams("show", playTime: playTime)
        let tokenInfo = item.xml_validShowTokenInfo()
        if tokenInfo.needMore == false {
            dic["showToken"] = String.adxDecryption(with: tokenInfo.token)
            XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)
        } else {
            XMLADXReportManager.loadMoreValidToken { (token) in
                guard let `token` = token else { return }
                dic["showToken"] = String.adxDecryption(with: token)
                XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)
            }
        }
    }
    
    // showOb上报逻辑
    public static func sendShowObReportInfo(_ item: XMLAdTypeItemModel?, isFinish: Bool) {
        guard let `item` = item, !item.xml_isShowObReported else { return }
        print(">|<", "sendShowObReportInfo")
        var dic: [String: Any] = item.toStaticParams("show", logType: "showOb")
        dic["positionId"] = item.xml_positionId
        dic["promptSuc"] = isFinish ? 0 : 1
        // 视频贴片跳过需附加额外参数
        if item.xml_showType().isInterruptPatch || item.xml_showType().isAutoPlayVideo {
            item.xml_isShowObReported = true
            dic["skipAd"]   = item.xml_manualSkip ? 0 : 1
            dic["skipTime"] = Int64((item.xml_endTime - item.xml_playTime) * 1000.0) // 跳过时间: 单位ms
        }
        let tokenInfo = item.xml_validShowTokenInfo()
        if tokenInfo.needMore == false {
            dic["showToken"] = String.adxDecryption(with: tokenInfo.token)
            XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)
        } else {
            XMLADXReportManager.loadMoreValidToken { (token) in
                guard let `token` = token else { return }
                dic["showToken"] = String.adxDecryption(with: token)
                XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)
            }
        }
    }
    
    // activityShow上报逻辑
    public static func sendActivityShowReportInfo(_ item: XMLAdTypeItemModel?, extraInfo: [String: Any]? = nil) {
        guard let `item` = item else { return }
        var dic: [String: Any] = item.toStaticParams("show", logType: "activityShow")
        // 额外信息附加
        if let addition = extraInfo {
            for key in addition.keys {
                dic[key] = addition[key]
            }
        }
        // 额外信息处理
        dic["responseId"]      = ""
        dic["positionId"]      = item.xml_positionId
        // 限定仅sub_gamecenter_video广告位上报👇字段
        if item.xml_posiName == "sub_gamecenter_video" {
            dic["calculateMethod"] = "cpm"
        }
        let tokenInfo = item.xml_validShowTokenInfo()
        if tokenInfo.needMore == false {
            dic["showToken"] = String.adxDecryption(with: tokenInfo.token)
            XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)
        } else {
            XMLADXReportManager.loadMoreValidToken { (token) in
                guard let `token` = token else { return }
                dic["showToken"] = String.adxDecryption(with: token)
                XMBehaviorManager.shared().postOnlineData(dic: dic, param: nil, type: .ad)
            }
        }
    }
    
    // 喜马激励视频播放上报逻辑 status 1：开始播放 2：播放完成 3：广告关闭，手动关闭 4：广告不可见 5：播放失败
    public static func sendVideoRecordReportInfo(_ item: XMLAdTypeItemModel?, status: Int, currTime: TimeInterval) {
        guard let `item` = item else { return }
        var dic: [String: Any] = item.toVideoParams()
        dic["playStatus"]      = status
        dic["playMs"]          = Int64(currTime * 1000.0)
        XMLXlog.LOG_INFO("XmAd", subModule: "videoRecord", format: NSDictionary(dictionary: dic))
    }
}

// MARK: - ADX APM统计
extension XMLADXReportManager {
    // 上报冷启动请求时间至APM 单位s
    static func sendCoolSplashADXReqToAPM(_ interval: TimeInterval) {
        XMStartupMonitor.monitor().addLaunchInfo(["adApiLoadTime": interval * 1000])
    }
    
    // 上报冷启动SDK渲染及竞价时间至APM 单位s
    static func sendCoolSplashADXLoginToAPM(_ interval: TimeInterval) {
        XMStartupMonitor.monitor().addLaunchInfo(["adSdkLoadTime": interval * 1000])
    }
    
    // 上报冷启动喜马广告资源下载时间至APM 单位s
    static func sendCoolSplashResDownloadToAPM(_ interval: TimeInterval) {
        XMStartupMonitor.monitor().addLaunchInfo(["adResLoadTime": interval * 1000])
    }
    
    // 上报冷启动展示时间
    static func sendCoolSplashShowToAPM() {
        guard let moment = XMLADXManager.shared().coolSplashShowMoment else { return }
        let interval: TimeInterval = Date().timeIntervalSince1970 - moment
        XMStartupMonitor.monitor().addLaunchInfo(["adShowTime": interval])
        XMLADXManager.shared().coolSplashShowMoment = nil
    }
}

// MARK: - 辅助 Method
extension XMLADXReportManager {
    // 获取额外token
    public static func loadMoreValidToken(_ completion: ((String?) -> Void)? = nil) {
        let domain = "\(xmEnvironmentUrl(kAdSeServerUrl)!)nonce/\(Int64(Date().timeIntervalSince1970))"
        _ = XMNetwork.get(domain, parameters: ["count": 1]).subscribe(onNext: { response in
            if response.success, let json = response.network.json {
                let responseJSON = JSON(json)
                let nonces: [String] = responseJSON["data"]["nonces"].arrayValue.map { $0["nonce"].stringValue }.filter { !$0.isEmpty }
                completion?(nonces.first)
            } else {
                completion?(nil)
            }
        })
    }
}

// 穿山甲信息流素材拓展
extension BUNativeAd {
    func xml_material() -> [String: Any] {
        let url: [String] = self.data?.imageAry?.map({ $0.imageURL ?? "" }) ?? []
        return ["url": url,
                "coverUrl": url.first ?? "",
                "title": self.data?.adTitle ?? "",
                "desc": self.data?.adDescription ?? "",
                "videoUrl": "",
                "iconUrl": self.data?.icon?.imageURL ?? "",
                "source": self.data?.source ?? "",
                "clickAction": self.data?.buttonText ?? "",
                "style": 2]
    }
    
    // 推广类型
    func xml_promotionGoal() -> Int {
        return (self.data?.interactionType == BUInteractionType.download) ? 1 : 2
    }
}

// 广点通信息流素材拓展
extension GDTUnifiedNativeAdDataObject {
    func xml_material() -> [String: Any] {
        let url: [String] = [""]
        return ["url": url,
                "coverUrl": url.first ?? "",
                "title": self.title ?? "",
                "desc": self.desc ?? "",
                "videoUrl": "",
                "iconUrl": self.iconUrl ?? "",
                "source": "",
                "clickAction": "",
                "style": 2]
    }
    
    // 推广类型
    func xml_promotionGoal() -> Int {
        return self.isAppAd ? 1 : 2
    }
}

// 百度信息流素材拓展
extension BaiduMobAdNativeAdObject {
    func xml_material() -> [String: Any] {
        let url: [String] = [self.mainImageURLString ?? ""]
        return ["url": url,
                "coverUrl": url.first ?? "",
                "title": self.title ?? "",
                "desc": self.text ?? "",
                "videoUrl": self.videoURLString ?? "",
                "iconUrl": "",
                "source": self.brandName ?? "",
                "clickAction": "",
                "style": 2]
    }
    
    // 推广类型
    func xml_promotionGoal() -> Int {
        return self.actType == BaiduMobNativeAdActionType.init(rawValue: 2) ? 1 : 2
    }
}

extension JADNativeAd {
    func xml_material() -> [String: Any] {
        let adData = self.data?.first
        let url: [String] = adData?.adImages ?? []
        return ["url": url,
                "coverUrl": adData?.adImages.first ?? "",
                "title": adData?.adTitle ?? "",
                "desc": adData?.adDescription ?? "",
                "videoUrl": "",
                "iconUrl": "",
                "source": adData?.adResource ?? "",
                "clickAction": "",
                "style": 2]
    }
    
    // 推广类型
    func xml_promotionGoal() -> Int {
        return 2
    }
}
