//
//  XMLADXPretreater.swift
//  XMADXModule
//
//  Created by yangle<PERSON> on 2020/8/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation

// 预处理器
public class XMLADXPretreater: NSObject {
    // MARK: - 预加载状态
    /// 预加载是否完成
    var preReady  : Bool                   = false
    /// 预加载成功的广告对象
    var preloadObj: XMLADXLoaderItem?      = nil
    /// 预加载失败的广告项列表
    var preErrors : [XMLADXLoaderItem]     = []
    
    // MARK: - ADX网络请求状态  
    /// ADX网络请求是否完成
    var rBackReady : Bool                  = false
    /// ADX返回的广告数据项列表
    var rBackItems : [XMLAdTypeItemModel]  = []
    /// ADX请求是否成功
    var rBackResult: Bool                  = false
    /// ADX请求完成的时间戳
    var rBackTime  : TimeInterval          = 0.0
    
    // MARK: - 处理控制
    /// 是否已经处理完成（防重复处理）
    var isHandled  : Bool                  = false
    
    // MARK: - 并发控制
    /// 并发加载任务组，用于协调预加载和ADX请求
    var loaderGroup: XMLADXSafetyGroup     = XMLADXSafetyGroup()
}

extension XMLADXPretreater {
    // 类方法请求
    func preTreat(at position : XMLAdRequestPositionType,
                  type        : XMLADXLoaderType,
                  extraInfo   : [String: Any]? = nil,
                  filter      : (([XMLAdTypeItemModel]) -> [XMLAdTypeItemModel])? = nil,
                  asyncHandle : (([XMLAdTypeItemModel], Bool, TimeInterval) -> Void)? = nil,
                  directHandle: XMLADXLoaderManagerCompletion = nil) {
        // 预处理广告数据
        var preADXItems: [XMLAdTypeItemModel] = XMLADXPreDataManager.getAndCleanItemsFromCache(position.name)
        // 场景初步筛选
        if let filter = filter, preADXItems.count > 0 { preADXItems = filter(preADXItems) }
        // 预处理广告数据ADID(用,分隔)(现在只处理一个)
        var preRAdIds: String = preADXItems.first?.adid ?? ""
        // 预加载数据过滤(穿山甲开屏、百度、京东开屏不支持预加载)
        if type == .splash, let adxItemAdType = preADXItems.first?.xml_AdType(), ((adxItemAdType.isBU && !XMLADXConfig.enableBUSplashPretreat()) || adxItemAdType.isBD || adxItemAdType.isJD) {
            preADXItems.removeAll()
            preRAdIds = ""
        }
        
        guard !preADXItems.isEmpty else {
            XMLADXUtils.consoleLog("\(position.name)-缓存物料为空-请求ADX")
            XMLAdRequestManager.requestAd(at: position, extraInfo: extraInfo) { (items, isSuccess, reqTime) in
                guard isSuccess, items.count > 0 else { asyncHandle?(items, isSuccess, reqTime);return }
                // 数据分拣, 下次SDK预加载的内容
                XMLADXPreDataManager.addItemToCache(items.filter { $0.isNeedPreRequest && !$0.xml_showType().isInterruptPatch })
                // 场景数据筛选
                let resultItems: [XMLAdTypeItemModel] = filter?(items) ?? items
                // 当次SDK处理的内容
                asyncHandle?(resultItems, isSuccess, reqTime)
            }
            return
        }
        
        loaderGroup.enter()
        DispatchQueue.global(qos: .default).async(group: loaderGroup.obj(), qos: .default, flags: []) { [weak self] in
            XMLADXUtils.consoleLog("\(position.name)-缓存物料有数据-请求ADX")
            XMLAdRequestManager.requestAd(at: position, preRAdIds: preRAdIds, extraInfo: extraInfo) { [weak self] (items, isSuccess, reqTime) in
                guard let wself = self else { return }
                // 下次SDK预加载的内容
                XMLADXPreDataManager.addItemToCache(items.filter { $0.isNeedPreRequest && !$0.xml_showType().isInterruptPatch })
                // 场景数据筛选
                let resultItems: [XMLAdTypeItemModel] = filter?(items) ?? items
                wself.rBackReady  = true
                wself.rBackItems  = resultItems
                wself.rBackResult = isSuccess
                wself.rBackTime   = reqTime
                wself.loaderGroup.leave()
                guard wself.preReady == false else { return }
                // 接口返回失败、返回空数组、预加载非高优先级则直接返回
                guard isSuccess, let adid = resultItems.first?.adid, adid == preRAdIds else { wself.loaderGroup.leave(); return }
            }
        }
        loaderGroup.enter()
        let limitMaxTime = Self.preprocessADXPreLoaderMaxLimitTime(type)
        DispatchQueue.global(qos: .default).async(group: loaderGroup.obj(), qos: .default, flags: []) { [weak self] in
            XMLADXUtils.consoleLog("\(position.name)-缓存物料有数据-处理缓存")
            // 并发请求广告及从ADX获取当前内容
            XMLADXPreLoaderManager.shared().prepareLoader(type, items: preADXItems, limitMaxTime: limitMaxTime) { [weak self] (obj, errItems) in
                guard let wself = self, wself.isHandled == false else { // 如果当前逻辑不生效则直接添加到缓存
                    if let loadItem = obj { loadItem.item?.xml_index = Int.max; XMLADXCacheManager.addLoaderItemToCache(loadItem) }
                    return
                }
                wself.preReady   = true
                wself.preloadObj = obj
                wself.preErrors  = errItems
                wself.loaderGroup.leave()
            }
        }
        // 任务执行完成
        loaderGroup.obj().notify(queue: .main) { [weak self] in
            XMLADXUtils.consoleLog("\(position.name)-缓存物料有数据-处理缓存完毕-ADX请求完毕")
            guard let wself = self, wself.isHandled == false else { return }
            wself.isHandled = true
            // 如果预加载所返回的数据在ADX列表内并且是第一位则直接处理
            if wself.rBackResult, let obj = wself.preloadObj, let objAdid = obj.item?.adid, let adid = wself.rBackItems.first?.adid, adid == objAdid {
                directHandle?(obj, [])
            } else {
                // 如果不用预加载数据则添加到缓存中
                if wself.preReady, let obj = wself.preloadObj { obj.item?.xml_index = Int.max; XMLADXCacheManager.addLoaderItemToCache(obj) }
                // 如果预请求失败则进行数据筛选(剔除SDK主动拒绝的)
                if wself.rBackItems.count > 0, wself.rBackResult, wself.preReady, wself.preloadObj == nil,
                   let errorItem = wself.preErrors.first, errorItem.isSDKRefuse {
                    var validItems  : [XMLAdTypeItemModel] = []
                    var unValidItems: [XMLAdTypeItemModel] = []
                    wself.rBackItems.forEach {
                        if $0.dspPositionId != errorItem.slotId { validItems.append($0) } else { unValidItems.append($0) }
                    }
                    wself.rBackItems  = validItems
                    wself.rBackResult = validItems.count > 0
                    // 未请求的数据进行数据上报
                    wself.reportSDKRefuseItems(unValidItems)
                    wself.preErrors.removeAll()
                }
                // 当次SDK处理的内容
                asyncHandle?(wself.rBackItems, wself.rBackResult, wself.rBackTime)
            }
        }
    }
    
    // 预处理当前最新的最大超时时间(客户端容错)
    fileprivate class func preprocessADXPreLoaderMaxLimitTime(_ type: XMLADXLoaderType) -> Double {
        var maxLimitTime: Double = XMLADXConfig.kPreTreatLimitMaxTimeFlow()
        // 从配置中心获取各个类型获取最大加载时间
        if type == .splash {
            maxLimitTime = XMLADXConfig.kPreTreatLimitMaxTimeSplash()
        } else if type == .video {
            maxLimitTime = XMLADXConfig.kPreTreatLimitMaxTimeVideo()
        }
        // 预加载的最大加载时间调整至最低3.5s
        maxLimitTime = max(maxLimitTime, 3.5)
        return maxLimitTime
    }
    
    // 上报SDK主动拒绝的物料
    fileprivate func reportSDKRefuseItems(_ items: [XMLAdTypeItemModel]) {
        guard items.count > 0 else { return }
        DispatchQueue.global(qos: .background).async {
            let currentTime: TimeInterval = Date().timeIntervalSince1970
            items.forEach {
                // 数据源阶段重置
                $0.xml_loaderPhase = .asyncLoader
                XMLADXLoaderItem.uploadADXReqFailureStatisticInfo(6001, currentTime: currentTime, obj: $0)
            }
        }
    }
}
