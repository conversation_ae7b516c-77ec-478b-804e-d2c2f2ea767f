//
//  XMLADXLoader.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import XMConfigModule
import BUAdSDK
import GDTMobSDK
import BaiduMobAdSDK
import JADYun
import RouterModule

// 广告ADX数据加载器类型
public enum XMLADXLoaderType {
    case splash // 启动图
    case video  // 激励视频
    case flow   // 信息流
    case plaque // 插屏
}

@objc protocol XMLADXLoaderDelegate {
    // adx数据解析并获取成功
    @objc optional func adxLoader(_ loader: XMLADXLoader, didSuccessWith dataSource: XMLADXLoaderItem)
    // adx数据解析并获取失败
    @objc optional func adxLoader(_ loader: XMLADXLoader, didFailureWith error: Error?)
}

// SDK广告素材加载器
class XMLADXLoader: NSObject {
    var item         : XMLAdTypeItemModel
    var type         : XMLADXLoaderType
    weak var delegate: XMLADXLoaderDelegate?   = nil
    var data         : XMLADXLoaderItem        = XMLADXLoaderItem()
    
    // 是否为竞价模式
    var isBiddingMode: Bool = false

    var buTransfer   : XMLADXLoaderBUTransfer  = XMLADXLoaderBUTransfer()
    var gdtTransfer  : XMLADXLoaderGDTTransfer = XMLADXLoaderGDTTransfer()
    var drTransfer   : XMLADXLoaderDRTransfer  = XMLADXLoaderDRTransfer()
    var bdTransfer   : XMLADXLoaderBDTransfer  = XMLADXLoaderBDTransfer()
    var xmlTransfer  : XMLADXLoaderDRTransfer  = XMLADXLoaderDRTransfer()
    var jdTransfer   : XMLADXLoaderJDTransfer  = XMLADXLoaderJDTransfer()

    init(with item: XMLAdTypeItemModel, type: XMLADXLoaderType) {
        self.item             = item
        self.type             = type
        self.data.item        = item
        self.drTransfer.type  = type
        self.xmlTransfer.type = type
        super.init()
    }
    
    // 启动图请求
    var s_reqBuAd    : BUSplashAd?     = nil
    var s_reqGDTAd   : GDTSplashAd?        = nil
    var s_reqDRAd    : XMLDRNativeAd?      = nil
    var s_reqBdAd    : BaiduMobAdSplash?   = nil
    var s_reqJDAd    : JADSplashView?         = nil

    // 激励视频请求
    var v_reqBuNEAd  : BUNativeExpressRewardedVideoAd?   = nil
    var v_reqBuNEFAd : BUNativeExpressFullscreenVideoAd? = nil
    var v_reqGDTAd   : GDTRewardVideoAd?                 = nil
    var v_reqDRAd    : XMLDRRewardVideoAd?               = nil
    var v_reqBdAd    : BaiduMobAdRewardVideo?            = nil

    // 信息流请求
    var f_reqBuAd       : BUNativeAdsManager?       = nil
    weak var f_reqBuNEAd: BUNativeExpressAdManager? = nil
    var f_reqGDTAd      : GDTUnifiedNativeAd?       = nil
    var f_reqDRAd       : XMLDRNativeAd?            = nil
    weak var f_reqBdAd  : BaiduMobAdNative?         = nil
    var f_reqXMLAd      : XMLDRNativeAd?            = nil
    var f_reqJDAd       : JADNativeAd?              = nil

    // 贴片请求
    var p_reqBuNEFAd : BUNativeExpressFullscreenVideoAd? = nil
    var p_reqGDTAd   : GDTUnifiedInterstitialAd?         = nil
    var p_reqDRAd    : XMLDRNativeAd?                    = nil
    var p_reqBUNaAd  : BUNativeAdsManager?               = nil
    var p_reqGDTNaAd : GDTUnifiedNativeAd?               = nil
}

// MARK: - 对外调用 Method
extension XMLADXLoader {
    // 解析ADX数据并加载广告
    public func load() {
        if type == .splash && item.xml_AdType().isBU { // 穿山甲启动图
            DispatchQueue.main.async { [weak self] in self?.prepareSBuAdReqAndStart() }
        } else if type == .splash && item.xml_AdType().isGDT { // 广点通启动图
            DispatchQueue.main.async { [weak self] in self?.prepareSGDTAdReqAndStart() }
        } else if type == .splash && item.xml_AdType().isHimalaya { // 喜马拉雅启动图
            DispatchQueue.main.async { [weak self] in self?.prepareSDRAdReqAndStart() }
        } else if type == .splash && item.xml_AdType().isBD { // 百度百青藤启动图
            DispatchQueue.main.async { [weak self] in self?.prepareSBdAdReqAndStart() }
        } else if type == .splash && item.xml_AdType().isJD { // 京东启动图
            DispatchQueue.main.async { [weak self] in self?.prepareSJDAdReqAndStart() }
        } else if type == .video && item.xml_AdType().isBU && item.xml_isTemplate() && !item.xml_showType().isFullVideo {  // 穿山甲模版激励视频
            DispatchQueue.main.async { [weak self] in self?.prepareVBuNEAdReqAndStart() }
        } else if type == .video && item.xml_AdType().isBU && item.xml_showType().isFullVideo {  // 穿山甲模版全屏视频
            DispatchQueue.main.async { [weak self] in self?.prepareVBuNEFAdReqAndStart() }
        } else if type == .video && item.xml_AdType().isGDT { // 广点通激励视频
            DispatchQueue.main.async { [weak self] in self?.prepareVGDTAdReqAndStart() }
        } else if type == .video && item.xml_AdType().isHimalaya { // 喜马拉雅激励视频
            DispatchQueue.main.async { [weak self] in self?.prepareVDRAdReqAndStart() }
        } else if type == .video && item.xml_AdType().isBD { // 百度百青藤激励视频
            DispatchQueue.main.async { [weak self] in self?.prepareVBdAdReqAndStart() }
        } else if type == .flow && item.xml_AdType().isBU && !item.xml_isTemplate() { // 穿山甲信息流非模版
            DispatchQueue.main.async { [weak self] in self?.prepareFBuAdReqAndStart() }
        } else if type == .flow && item.xml_AdType().isBU && item.xml_isTemplate() { // 穿山甲信息流模版渲染
            DispatchQueue.main.async { [weak self] in self?.prepareFBuNEAdReqAndStart() }
        } else if type == .flow && item.xml_AdType().isGDT { // 广点通信息流
            DispatchQueue.main.async { [weak self] in self?.prepareFGDTAdReqAndStart() }
        } else if type == .flow && item.xml_AdType().isHimalaya { // 喜马拉雅信息流
            DispatchQueue.main.async { [weak self] in self?.prepareFDRAdReqAndStart() }
        } else if type == .flow && item.xml_AdType().isBD { // 百度百青藤信息流
            DispatchQueue.main.async { [weak self] in self?.prepareFBdAdReqAndStart() }
        } else if type == .flow && item.xml_AdType().isXML { // 喜马拉雅(SDK)信息流
            DispatchQueue.main.async { [weak self] in self?.prepareFXMLAdReqAndStart() }
        } else if type == .flow && item.xml_AdType().isJD { // 京东(SDK)信息流
            DispatchQueue.main.async { [weak self] in self?.prepareFJDAdReqAndStart() }
        } else if type == .plaque && item.xml_AdType().isBU && item.xml_isTemplate() {  // 穿山甲模版插屏
            DispatchQueue.main.async { [weak self] in self?.preparePBuNEFAdReqAndStart() }
        } else if type == .plaque && item.xml_AdType().isGDT && item.xml_isTemplate() {             // 广点通模版插屏
            DispatchQueue.main.async { [weak self] in self?.preparePGDTAdReqAndStart() }
        } else if type == .plaque && item.xml_AdType().isHimalaya {                                 // 喜马拉雅自渲染插屏
            DispatchQueue.main.async { [weak self] in self?.preparPDRAdReqAndStart() }
        } else if type == .plaque && item.xml_AdType().isBU && item.xml_showType() == .lanPlaque {  // 穿山甲自渲染插屏
            DispatchQueue.main.async { [weak self] in self?.preparPBUNaAdReqAndStart()}
        } else if type == .plaque && item.xml_AdType().isGDT && item.xml_showType() == .lanPlaque { // 广点通自渲染插屏
            DispatchQueue.main.async { [weak self] in self?.preparPGDTNaAdReqAndStart()}
        }
        // SDK激励视频请求发出 status(13) 上报
        if type == .video { self.data.uploadStatisticInfo(13) }
    }
    
    // 解析ADX数据并加载竞价数据
    public func bidToken() -> String? {
        if type == .splash && item.xml_AdType().isBU { // 穿山甲启动图
            return self.prepareSBuAdBidToken()
        } else if type == .splash && item.xml_AdType().isGDT { // 广点通启动图
            return self.prepareSGDTAdBidToken()
        } else if type == .video && item.xml_AdType().isBU && item.xml_isTemplate() && !item.xml_showType().isFullVideo {  // 穿山甲模版激励视频
            return self.prepareVBuNEAdBidToken()
        } else if type == .video && item.xml_AdType().isBU && item.xml_showType().isFullVideo {  // 穿山甲模版全屏视频
            return self.prepareVBuNEFAdBidToken()
        } else if type == .video && item.xml_AdType().isGDT { // 广点通激励视频
            return self.prepareVGDTAdBidToken()
        } else if type == .flow && item.xml_AdType().isBU && !item.xml_isTemplate() { // 穿山甲信息流非模版
            return self.prepareFBuAdBidToken()
        } else if type == .flow && item.xml_AdType().isBU && item.xml_isTemplate() { // 穿山甲信息流模版渲染
            return self.prepareFBuNEAdBidToken()
        } else if type == .flow && item.xml_AdType().isGDT { // 广点通信息流
            return self.prepareFGDTAdBidToken()
        } else if type == .plaque && item.xml_AdType().isBU && item.xml_isTemplate() {  // 穿山甲模版插屏
            return self.preparePBuNEFAdBidToken()
        } else if type == .plaque && item.xml_AdType().isGDT && item.xml_isTemplate() {  // 广点通模版插屏
            return self.preparePGDTAdBidToken()
        }
        return nil
    }
}

// MARK: - 启动 Method
extension XMLADXLoader {
    // 解析ADX数据并启动穿山甲SDK获取启动图数据
    func prepareSBuAdReqAndStart() {
        buTransfer.data = data
        buTransfer.delegate = self
        
        s_reqBuAd = instanceBUSplashAdView()
        s_reqBuAd?.delegate = buTransfer
        if item.slotRealBid, item.slotAdm.count > 0 {
            s_reqBuAd?.setAdMarkup(item.slotAdm)
        } else {
            s_reqBuAd?.loadData()
        }
    }
    
    // 解析ADX数据并启动广点通SDK获取启动图数据
    func prepareSGDTAdReqAndStart() {
        gdtTransfer.data = data
        gdtTransfer.delegate = self
        
        s_reqGDTAd = instanceGDTSplashAd()
        s_reqGDTAd?.delegate = gdtTransfer
        s_reqGDTAd?.load()
    }
    
    // 解析ADX数据并启动喜马拉雅获取启动图数据
    func prepareSDRAdReqAndStart() {
        drTransfer.data = data
        drTransfer.delegate = self
        
        s_reqDRAd = XMLDRNativeAd.ad(with: item, type: .splash)
        s_reqDRAd?.delegate = drTransfer
        s_reqDRAd?.loadAdData(3)
    }
    
    // 解析ADX数据并启动百度百青藤SDK获取启动图数据
    func prepareSBdAdReqAndStart() {
        bdTransfer.data = data
        bdTransfer.delegate = self
        
        data.s_reqBdAdBox = instanceBDSplashAdBoxView()
        s_reqBdAd = instanceBDSplashAdView()
        s_reqBdAd?.delegate = bdTransfer
        if let container = data.s_reqBdAdBox {
            s_reqBdAd?.loadAndDisplay(usingContainerView: container)
        }
    }
    
    // 解析ADX数据并启动京东SDK获取启动图数据
    func prepareSJDAdReqAndStart() {
        jdTransfer.data = data
        jdTransfer.delegate = self
        
        s_reqJDAd = instanceJDSplashView()
        s_reqJDAd?.delegate = jdTransfer
        s_reqJDAd?.loadAdData()
    }
    
    // 解析ADX数据并启动穿山甲SDK获取模版激励视频数据
    func prepareVBuNEAdReqAndStart() {
        buTransfer.data = data
        buTransfer.delegate = self
        
        v_reqBuNEAd = instanceBUNERewardedVideoAd()
        v_reqBuNEAd?.delegate = buTransfer
        if item.slotRealBid, item.slotAdm.count > 0 {
            v_reqBuNEAd?.setAdMarkup(item.slotAdm)
        } else {
            v_reqBuNEAd?.loadData()
        }
    }
    
    // 解析ADX数据并启动穿山甲SDK获取模版全屏视频数据
    func prepareVBuNEFAdReqAndStart() {
        buTransfer.data = data
        buTransfer.delegate = self
        
        v_reqBuNEFAd = instanceBUNEFullscreenVideoAd()
        v_reqBuNEFAd?.delegate = buTransfer
        if item.slotRealBid, item.slotAdm.count > 0 {
            v_reqBuNEFAd?.setAdMarkup(item.slotAdm)
        } else {
            v_reqBuNEFAd?.loadData()
        }
    }
    
    // 解析ADX数据并启动广点通SDK获取激励视频数据
    func prepareVGDTAdReqAndStart() {
        gdtTransfer.data = data
        gdtTransfer.delegate = self
        
        v_reqGDTAd = instanceGDTRewardVideoAd()
        v_reqGDTAd?.delegate = gdtTransfer
        v_reqGDTAd?.load()
    }
    
    // 解析ADX数据并启动喜马拉雅获取激励视频数据
    func prepareVDRAdReqAndStart() {
        drTransfer.data = data
        drTransfer.delegate = self
        
        v_reqDRAd = XMLDRRewardVideoAd(adModel: item)
        v_reqDRAd?.delegate = drTransfer
        v_reqDRAd?.loadData()
    }
    
    // 解析ADX数据并启动百度百青藤获取激励视频数据
    func prepareVBdAdReqAndStart() {
        bdTransfer.data = data
        bdTransfer.delegate = self
        
        v_reqBdAd = instanceBaiduMobAdRewardVideo()
        v_reqBdAd?.delegate = bdTransfer
        v_reqBdAd?.load()
    }
    
    // 解析ADX数据并启动穿山甲SDK获取信息流数据
    func prepareFBuAdReqAndStart() {
        buTransfer.data = data
        buTransfer.delegate = self
        
        f_reqBuAd = instanceBUNativeAd(slotID: kBUAdSDK_DefaultFeed_SlotId)
        f_reqBuAd?.delegate = buTransfer
        if item.slotRealBid, item.slotAdm.count > 0 {
            f_reqBuAd?.setAdMarkup(item.slotAdm)
        } else {
            f_reqBuAd?.loadAdData(withCount: 1)
        }
    }
    
    // 解析ADX数据并启动穿山甲SDK获取信息流数据(模版渲染)
    func prepareFBuNEAdReqAndStart() {
        buTransfer.data = data
        buTransfer.delegate = self
        
        data.f_reqBuNEAd = instanceBUNEAdManager()
        f_reqBuNEAd = data.f_reqBuNEAd
        f_reqBuNEAd?.delegate = buTransfer
        if item.slotRealBid, item.slotAdm.count > 0 {
            f_reqBuNEAd?.setAdMarkup(item.slotAdm)
        } else {
            f_reqBuNEAd?.loadAdData(withCount: 1)
        }
    }
    
    // 解析ADX数据并启动广点通SDK获取信息流数据
    func prepareFGDTAdReqAndStart() {
        gdtTransfer.data = data
        gdtTransfer.delegate = self
        
        f_reqGDTAd = instanceGDTUnifiedNativeAd(placementId: kGDTAdSDK_DefaultFeed_PlacementId)
        f_reqGDTAd?.delegate = gdtTransfer
        f_reqGDTAd?.load()
    }
    
    // 解析ADX数据并启动喜马拉雅获取信息流数据
    func prepareFDRAdReqAndStart() {
        drTransfer.data = data
        drTransfer.delegate = self
        
        f_reqDRAd = XMLDRNativeAd.ad(with: item, type: .patch)
        f_reqDRAd?.delegate = drTransfer
        f_reqDRAd?.loadAdData(5)
    }
    
    // 解析ADX数据并启动百度百青藤SDK获取信息流数据
    func prepareFBdAdReqAndStart() {
        data.bdTransfer.data = data
        data.bdTransfer.delegate = self
        
        data.f_reqBdAd = instanceBDNativeAd()
        data.f_reqBdAd?.adDelegate = data.bdTransfer
        f_reqBdAd = data.f_reqBdAd
        f_reqBdAd?.requestAds()
    }
    
    // 解析ADX数据并启动喜马拉雅(SDK)获取信息流数据
    func prepareFXMLAdReqAndStart() {
        xmlTransfer.data = data
        xmlTransfer.delegate = self
        
        f_reqXMLAd = XMLDRNativeAd.ad(with: item, type: .patch)
        f_reqXMLAd?.delegate = xmlTransfer
        f_reqXMLAd?.loadAdData(5)
    }
    
    // 解析ADX数据并启动京东(SDK)获取信息流数据
    func prepareFJDAdReqAndStart() {
        jdTransfer.data = data
        jdTransfer.delegate = self
        
        f_reqJDAd = instanceJADNativeAd()
        f_reqJDAd?.delegate = jdTransfer
        f_reqJDAd?.loadData()
    }
    
    // 解析ADX数据并启动穿山甲SDK获取模版插屏数据
    func preparePBuNEFAdReqAndStart() {
        buTransfer.data = data
        buTransfer.delegate = self
        
        p_reqBuNEFAd = instancePBUNEFullscreenVideoAd()
        p_reqBuNEFAd?.delegate = buTransfer
        if item.slotRealBid, item.slotAdm.count > 0 {
            p_reqBuNEFAd?.setAdMarkup(item.slotAdm)
        } else {
            p_reqBuNEFAd?.loadData()
        }
    }
    
    // 解析ADX数据并启动广点通SDK获取插屏数据
    func preparePGDTAdReqAndStart() {
        gdtTransfer.data = data
        gdtTransfer.delegate = self
        
        p_reqGDTAd = instanceGDTUnifiedInterstitialAd()
        p_reqGDTAd?.delegate = gdtTransfer
        p_reqGDTAd?.load()
    }
    
    // 解析ADX数据并启动喜马拉雅SDK获取插屏数据
    func preparPDRAdReqAndStart() {
        drTransfer.data = data
        drTransfer.delegate = self
        
        p_reqDRAd = XMLDRNativeAd.ad(with: item, type: .patch)
        p_reqDRAd?.delegate = drTransfer
        p_reqDRAd?.loadAdData(5)
    }
    
    // 解析ADX数据并启动穿山甲SDK获取插屏数据
    func preparPBUNaAdReqAndStart() {
        buTransfer.data = data
        buTransfer.delegate = self
        
        p_reqBUNaAd = instanceBUNativeAd(slotID: kBUAdSDK_HomePlaqueFeed_SlotId)
        p_reqBUNaAd?.delegate = buTransfer
        if item.slotRealBid, item.slotAdm.count > 0 {
            p_reqBUNaAd?.setAdMarkup(item.slotAdm)
        } else {
            p_reqBUNaAd?.loadAdData(withCount: 1)
        }
    }
    
    // 解析ADX数据并启动广点通SDK获取插屏数据
    func preparPGDTNaAdReqAndStart() {
        gdtTransfer.data = data
        gdtTransfer.delegate = self
        
        p_reqGDTNaAd = instanceGDTUnifiedNativeAd(placementId: kGDTAdSDK_HomePlaqueFeed_PlacementId)
        p_reqGDTNaAd?.delegate = gdtTransfer
        p_reqGDTNaAd?.load()
    }
}

// MARK: - 转换Token Method
extension XMLADXLoader {
    // 解析ADX数据并启动广点通SDK获取竞价数据
    class func prepareGDTAdBidToken() -> String? {
        return nil // GDTSDKConfig.getBuyerId()
    }
    
    // 解析ADX数据并启动穿山甲SDK获取启动图竞价数据
    func prepareSBuAdBidToken() -> String? {
        buTransfer.data = data
        buTransfer.delegate = self
        
        s_reqBuAd = instanceBUSplashAdView()
        s_reqBuAd?.delegate = buTransfer
        return s_reqBuAd?.biddingToken()
    }
    
    // 解析ADX数据并启动广点通SDK获取启动图竞价数据
    func prepareSGDTAdBidToken() -> String? {
        return nil // GDTSDKConfig.getBuyerId()
    }
    
    // 解析ADX数据并启动穿山甲SDK获取模版激励视频竞价数据
    func prepareVBuNEAdBidToken() -> String? {
        buTransfer.data = data
        buTransfer.delegate = self
        
        v_reqBuNEAd = instanceBUNERewardedVideoAd()
        v_reqBuNEAd?.delegate = buTransfer
        return v_reqBuNEAd?.biddingToken()
    }
    
    // 解析ADX数据并启动穿山甲SDK获取模版全屏视频竞价数据
    func prepareVBuNEFAdBidToken() -> String? {
        buTransfer.data = data
        buTransfer.delegate = self
        
        v_reqBuNEFAd = instanceBUNEFullscreenVideoAd()
        v_reqBuNEFAd?.delegate = buTransfer
        return v_reqBuNEFAd?.biddingToken()
    }
    
    // 解析ADX数据并启动广点通SDK获取激励视频竞价数据
    func prepareVGDTAdBidToken() -> String? {
        return nil // GDTSDKConfig.getBuyerId()
    }
    
    // 解析ADX数据并启动穿山甲SDK获取信息流竞价数据
    func prepareFBuAdBidToken() -> String? {
        buTransfer.data = data
        buTransfer.delegate = self
        
        f_reqBuAd = instanceBUNativeAd(slotID: kBUAdSDK_DefaultFeed_SlotId)
        f_reqBuAd?.delegate = buTransfer
        return f_reqBuAd?.biddingToken()
    }
    
    // 解析ADX数据并启动穿山甲SDK获取信息流竞价数据(模版渲染)
    func prepareFBuNEAdBidToken() -> String? {
        buTransfer.data = data
        buTransfer.delegate = self
        
        data.f_reqBuNEAd = instanceBUNEAdManager()
        f_reqBuNEAd = data.f_reqBuNEAd
        f_reqBuNEAd?.delegate = buTransfer
        return f_reqBuNEAd?.biddingToken()
    }
    
    // 解析ADX数据并启动广点通SDK获取信息流竞价数据
    func prepareFGDTAdBidToken() -> String? {
        return nil // GDTSDKConfig.getBuyerId()
    }
    
    // 解析ADX数据并启动穿山甲SDK获取模版插屏竞价数据
    func preparePBuNEFAdBidToken() -> String? {
        buTransfer.data = data
        buTransfer.delegate = self
        
        p_reqBuNEFAd = instancePBUNEFullscreenVideoAd()
        p_reqBuNEFAd?.delegate = buTransfer
        return p_reqBuNEFAd?.biddingToken()
    }
    
    // 解析ADX数据并启动广点通SDK获取插屏竞价数据
    func preparePGDTAdBidToken() -> String? {
        return nil // GDTSDKConfig.getBuyerId()
    }
}

// MARK: - 辅助 Method
extension XMLADXLoader {
    // 实例化启动图BUSplashAdView
    func instanceBUSplashAdView() -> BUSplashAd? {
        let adSize: CGSize = CGSize(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height*4/5)
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : kBUAdSDK_SplashPage_SlotId
        let adView = BUSplashAd.init(slotID: slotID, adSize: CGSize(width: adSize.width, height: adSize.height));
        adView.hideSkipButton = true
        adView.tolerateTimeout = 5.0
//        adView.rootViewController = XMLADXManager.viewController
        return adView
    }
    
    // 实例化启动图GDTSplashAd
    func instanceGDTSplashAd() -> GDTSplashAd? {
        let placementId: String = item.dspPositionId.count != 0 ? item.dspPositionId : kGDTAdSDK_SplashPage_PlacementId
//        let splashAd = (item.slotRealBid && item.slotAdm.count > 0) ? GDTSplashAd(placementId: placementId, token: item.slotAdm) : GDTSplashAd(placementId: placementId)
        let splashAd = GDTSplashAd(placementId: placementId)
        splashAd?.fetchDelay = 5
        return splashAd
    }
    
    // 实例化启动图BaiduMobAdSplash
    func instanceBDSplashAdView() -> BaiduMobAdSplash? {
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : kBDAdSDK_SplashPage_PlacementId
        let adView = BaiduMobAdSplash()
        adView.canSplashClick = true
        adView.adUnitTag = slotID
        return adView
    }
    
    // 实例化启动图BaiduMobAdSplash容器
    func instanceBDSplashAdBoxView() -> UIView {
        let adSize: CGSize = CGSize(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height*4/5)
        let boxView: UIView = UIView(frame: CGRect(origin: .zero, size: adSize))
        boxView.backgroundColor = .white
        return boxView
    }
    
    // 实例化启动图JADSplashView
    func instanceJDSplashView() -> JADSplashView? {
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : kJDAdSDK_SplashPage_PlacementId
        let adSize: CGSize = CGSize(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height*4/5)
        let rootVC: UIViewController =  XMLADXManager.viewController ?? UIViewController.init()
        let splashView = JADSplashView.init(slotID: slotID, adSize: adSize)
        splashView.rootViewController = rootVC;
        splashView.splashStyle = .serverConfig
        splashView.tolerateTime = 5
        splashView.skipTime = 5
        return splashView
    }
    
    // 实例化模版激励视频BUNativeExpressRewardedVideoAd
    func instanceBUNERewardedVideoAd() -> BUNativeExpressRewardedVideoAd? {
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : kBUAdSDK_DefaultNEVideo_SlotId
        let model: BURewardedVideoModel = BURewardedVideoModel()
        model.userId = "\(item.xml_userId)"
        let videoAd = BUNativeExpressRewardedVideoAd(slotID: slotID, rewardedVideoModel: model)
        return videoAd
    }
    
    // 实例化模版全屏视频BUNativeExpressFullscreenVideoAd
    func instanceBUNEFullscreenVideoAd() -> BUNativeExpressFullscreenVideoAd? {
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : kBUAdSDK_DefaultNEFVideo_SlotId
        let videoAd = BUNativeExpressFullscreenVideoAd(slotID: slotID)
        return videoAd
    }
    
    // 实例化激励视频GDTRewardVideoAd
    func instanceGDTRewardVideoAd() -> GDTRewardVideoAd? {
        let placementId: String = item.dspPositionId.count != 0 ? item.dspPositionId : kGDTAdSDK_DefaultVideo_PlacementId
//        let splashAd = (item.slotRealBid && item.slotAdm.count > 0) ? GDTRewardVideoAd(placementId: placementId, token: item.slotAdm) : GDTRewardVideoAd(placementId: placementId)
        let splashAd = GDTRewardVideoAd(placementId: placementId)
        return splashAd
    }
    
    // 实例化激励视频BaiduMobAdRewardVideo
    func instanceBaiduMobAdRewardVideo() -> BaiduMobAdRewardVideo? {
        let adUnitTag: String = item.dspPositionId.count != 0 ? item.dspPositionId : kBDAdSDK_DefaultVideo_PlacementId
        let videoAd = BaiduMobAdRewardVideo()
        videoAd.publisherId = kBDAdSDKAppId
        videoAd.adUnitTag = adUnitTag
        return videoAd
    }
    
    // 实例化信息流BUNativeAd
    func instanceBUNativeAd(slotID: String) -> BUNativeAdsManager? {
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : slotID
        let adSlot = BUAdSlot()
        adSlot.id = slotID
        adSlot.adType = .feed
        adSlot.position = .feed
        let renderWidth: CGFloat  = item.xml_renderSize?.width  ?? 300
        let renderHeight: CGFloat = item.xml_renderSize?.height ?? 170
        let imgSize: BUSize = BUSize()
        imgSize.width  = Int(renderWidth)
        imgSize.height = Int(renderHeight)
        adSlot.imgSize = imgSize
        let adManager = BUNativeAdsManager(slot: adSlot)
        return adManager
    }
    
    // 实例化信息流BUNativeExpressAdManager
    func instanceBUNEAdManager() -> BUNativeExpressAdManager? {
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : kBUAdSDK_DefaultFeed_SlotId
        let adSlot = BUAdSlot()
        adSlot.id = slotID
        adSlot.adType = .feed
        adSlot.position = .feed
        let renderWidth: CGFloat  = item.xml_renderSize?.width  ?? 300
        let renderHeight: CGFloat = item.xml_renderSize?.height ?? 170
        let imgSize: BUSize = BUSize()
        imgSize.width  = Int(renderWidth)
        imgSize.height = Int(renderHeight)
        adSlot.imgSize = imgSize
        let neAdManager = BUNativeExpressAdManager(slot: adSlot, adSize: CGSize(width: renderWidth, height: renderHeight))
        return neAdManager
    }
    
    // 实例化信息流GDTUnifiedNativeAd
    func instanceGDTUnifiedNativeAd(placementId: String) -> GDTUnifiedNativeAd? {
        let placementId: String = item.dspPositionId.count != 0 ? item.dspPositionId : placementId
//        let nativeAd = (item.slotRealBid && item.slotAdm.count > 0) ? GDTUnifiedNativeAd(placementId: placementId, token: item.slotAdm) : GDTUnifiedNativeAd(placementId: placementId)
        let nativeAd = GDTUnifiedNativeAd(placementId: placementId)
        return nativeAd
    }
    
    // 实例化信息流BaiduMobAdNative
    func instanceBDNativeAd() -> BaiduMobAdNative? {
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : kBDAdSDK_DefaultFeed_PlacementId
        let nativeAd = BaiduMobAdNative()
        nativeAd.publisherId = kBDAdSDKAppId
        nativeAd.adUnitTag = slotID
        let renderWidth : CGFloat  = item.xml_renderSize?.width  ?? 300
        let renderHeight: CGFloat  = item.xml_renderSize?.height ?? 170
        nativeAd.baiduMobAdsWidth  = NSNumber(value: Float(renderWidth))
        nativeAd.baiduMobAdsHeight = NSNumber(value: Float(renderHeight))
        return nativeAd
    }
    
    // 实例化信息流图JADNativeAd
    func instanceJADNativeAd() -> JADNativeAd? {
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : kJDAdSDK_DefaultFeed_PlacementId
        let slot = JADNativeAdSlot()
        slot.type = .feed
        slot.slotID = slotID
        let jdAdSize = JADNativeSize()
        // 代码位和尺寸是一一对应的
        jdAdSize.width = 1280
        jdAdSize.height = 720
        slot.imgSize = jdAdSize
        let splashAd = JADNativeAd.init(slot: slot)
        splashAd.rootViewController = XMLADXManager.viewController ?? UIViewController()
        return splashAd
    }
    
    // 实例化模版插屏BUNativeExpressFullscreenVideoAd
    func instancePBUNEFullscreenVideoAd() -> BUNativeExpressFullscreenVideoAd? {
        let slotID: String = item.dspPositionId.count != 0 ? item.dspPositionId : kBUAdSDK_DefaultPNEFVideo_SlotId
        let videoAd = BUNativeExpressFullscreenVideoAd(slotID: slotID)
        return videoAd
    }
    
    // 实例化插屏GDTUnifiedInterstitialAd
    func instanceGDTUnifiedInterstitialAd() -> GDTUnifiedInterstitialAd? {
        let placementId: String = item.dspPositionId.count != 0 ? item.dspPositionId : kGDTAdSDK_DefaultPlaque_PlacementId
//        let nativeAd = (item.slotRealBid && item.slotAdm.count > 0) ? GDTUnifiedInterstitialAd(placementId: placementId, token: item.slotAdm) : GDTUnifiedInterstitialAd(placementId: placementId)
        let nativeAd = GDTUnifiedInterstitialAd(placementId: placementId)
        return nativeAd
    }
}

// MARK: - ADX 穿山甲中转代理 XMLADXLoaderBUTransferDelegate Method
extension XMLADXLoader: XMLADXLoaderBUTransferDelegate {
    // 穿山甲数据解析并获取成功
    func buAd(_ transfer: XMLADXLoaderBUTransfer, didSuccessWith dataSource: XMLADXLoaderItem?) {
        s_reqBuAd?.delegate    = nil
        f_reqBuAd?.delegate    = nil
        v_reqBuNEAd?.delegate  = nil
        v_reqBuNEFAd?.delegate = nil
        delegate?.adxLoader?(self, didSuccessWith: self.data)
    }
    
    // 穿山甲数据解析并获取失败
    func buAd(_ transfer: XMLADXLoaderBUTransfer, didFailureWith error: Error?) {
        s_reqBuAd?.delegate    = nil
        f_reqBuAd?.delegate    = nil
        v_reqBuNEAd?.delegate  = nil
        v_reqBuNEFAd?.delegate = nil
        delegate?.adxLoader?(self, didFailureWith: error)
    }
}

// MARK: - ADX 广点通中转代理 XMLADXLoaderGDTTransferDelegate Method
extension XMLADXLoader: XMLADXLoaderGDTTransferDelegate {
    // 广点通数据解析并获取成功
    func gdtAd(_ transfer: XMLADXLoaderGDTTransfer, didSuccessWith dataSource: XMLADXLoaderItem?) {
        s_reqGDTAd?.delegate = nil
        v_reqGDTAd?.delegate = nil
        f_reqGDTAd?.delegate = nil
        delegate?.adxLoader?(self, didSuccessWith: self.data)
    }
    
    // 广点通数据解析并获取失败
    func gdtAd(_ transfer: XMLADXLoaderGDTTransfer, didFailureWith error: Error?) {
        s_reqGDTAd?.delegate = nil
        v_reqGDTAd?.delegate = nil
        f_reqGDTAd?.delegate = nil
        delegate?.adxLoader?(self, didFailureWith: error)
    }
}

// MARK: - ADX 喜马拉雅/喜马拉雅(SDK)中转代理 XMLADXLoaderDRTransferDelegate Method
extension XMLADXLoader: XMLADXLoaderDRTransferDelegate {
    // 广点通数据解析并获取成功
    func drAd(_ transfer: XMLADXLoaderDRTransfer, didSuccessWith dataSource: XMLADXLoaderItem?) {
        s_reqDRAd?.delegate  = nil
        v_reqDRAd?.delegate  = nil
        f_reqDRAd?.delegate  = nil
        f_reqXMLAd?.delegate = nil
        p_reqDRAd?.delegate  = nil
        delegate?.adxLoader?(self, didSuccessWith: self.data)
    }
    
    // 广点通数据解析并获取失败
    func drAd(_ transfer: XMLADXLoaderDRTransfer, didFailureWith error: Error?) {
        s_reqDRAd?.delegate  = nil
        v_reqDRAd?.delegate  = nil
        f_reqDRAd?.delegate  = nil
        f_reqXMLAd?.delegate = nil
        p_reqDRAd?.delegate  = nil
        delegate?.adxLoader?(self, didFailureWith: error)
    }
}

// MARK: - ADX 百度百青藤中转代理 XMLADXLoaderBDTransferDelegate Method
extension XMLADXLoader: XMLADXLoaderBDTransferDelegate {
    // 百度百青藤数据解析并获取成功
    func bdAd(_ transfer: XMLADXLoaderBDTransfer, didSuccessWith dataSource: XMLADXLoaderItem?) {
        s_reqBdAd?.delegate = nil
        v_reqBdAd?.delegate = nil
        delegate?.adxLoader?(self, didSuccessWith: self.data)
    }
    
    // 百度百青藤数据解析并获取失败
    func bdAd(_ transfer: XMLADXLoaderBDTransfer, didFailureWith error: Error?) {
        s_reqBdAd?.delegate = nil
        v_reqBdAd?.delegate = nil
        f_reqBdAd?.adDelegate = nil
        delegate?.adxLoader?(self, didFailureWith: error)
    }
}

// MARK: - ADX 京东云中转代理 XMLADXLoaderJDTransferDelegate Method
extension XMLADXLoader: XMLADXLoaderJDTransferDelegate {
    // 京东云SDK数据获取成功
    func jdAd(_ transfer: XMLADXLoaderJDTransfer, didSuccessWith dataSource: XMLADXLoaderItem?) {
        s_reqJDAd?.delegate = nil
        f_reqJDAd?.delegate = nil
        delegate?.adxLoader?(self, didSuccessWith: self.data)
    }
    // 京东云SDK数据获取失败
    func jdAd(_ transfer: XMLADXLoaderJDTransfer, didFailureWith error: Error?) {
        s_reqJDAd?.delegate = nil
        f_reqJDAd?.delegate = nil
        delegate?.adxLoader?(self, didFailureWith: error)
    }
}

