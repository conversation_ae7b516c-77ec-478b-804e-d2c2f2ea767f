//
//  XMLADXLoaderItem.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import XMConfigModule
import BUAdSDK
import GDTMobSDK
import BaiduMobAdSDK
import JADYun

@objcMembers
public class XMLADXLoaderItem: NSObject {
    public var positionName  : String                          = ""      // 代码位
    public var item          : XMLAdTypeItemModel?             = nil {   // adx后台信息
        didSet { positionName = item?.xml_posiName ?? "" }
    }
    public var itemEnterTime : TimeInterval                    = 0       // adxItem录入时间
    public var adEnterTime   : TimeInterval                    = 0       // 广告Item录入时间
    public var errorCode     : Int                             = .zero   // 错误码
    
    // Splash AD Item
    var s_buAd        : BUSplashAd?                 = nil
    var s_gdtAd       : GDTSplashAd?                    = nil
    var s_xmAd        : XMLDRNativeAd?                  = nil
    var s_bdAd        : BaiduMobAdSplash?               = nil
    var s_jdAd        : JADSplashView?                  = nil

    // Video AD Item
    var v_buNEAd      : BUNativeExpressRewardedVideoAd?   = nil
    var v_buNEFAd     : BUNativeExpressFullscreenVideoAd? = nil
    var v_gdtAd       : GDTRewardVideoAd?                 = nil
    var v_xmAd        : XMLDRRewardVideoAd?               = nil
    var v_bdAd        : BaiduMobAdRewardVideo?            = nil

    // FlowData AD Item
    var f_buAd        : BUNativeAd?                     = nil
    var f_buNEAd      : BUNativeExpressAdView?          = nil
    var f_gdtAd       : GDTUnifiedNativeAdDataObject?   = nil
    var f_xmAd        : XMLDRNativeAd?                  = nil
    var f_bdAd        : BaiduMobAdNativeAdObject?       = nil
    var f_xmlAd       : XMLDRNativeAd?                  = nil
    var f_jdAd        : JADNativeAd?                    = nil
    
    var f_buTrigger   : XMLBUNativeAdTrigger?           = nil
    var f_gdtTrigger  : XMLGDTNativeAdTrigger?          = nil
    var f_drTrigger   : XMLDRNativeAdTrigger?           = nil
    var f_bdTrigger   : XMLBDNativeAdTrigger?           = nil
    var f_jdTrigger   : XMLJDNativeAdTrigger?           = nil
    
    // 信息流是否已经被绑定
    var f_isBind      : Bool                            = false
    
    // Plaque AD Item
    var p_buNEFAd     : BUNativeExpressFullscreenVideoAd? = nil
    var p_gdtAd       : GDTUnifiedInterstitialAd?         = nil
    var p_xmAd        : XMLDRNativeAd?                    = nil
    var p_buNaAd      : BUNativeAd?                       = nil
    var p_gdtNaAd     : GDTUnifiedNativeAdDataObject?     = nil

    // 穿山甲信息流(模版渲染)特殊持有
    var f_reqBuNEAd   : BUNativeExpressAdManager?       = nil
    // 百度百青藤启动图(容器)特殊持有
    var s_reqBdAdBox  : UIView?                         = nil
    // 百度百青藤信息流特殊持有
    var f_reqBdAd     : BaiduMobAdNative?               = nil
    
    var bdTransfer    : XMLADXLoaderBDTransfer          = XMLADXLoaderBDTransfer()

    // 是否为ADX请求失败的时候才使用的缓存
    var isADXFailure  : Bool   = false
    
    
    
    // 是否为ADX之前的缓存
    public var isInCache     : Bool {
        return self.item?.xml_inCache ?? false
    }

    // 是否为SDK方拒绝返回广告
    public var isSDKRefuse   : Bool {
        // 保证为SDK类型存在
        guard let adType = item?.xml_AdType() else { return false }
        
        return XMLADXErrorCodeManager.isSDKRefuse(errorCode, adType: adType)
    }
    
    public func uploadStatisticInfo(_ status: Int) {
        if let isHimalaya = self.item?.xml_AdType().isHimalaya, isHimalaya, (status == 3001 || status == 4001) {
            // 喜马拉雅物料不上报 3001 4001
            return
        }
        let useTime: TimeInterval = Date().timeIntervalSince1970 * 1000 - itemEnterTime * 1000
        let info: [String: Any] = self.item?.toV2UploaderParams(status, useTime: useTime) ?? [String: Any]()
        XMBehaviorManager.shared().postOnlineData(dic: info, param: nil, type: .adx)
    }
    
    public func uploadVisibleStatisticInfo() {
        let isLoadFromCache: Bool = self.item?.xml_inCache ?? false
        let isADXFailure   : Bool = self.isADXFailure
        self.uploadStatisticInfo(isADXFailure ? 3 : (isLoadFromCache ? 2 : 1))
    }
    
    public class func uploadADXReqFailureStatisticInfo(_ status: Int, currentTime: TimeInterval, positionName: String = "", obj: XMLAdTypeItemModel? = nil) {
        let useTime: TimeInterval = Date().timeIntervalSince1970 * 1000 - currentTime * 1000
        let statisticObject: XMLAdTypeItemModel = obj ?? XMLAdTypeItemModel.statisticObject(positionName)
        if status == 5001 { statisticObject.adid = "90" }
        let info: [String: Any] = statisticObject.toV2UploaderParams(status, useTime: useTime)
        XMBehaviorManager.shared().postOnlineData(dic: info, param: nil, type: .adx)
    }
    
    // 更新底层数据状态
    public func updateStatusIfNeed() {
        self.item?.xml_isSilentPatch = self.isSilentPatch
    }
    
    // 更新状态
    public func updateTrigger(_ isShowed: Bool) {
        self.f_bdTrigger?.isTingShowReported = isShowed
        self.f_buTrigger?.isTingShowReported = isShowed
        self.f_gdtTrigger?.isTingShowReported = isShowed
        self.f_jdTrigger?.isTingShowReported = isShowed
        self.f_drTrigger?.isTingShowReported = isShowed
    }
}

// MARK: - 对外 Method
extension XMLADXLoaderItem {
    public func isBuAd() -> Bool {
        return (s_buAd != nil) || (v_buNEAd != nil) || (v_buNEFAd != nil) || (f_buAd != nil) || (f_buNEAd != nil) || (p_buNEFAd != nil) || (p_buNaAd != nil)
    }
    
    public func isGDTAd() -> Bool {
        return (s_gdtAd != nil) || (v_gdtAd != nil) || (f_gdtAd != nil) || (p_gdtAd != nil) || (p_gdtNaAd != nil)
    }
    
    public func isxmAd() -> Bool {
        return (s_xmAd != nil) || (v_xmAd != nil) || (f_xmAd != nil) || (p_xmAd != nil)
    }
    
    public func isxmlAd() -> Bool {
        return (f_xmlAd != nil)
    }
    
    public func isBdAd() -> Bool {
        return (s_bdAd != nil) || (v_bdAd != nil) || (f_bdAd != nil)
    }
    
    public func isJDAd() -> Bool {
        return (s_jdAd != nil) || (f_jdAd != nil)
    }
    
    public func isVideoAd() -> Bool {
        return (v_buNEAd != nil) || (v_buNEFAd != nil) || (v_gdtAd != nil) || (v_xmAd != nil) || (v_bdAd != nil)
    }
    
    public var slotId: String {
        return self.item?.dspPositionId ?? ""
    }
    
    public var vender: Int {
        return self.item?.xml_AdType().sdkType ?? 0
    }
    
    public var isRender: Bool {
        return self.f_buNEAd != nil
    }
    
    public var renderSize: CGSize {
        return self.f_buNEAd?.bounds.size ?? .zero
    }
    
    public var adId: String {
        return self.item?.adid ?? ""
    }
    
    public var material: [String: Any] {
        let info: [String: Any] = [:]
        return info
    }
    
    // 标题
    public var title: String? {
        if let model = f_buAd {
            return model.data?.adTitle
        } else if let model = f_gdtAd {
            return model.title
        } else if let model = f_xmAd {
            return model.title
        } else if let model = f_bdAd {
            return model.title
        } else if let model = f_jdAd {
            return model.data?.first?.adTitle
        }
        return nil
    }
    
    // 描述
    public var desc: String? {
        if let model = f_buAd {
            return model.data?.adDescription
        } else if let model = f_gdtAd {
            return model.desc
        } else if let model = f_xmAd {
            return model.desc
        } else if let model = f_bdAd {
            return model.text
        } else if let model = f_jdAd {
            return model.data?.first?.adDescription
        }
        return ""
    }
    
    // 广告主名称
    public var advertiser: String {
        if self.isBuAd() {
            return "穿山甲"
        } else if self.isGDTAd() {
            return "广点通"
        } else if self.isBdAd() {
            return "百度"
        } else if self.isJDAd() {
            return "京东"
        } else if let inScreenSource = self.item?.inScreenSource, inScreenSource == 1, (self.isxmAd() || self.isxmlAd()) { // 广告主文本
            return self.item?.materialProvideSource ?? ""
        }
        return ""
    }
    
    // 广告主信息
    public var advertiserInfo: String {
        if let inScreenSource = self.item?.inScreenSource, inScreenSource == 1 {
            return self.item?.materialProvideSource ?? ""
        }
        return ""
    }
    
    // 引导文案
    public var guideText: String {
        if let interactionType = self.f_buAd?.data?.interactionType {
            return interactionType == .download ? "立即下载" : "立即查看"
        } else if let isAppAd = self.f_gdtAd?.isAppAd {
            return isAppAd ? "立即下载" : "立即查看"
        } else if let actType = self.f_bdAd?.actType {
            return actType == BaiduMobNativeAdActionTypeDL ? "立即下载" : "立即查看"
        } else if let linkType = self.item?.linkType {
            return linkType == 2 ? "立即下载" : "立即查看"
        }
        return "立即查看"
    }
    
    // 是否为无声视频贴片
    public var isSilentVPatch: Bool {
        if let showType = self.item?.xml_showType(), showType.isInterruptPatch {
            return false
        } else if let isVideoAd = self.f_gdtAd?.isVideoAd, isVideoAd {
            return true
        } else if let isVideoAd = self.f_buAd?.xm_isFeedVideo, isVideoAd {
            return true
        }
        return false
    }
    
    // 是否为无声图文贴片
    public var isSilentPatch: Bool {
        if let isVideoAd = self.f_buAd?.xm_isFeedVideo {
            return !isVideoAd
        } else if let isVideoAd = self.f_gdtAd?.isVideoAd {
            return !isVideoAd
        } else if self.f_bdAd != nil {
            return true
        } else if self.f_xmAd != nil, let showType = self.item?.xml_showType(), (showType == .imageText || showType == .verText || showType == .scatterImageText || showType == .hFeedHorImageT) {
            return true
        }
        return false
    }
    
    // 恢复音频播放从广告详情页
    public func recoveryAudioBackFromDetailIfNeed(_ isForce: Bool) {
        self.f_buTrigger?.nativeAdVideoDetailDidCloseHandleIfNeed(isForce)
        self.f_gdtTrigger?.nativeAdVideoDetailDidCloseHandleIfNeed(isForce)
        self.f_drTrigger?.nativeAdVideoDetailDidCloseHandleIfNeed(isForce)
        self.f_bdTrigger?.nativeAdVideoDetailDidCloseHandleIfNeed(isForce)
        self.f_jdTrigger?.nativeAdVideoDetailDidCloseHandleIfNeed(isForce)
    }
    
    // 解除旧容器广告绑定事件
    public func unbind() {
        guard self.f_isBind else { return }
        self.f_isBind = false
        
        self.f_buAd?.unregisterView()
        self.f_gdtTrigger?.nativeAdView.unregisterDataObject()
        self.f_jdAd?.unregisterView()
        self.f_xmAd?.unregisterView()
        self.f_xmlAd?.unregisterView()
    }
    
    // 恢复播放
    public func resumeVideoIfNeed() {
        guard let showType = self.item?.xml_showType(), (showType.isImmersePatch || showType.isAutoPlayVideo) else { return }
        self.f_xmAd?.resumeIfNeed()
        self.f_xmlAd?.resumeIfNeed()
    }
    
    // 暂停播放
    public func pauseVideoIfNeed() {
        guard let showType = self.item?.xml_showType(), (showType.isImmersePatch || showType.isAutoPlayVideo) else { return }
        self.f_xmAd?.pauseIfNeed()
        self.f_xmlAd?.pauseIfNeed()
    }
}

extension BUNativeAd {
    public var xm_isFeedVideo: Bool {
        let imageModel: Int = self.data?.imageMode.rawValue ?? 0
        return imageModel == 5 || imageModel == 15
    }
}
