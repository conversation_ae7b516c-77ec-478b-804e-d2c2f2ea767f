//
//  XMLADXBaseLoaderManager.swift
//  XMADXModule
//
//  Created by yangle<PERSON> on 2020/8/7.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import UIKit
import XMConfigModule


public typealias XMLADXLoaderManagerCompletion = ((XMLADXLoaderItem?, [XMLADXLoaderItem]) -> Void)?

public var kXMLADXLoaderItemValidMaxTime: TimeInterval = 15.0 * 60

// 基础SDK并发数据源加载器
public class XMLADXBaseLoaderManager {
    // 广告加载超时时间
    public  var kXMLADXLoaderMaxLimitTime: TimeInterval = 6.0
    
    // 是否启用客户端竞价 football开关，默认为true
    public lazy var enableClientBidding: Bool = {
        return XMLADXConfig.isBiddingEnabled()
    }()

    // 广告加载器缓存
    private var loaderCache     : [String: [XMLADXLoader]]                = [:]
    // 同批次广告处理记录(KEY:代码位+时间)
    private var itemHandleRecord: [String: XMLADXLoaderManagerCompletion] = [:]
    // SDK渲染失败的物料
    private var errorItems      : [String: [XMLADXLoaderItem]]            = [:]
    
    // 广告加载器缓存信号锁
    private var loaderCacheLock     : DispatchSemaphore = DispatchSemaphore(value: 1)
    // 同批次广告处理记录信号锁
    private var itemHandleRecordLock: DispatchSemaphore = DispatchSemaphore(value: 1)
    // 渲染失败物料广告处理记录信号锁
    private var errorItemsRecordLock: DispatchSemaphore = DispatchSemaphore(value: 1)
    // SDK物料加载阶段
    public  var loaderPhase         : XMLAdLoaderPhase { .asyncLoader }
}

extension XMLADXBaseLoaderManager {
    
    // 直接从缓存中随机读取广告源数据
    func prepareLoaderDirectlyFromCache(_ positionName: String, beginTime: TimeInterval, completion: XMLADXLoaderManagerCompletion = nil) {
        guard positionName.count > 0 else { completion?(nil, []); return }
        // 刷新当前广告位ADXItem缓存(移除超时数据)
        XMLADXCacheManager.refreshLoaderItemCache(positionName)
        // 随机返回并清除当前广告位在缓存中存在的广告源数据，喜马物料默认缓存中不存在
        let randomItem = XMLADXCacheManager.randomGetAndCleanLoaderItemFromCache(positionName)
        // 将ADX失败标志置入数据源
        randomItem?.isADXFailure = true
        // 重置缓存数据状态
        randomItem?.item?.xml_loaderPhase = loaderPhase
        
        completion?(randomItem, getAndCleanErrorLoaderItems(positionName))
    }
}

// MARK: - ADX 解析器代理 XMLADXLoaderDelegate Method
extension XMLADXBaseLoaderManager: XMLADXLoaderDelegate {
    // adx数据解析并获取成功
    func adxLoader(_ loader: XMLADXLoader, didSuccessWith dataSource: XMLADXLoaderItem) {
        cleanLoaderFromCache(loader)
        // 数据源阶段重置
        dataSource.item?.xml_loaderPhase = loaderPhase
        
        // 竞价模式处理广告物料
        if loader.isBiddingMode {
            XMLADXBiddingManager.shared.addSuccessItem(dataSource)
            return
        }
        
        if Date().timeIntervalSince1970 - dataSource.itemEnterTime <= kXMLADXLoaderMaxLimitTime {
            if loader.item.xml_index == 0 { // 请求允许最大限度内，首位的广告源数据成功直接进行处理
                handleLoaderItem(dataSource)
            } else { // 请求允许最大限度内，非首位的广告源数据如果比它优先级高的广告解析都失败，则进行处理
                handleLoaderItemIfPreAllFailure(dataSource)
            }
        } else { // 超时领取时间
            // 3001 请求sdk返回超时 数据埋点
            dataSource.uploadStatisticInfo(3001)
            XMLADXCacheManager.addLoaderItemToCache(dataSource)
        }
    }
    
    // adx数据解析并获取失败
    func adxLoader(_ loader: XMLADXLoader, didFailureWith error: Error?) {
        // 竞价模式处理
        if loader.isBiddingMode {
            // 打印错误信息
            XMLADXUtils.consoleLog("[\(loader.data.item?.xml_advertiserName ?? "")] 竞价模式广告加载失败: \(loader.data.positionName): \(loader.data.item?.dspPositionId ?? "-") \(String(describing: error))")
            // 设置失败信息
            loader.data.errorCode = (error as NSError?)?.code ?? 9999
            XMLADXBiddingManager.shared.addFailureItem(loader.data)
            // 仍需要执行部分原有逻辑
            cleanLoaderFromCache(loader)
            recordErrorLoaderItem(loader, error: error)
            return
        }
        
        // 数据源阶段重置
        loader.data.item?.xml_loaderPhase = loaderPhase
        if Date().timeIntervalSince1970 - loader.data.itemEnterTime <= kXMLADXLoaderMaxLimitTime { // 4001 SDK未返回物料 数据埋点
            loader.data.uploadStatisticInfo(4001)
            XMLADXUtils.consoleLog("SDK 渲染失败(4001)(\(loaderPhase)) \(loader.data.positionName): \(loader.data.item?.dspPositionId ?? "-") \(String(describing: error))")
        } else { // 超时领取时间
            XMLADXUtils.consoleLog("SDK 渲染失败(3001)(\(loaderPhase)) \(loader.data.positionName): \(loader.data.item?.dspPositionId ?? "-") \(String(describing: error))")
            loader.data.uploadStatisticInfo(3001)
        }
        // 记录加载失败的加载器
        recordErrorLoaderItem(loader, error: error)
        // 清除无效/已使用的adx加载器
        cleanLoaderFromCache(loader)
        // 若优先级次一级加载成功或所有解析器都完成，取合适广告源数据进行后续操作(当前不存在优先级高的解析器)
        handleLoaderItemIfLaterSuccess(loader)
    }
}

// MARK: - 辅助 Method
extension XMLADXBaseLoaderManager {    
    // 预处理ADX数据源
    internal func preprocessADXSourceItems(_ items: [XMLAdTypeItemModel], type: XMLADXLoaderType) -> (loads: [XMLAdTypeItemModel], unloads: [XMLAdTypeItemModel]) {
        // 1. 清理喜马拉雅物料(底料)后的物料
        // 2. 高优先级物料有缓存则不加载后续物料
        var result: [XMLAdTypeItemModel] = []
        var presentedViewController: UIViewController? = nil
        if Thread.isMainThread {
            presentedViewController = UIApplication.shared.keyWindow?.rootViewController?.presentedViewController
        } else {
            DispatchQueue.main.sync {
                presentedViewController = UIApplication.shared.keyWindow?.rootViewController?.presentedViewController
            }
        }
        for item in items {
            // 当存在Present视图控制器的时候，GDT启动图无法正常渲染，故暂时抛弃这个数据
            if type == .splash && item.xml_AdType().isGDT && presentedViewController != nil { continue }
            // 若穿山甲尚未初始化完成，物料为穿山甲类型时，暂时抛弃这个数据
            if !XMLADXManager.isBUReady && item.xml_AdType().isBU { continue }
            
            item.xml_inCache     = XMLADXCacheManager.existLoaderItemFromCache(item)
            item.xml_loaderPhase = loaderPhase
            result.append(item)
            
            if item.xml_AdType() == .himalaya || item.xml_inCache { break }
        }
        
        var unLoadSDKItems: [XMLAdTypeItemModel] = []
        var isIgnoreLater : Bool                 = false
        for item in items {
            item.xml_loaderPhase = loaderPhase
            // 当存在Present视图控制器的时候，GDT启动图无法正常渲染，故暂时抛弃这个数据
            if type == .splash && item.xml_AdType().isGDT && presentedViewController != nil {
                unLoadSDKItems.append(item); continue
            }
            // 若穿山甲尚未初始化完成，物料为穿山甲类型时，暂时抛弃这个数据
            if !XMLADXManager.isBUReady && item.xml_AdType().isBU {
                unLoadSDKItems.append(item); continue
            }
            
            if item.xml_AdType() == .himalaya || item.xml_inCache { isIgnoreLater = true }
            
            if isIgnoreLater { unLoadSDKItems.append(item) }
        }
        // 4. 喜马拉雅物料(底料)物料不上报 6001
        unLoadSDKItems = unLoadSDKItems.filter({ $0.xml_AdType().isHimalaya == false })
        
        return (result, unLoadSDKItems)
    }
    
    // 预处理当前广告对应的缓存广告源数据
    internal func preprocessLoaderItemCache(_ items: [XMLAdTypeItemModel]) {
        guard let item = items.first, item.xml_posiName.count > 0 else { return }
        // 1. 重新将此广告位对应的广告源数据定优先级，此次没有请求的广告将置为Int.Max
        XMLADXCacheManager.lockWait()
        let loaderItems: [XMLADXLoaderItem] = XMLADXCacheManager.caches(item.xml_posiName)
        loaderItems.forEach { $0.item?.xml_index = Int.max; $0.item?.xml_loaderPhase = loaderPhase }
        items.enumerated().forEach { (offset, element) in
            loaderItems.forEach {
                if ($0.item?.dspPositionId ?? "") == element.dspPositionId { $0.item?.xml_index = offset }
            }
        }
        XMLADXCacheManager.updateCaches(item.xml_posiName, with: loaderItems)
        XMLADXCacheManager.lockSignal()
    }
    
    // 到达最大时间限制进行后续操作
    internal func reachLimitTimeHandleLoaderItem(_ time: TimeInterval, positionName: String) {
        // 竞价模式超时处理
        loaderCacheLock.wait()
        let loaders = loaderCache[positionName] ?? []
        let isBiddingTask = loaders.first?.isBiddingMode ?? false
        loaderCacheLock.signal()
        
        if isBiddingTask {
            XMLADXBiddingManager.shared.forceCompleteTask(positionName: positionName, time: time)
            return
        }
        
        guard let completion = getAndCleanLoaderCompletionFromCache(time, positionName: positionName) else {
            reachLimitTimeUploadUnusedLoaderItem(time, positionName: positionName); return
        }
        XMLADXCacheManager.lockWait()
        let loaderItems: [XMLADXLoaderItem] = XMLADXCacheManager.caches(positionName).sorted { ($0.item?.xml_index ?? 0) <= ($1.item?.xml_index ?? 0) }
        XMLADXCacheManager.lockSignal()
        guard let item = loaderItems.first, let index = item.item?.xml_index, index != Int.max else { completion(nil, []); return }
        // 数据源阶段重置
        item.item?.xml_loaderPhase = loaderPhase
        handleLoaderItem(item, isInCache: true, completion: completion)
        reachLimitTimeUploadUnusedLoaderItem(time, positionName: positionName)
    }
    
    // 到达最大时间限制后对新数据进行1002上报(仅限SDK，喜马拉雅物料不上报)
    internal func reachLimitTimeUploadUnusedLoaderItem(_ time: TimeInterval, positionName: String) {
        XMLADXCacheManager.lockWait()
        let uploadsItem: [XMLADXLoaderItem] = XMLADXCacheManager.caches(positionName).filter { $0.itemEnterTime == time && $0.isxmAd() == false}
        uploadsItem.forEach { $0.item?.xml_loaderPhase = loaderPhase; $0.uploadStatisticInfo(1002) }
        XMLADXCacheManager.lockSignal()
    }
    
    // 未请求SDK的新数据进行6001上报
    internal func uploadUnRequstSDKADXItems(_ time: TimeInterval, uploadsItem: [XMLAdTypeItemModel]) {
        guard uploadsItem.count > 0 else { return }
        uploadsItem.forEach {
            // 数据源阶段重置
            $0.xml_loaderPhase = loaderPhase
            XMLADXLoaderItem.uploadADXReqFailureStatisticInfo(6001, currentTime: time, obj: $0)
        }
    }
    
    // 使用广告源数据进行后续操作
    internal func handleLoaderItem(_ dataSource: XMLADXLoaderItem, isInCache: Bool = false, completion: XMLADXLoaderManagerCompletion = nil) {
        // 数据源阶段重置
        dataSource.item?.xml_loaderPhase = loaderPhase
        var comple: XMLADXLoaderManagerCompletion = completion
        if comple == nil { comple = getAndCleanLoaderCompletionFromCache(dataSource.itemEnterTime, positionName: dataSource.item?.xml_posiName ?? "") }
        guard let completion =  comple else { if !isInCache { XMLADXCacheManager.addLoaderItemToCache(dataSource) };return }
        if isInCache { XMLADXCacheManager.cleanLoaderItemFromCache(dataSource) }
        completion(dataSource, getAndCleanErrorLoaderItems(dataSource.item?.xml_posiName ?? ""))
    }
    
    // 使用广告源数据进行后续操作(如果比它优先级高的广告解析都失败)
    internal func handleLoaderItemIfPreAllFailure(_ dataSource: XMLADXLoaderItem) {
        // 数据源阶段重置
        dataSource.item?.xml_loaderPhase = loaderPhase
        // 获取当前广告的所有广告解析器
        let allLoader: [XMLADXLoader] = getAllLoaderFromCache(dataSource.item?.xml_posiName ?? "")
        let isPreAllFailure: Bool = allLoader.first { $0.item.xml_index < (dataSource.item?.xml_index ?? 0) } == nil
        // 确保比它优先级高的广告解析都失败
        guard isPreAllFailure else { XMLADXCacheManager.addLoaderItemToCache(dataSource); return }
        // 使用当前解析的广告数据源进行渲染
        let comple: XMLADXLoaderManagerCompletion = getAndCleanLoaderCompletionFromCache(dataSource.itemEnterTime, positionName: dataSource.item?.xml_posiName ?? "")
        guard let completion = comple else { XMLADXCacheManager.addLoaderItemToCache(dataSource); return }
        completion(dataSource, getAndCleanErrorLoaderItems(dataSource.item?.xml_posiName ?? ""))
    }
    
    // 若优先级次一级加载成功或所有解析器都完成，取合适广告源数据进行后续操作(优先级高的解析失败)
    internal func handleLoaderItemIfLaterSuccess(_ loader: XMLADXLoader) {
        // 获取当前广告的所有广告解析器
        let allLoader: [XMLADXLoader] = getAllLoaderFromCache(loader.item.xml_posiName)
        // 比当前广告解析器优先级高的是否全部失效
        let isPreAllFailure: Bool = allLoader.first { $0.item.xml_index < loader.item.xml_index } == nil
        if isPreAllFailure {
            XMLADXCacheManager.lockWait()
            // 获取当前广告位对应广告的所有缓存
            let loaderItems: [XMLADXLoaderItem] = XMLADXCacheManager.caches(loader.item.xml_posiName).sorted { ($0.item?.xml_index ?? 0) <= ($1.item?.xml_index ?? 0) }
            XMLADXCacheManager.lockSignal()
            // 从缓存中获取优先级最高的广告源
            let loaderItem: XMLADXLoaderItem? = loader.item.xml_index == Int.max ? nil : loaderItems.first { ($0.item?.xml_index ?? 0) <= (loader.item.xml_index + 1) }
            // 数据源阶段重置
            loaderItem?.item?.xml_loaderPhase = loaderPhase
            if let loaderItem = loaderItem {
                let comple: XMLADXLoaderManagerCompletion = getAndCleanLoaderCompletionFromCache(loader.data.itemEnterTime, positionName: loader.item.xml_posiName)
                guard let completion = comple else { return }
                XMLADXCacheManager.cleanLoaderItemFromCache(loaderItem)
                completion(loaderItem, getAndCleanErrorLoaderItems(loader.item.xml_posiName))
            } else if allLoader.count == 0, let validItem = loaderItems.first(where: { ($0.item?.xml_index ?? 0) != Int.max }) {
                // 若所有加载器均完成任务(失败或者完成)则使用当前缓存中解析的优先级最高的广告数据源进行渲染
                let comple: XMLADXLoaderManagerCompletion = getAndCleanLoaderCompletionFromCache(loader.data.itemEnterTime, positionName: loader.item.xml_posiName)
                guard let completion = comple else { return }
                // 数据源阶段重置
                validItem.item?.xml_loaderPhase = loaderPhase
                XMLADXCacheManager.cleanLoaderItemFromCache(validItem)
                completion(validItem, getAndCleanErrorLoaderItems(loader.item.xml_posiName))
            } else if allLoader.count == 0 {
                // 若所有加载器均完成任务(失败或者完成)且本地无可使用的广告数据源, 则直接退出
                let comple: XMLADXLoaderManagerCompletion = getAndCleanLoaderCompletionFromCache(loader.data.itemEnterTime, positionName: loader.item.xml_posiName)
                guard let completion = comple else { return }
                completion(nil, getAndCleanErrorLoaderItems(loader.item.xml_posiName))
            }
        }
    }
    
    // 记录ADX处理调用
    internal func getAndCleanLoaderCompletionFromCache(_ time: TimeInterval, positionName: String) -> XMLADXLoaderManagerCompletion {
        itemHandleRecordLock.wait()
        let key: String = positionName + "\(time)"
        let completion = itemHandleRecord[key]
        itemHandleRecord.removeValue(forKey: key)
        itemHandleRecordLock.signal()
        return completion ?? nil
    }
    
    // 记录ADX处理调用
    internal func addLoaderCompletionToCache(_ time: TimeInterval, positionName: String, completion: XMLADXLoaderManagerCompletion = nil) {
        itemHandleRecordLock.wait()
        let key: String = positionName + "\(time)"
        itemHandleRecord[key] = completion
        itemHandleRecordLock.signal()
    }
    
    // 清除无效/已使用的处理调用
    internal func cleanLoaderCompletionFromCache(_ time: TimeInterval, positionName: String) {
        itemHandleRecordLock.wait()
        let key: String = positionName + "\(time)"
        itemHandleRecord.removeValue(forKey: key)
        itemHandleRecordLock.signal()
    }
    
    // 返回所有当前位置正在使用adx加载器
    internal func getAllLoaderFromCache(_ positionName: String) -> [XMLADXLoader] {
        loaderCacheLock.wait()
        let loaders: [XMLADXLoader] = loaderCache[positionName] ?? []
        loaderCacheLock.signal()
        return loaders
    }
    
    // 记录正在使用adx加载器
    internal func addLoaderToCache(_ loader: XMLADXLoader) {
        loaderCacheLock.wait()
        var loaders: [XMLADXLoader] = loaderCache[loader.item.xml_posiName] ?? []
        loaders.append(loader)
        loaderCache[loader.item.xml_posiName] = loaders
        loaderCacheLock.signal()
    }
    
    // 清除无效/已使用的adx加载器
    internal func cleanLoaderFromCache(_ loader: XMLADXLoader) {
        loaderCacheLock.wait()
        var loaders: [XMLADXLoader] = loaderCache[loader.item.xml_posiName] ?? []
        loaders.remove(object: loader)
        loaderCache[loader.item.xml_posiName] = loaders
        loaderCacheLock.signal()
    }
    
    // 记录加载失败的加载器
    internal func recordErrorLoaderItem(_ loader: XMLADXLoader, error: Error?) {
        errorItemsRecordLock.wait()
        var loadItems: [XMLADXLoaderItem] = self.errorItems[loader.item.xml_posiName] ?? []
        let loadItem : XMLADXLoaderItem   = loader.data
        loadItem.errorCode = (error as NSError?)?.code ?? 9999
        loadItems.append(loadItem)
        self.errorItems[loader.item.xml_posiName] = loadItems
        errorItemsRecordLock.signal()
    }
    
    // 获取并清除渲染失败的数据
    internal func getAndCleanErrorLoaderItems(_ positionName: String) -> [XMLADXLoaderItem] {
        errorItemsRecordLock.wait()
        let result = self.errorItems.removeValue(forKey: positionName) ?? []
        errorItemsRecordLock.signal()
        return result
    }
}
