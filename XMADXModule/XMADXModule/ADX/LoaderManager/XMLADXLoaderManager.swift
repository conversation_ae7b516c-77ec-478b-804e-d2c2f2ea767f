//
//  XMLADXLoaderManager.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import UIKit
import XMConfigModule

// SDK并发数据源加载器
public class XMLADXLoaderManager: XMLADXBaseLoaderManager {
    
    private static let instance = XMLADXLoaderManager()
    
    public static func shared() -> XMLADXLoaderManager { return instance }
    
}

extension XMLADXLoaderManager {
    
    func prepareLoader(_ type: XMLADXLoaderType, items: [XMLAdTypeItemModel], isIgnoreADX: Bool = false, completion: XMLADXLoaderManagerCompletion = nil) {
        // 通用预处理逻辑
        let prepareResult = performCommonPreparation(type: type, items: items, isIgnoreADX: isIgnoreADX)
        
        // 如果预处理失败，直接返回
        guard let result = prepareResult else {
            completion?(nil, [])
            return
        }
        
        // 判断是否使用竞价模式
        let shouldUseBidding = enableClientBidding && result.hasMobileRtb
        
        if shouldUseBidding {
            XMLADXUtils.consoleLog("竞价模式处理广告 - positionName: \(result.positionName)")
            // 竞价模式处理
            handleBiddingMode(
                type: type,
                items: result.items,
                positionName: result.positionName,
                current: result.current,
                completion: completion
            )
        } else {
            XMLADXUtils.consoleLog("传统模式处理广告 - positionName: \(result.positionName)")
            // 传统模式处理
            handleTraditionalMode(
                type: type,
                items: result.items,
                positionName: result.positionName,
                current: result.current,
                completion: completion
            )
        }
    }
}

// MARK: - 数据结构
extension XMLADXLoaderManager {
    
    /// 通用预处理结果
    private struct PrepareResult {
        let items: [XMLAdTypeItemModel]
        let positionName: String
        let current: TimeInterval
        let hasMobileRtb: Bool
    }
}

// MARK: - 通用预处理逻辑
extension XMLADXLoaderManager {
    
    /// 执行通用预处理逻辑
    private func performCommonPreparation(type: XMLADXLoaderType, items: [XMLAdTypeItemModel], isIgnoreADX: Bool) -> PrepareResult? {
        // 从配置中心加载最新的最大超时时间
        kXMLADXLoaderMaxLimitTime     = isIgnoreADX ? 5.0   : XMLADXConfig.kADXLoaderMaxLimitTime(type)
        // 从配置中心加载缓存的最大保留时间
        kXMLADXLoaderItemValidMaxTime = isIgnoreADX ? 900.0 : XMLADXConfig.kADXLoaderItemValidMaxTime()
        // 预处理当前最新的最大超时时间(客户端容错)
        preprocessADXLoaderMaxLimitTime(type, items: items)
        
        guard items.count > 0, let positionName = items.first?.xml_posiName, positionName.count > 0 else { return nil }
        
        // 刷新当前广告位ADXItem缓存(移除超时数据)
        XMLADXCacheManager.refreshLoaderItemCache(positionName)
        
        // 预筛选当前ADX请求Item(加载使用，上报使用(不满足使用条件+当次缓存))
        let filterSourceItemsTuple = preprocessADXSourceItems(items, type: type)
        
        // 【1️⃣关键】这里的数组顺序决定优先级：index=0,1,2... 优先级递减
        let processedItems: [XMLAdTypeItemModel] = filterSourceItemsTuple.loads
        // 预筛选当前ADXItem(上报使用(不满足使用条件+当次缓存))
        let unloads: [XMLAdTypeItemModel] = filterSourceItemsTuple.unloads
        
        // 当前批次处理ADX数据时间点
        let current: TimeInterval = Date().timeIntervalSince1970
        
        // 未请求SDK的新数据进行6001上报
        uploadUnRequstSDKADXItems(current, uploadsItem: unloads)
        
        // 检查是否有竞价广告
        let hasMobileRtb = processedItems.contains { $0.isMobileRtb }
        
        // 若不存在加载的广告则直接结束并返回
        guard !processedItems.isEmpty else { return nil }
        
        // 预处理当前广告对应的缓存广告源数据
        preprocessLoaderItemCache(processedItems)
        
        return PrepareResult(
            items: processedItems,
            positionName: positionName,
            current: current,
            hasMobileRtb: hasMobileRtb
        )
    }
}

// MARK: - 竞价模式处理
extension XMLADXLoaderManager {
    
    /// 处理竞价模式
    private func handleBiddingMode(type: XMLADXLoaderType, items: [XMLAdTypeItemModel], positionName: String, current: TimeInterval, completion: XMLADXLoaderManagerCompletion) {
        // 启动竞价任务
        XMLADXBiddingManager.shared.startBiddingTask(
            positionName: positionName,
            time: current,
            totalCount: items.count,
            completion: completion
        )
        
        // 设置超时处理
        setupTimeoutHandler(current: current, positionName: positionName)
        
        // 并发处理所有广告源（包括缓存和实时请求）
        processBiddingItems(type: type, items: items, current: current)
    }
    
    /// 处理竞价模式下的广告源
    private func processBiddingItems(type: XMLADXLoaderType, items: [XMLAdTypeItemModel], current: TimeInterval) {
        XMLADXQueueManager.shared().loaderQueue.async {
            for item in items {
                if item.xml_inCache {
                    // 有缓存的item直接参与竞价
                    if let cachedLoaderItem = XMLADXCacheManager.getLoaderItemFromCache(item) {
                        cachedLoaderItem.itemEnterTime = current
                        XMLADXBiddingManager.shared.addSuccessItem(cachedLoaderItem)
                    }
                } else {
                    // 无缓存的item需要实时请求
                    self.createAndStartLoader(item: item, type: type, current: current, isBiddingMode: true)
                }
            }
        }
    }
}

// MARK: - 传统模式处理
extension XMLADXLoaderManager {
    
    /// 处理传统模式
    private func handleTraditionalMode(type: XMLADXLoaderType, items: [XMLAdTypeItemModel], positionName: String, current: TimeInterval, completion: XMLADXLoaderManagerCompletion) {
        // 若首个广告存在对应的缓存广告源数据，则直接回调
        if let first = items.first, first.xml_inCache, let loadItem = XMLADXCacheManager.getLoaderItemFromCache(first) {
            XMLADXCacheManager.cleanLoaderItemFromCache(loadItem)
            completion?(loadItem, [])
            return
        }
        
        // 保存completion到缓存：使用"广告位名称+时间戳"作为key
        addLoaderCompletionToCache(current, positionName: positionName, completion: completion)
        
        // 设置超时处理
        setupTimeoutHandler(current: current, positionName: positionName)
        
        // 按优先级顺序处理广告源（遇到缓存则停止）
        processTraditionalItems(type: type, items: items, current: current)
    }
    
    /// 处理传统模式下的广告源
    private func processTraditionalItems(type: XMLADXLoaderType, items: [XMLAdTypeItemModel], current: TimeInterval) {
        XMLADXQueueManager.shared().loaderQueue.async {
            for item in items {
                if item.xml_inCache {
                    // 传统模式：遇到缓存直接停止
                    break
                }
                
                // 创建并启动loader
                self.createAndStartLoader(item: item, type: type, current: current, isBiddingMode: false)
            }
        }
    }
}

// MARK: - 辅助方法
extension XMLADXLoaderManager {
    
    /// 创建并启动Loader
    private func createAndStartLoader(item: XMLAdTypeItemModel, type: XMLADXLoaderType, current: TimeInterval, isBiddingMode: Bool) {
        let loader: XMLADXLoader = XMLADXLoader(with: item, type: type)
        loader.data.itemEnterTime = current
        loader.isBiddingMode = isBiddingMode
        loader.item.requestStatus = .loading
        addLoaderToCache(loader)
        loader.delegate = self
        loader.load()
    }
    
    /// 设置超时处理
    private func setupTimeoutHandler(current: TimeInterval, positionName: String) {
        DispatchQueue.main.asyncAfter(deadline: .now() + kXMLADXLoaderMaxLimitTime) { [weak self] in
            self?.reachLimitTimeHandleLoaderItem(current, positionName: positionName)
        }
    }
    
    /// 预处理当前最新的最大超时时间(客户端容错)
    private func preprocessADXLoaderMaxLimitTime(_ type: XMLADXLoaderType, items: [XMLAdTypeItemModel]) {
        guard items.count > 0 else { return }
        if kXMLADXLoaderMaxLimitTime == 0.0 {
            kXMLADXLoaderMaxLimitTime = 5.0
        }
    }
}
