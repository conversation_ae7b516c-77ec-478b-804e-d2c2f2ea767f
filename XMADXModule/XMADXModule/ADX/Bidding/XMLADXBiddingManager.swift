//
//  XMLADXBiddingManager.swift
//  XMADXModule
//
//  Created by Assistant on 2024/12/19.
//  Copyright © 2024 ximalaya. All rights reserved.
//

import Foundation
import XMConfigModule
import XMXlog
// MARK: - 客户端竞价管理器
public class XMLADXBiddingManager {
    
    // 单例
    public static let shared = XMLADXBiddingManager()
    private init() {}
    
    // 竞价任务收集器
    private var biddingTasks: [String: XMLADXBiddingTask] = [:]
    private let taskLock = DispatchSemaphore(value: 1)
    
    // 竞价算法策略
    public var biddingStrategy: XMLADXBiddingStrategy = XMLADXDefaultBiddingStrategy()
}

// MARK: - Public Methods
extension XMLADXBiddingManager {
    
    // 开始一个竞价任务
    public func startBiddingTask(positionName: String,
                                time: TimeInterval,
                                totalCount: Int,
                                completion: XMLADXLoaderManagerCompletion) {
        let taskKey = positionName + "\(time)"
        let task = XMLADXBiddingTask(
            taskKey: taskKey,
            totalLoaderCount: totalCount,
            completion: completion
        )
        
        taskLock.wait()
        biddingTasks[taskKey] = task
        taskLock.signal()
        
        XMLADXUtils.consoleLog("🎯 竞价任务开始: \(taskKey), 总数: \(totalCount)")
    }
    
    // 添加成功的广告
    public func addSuccessItem(_ item: XMLADXLoaderItem) {
        let taskKey = (item.item?.xml_posiName ?? "") + "\(item.itemEnterTime)"
        
        taskLock.wait()
        defer { taskLock.signal() }
        
        guard let task = biddingTasks[taskKey], !task.isCompleted else { 
            XMLADXUtils.consoleLog("⚠️ 竞价任务不存在或已完成: \(taskKey)")
            return 
        }
        
        task.addSuccess(item)
        item.item?.requestStatus = .success
        XMLADXUtils.consoleLog("✅ 竞价收到成功结果: \(taskKey), 当前成功数: \(task.successItems.count)/\(task.totalLoaderCount), 广告主: \(item.item?.xml_advertiserName ?? "")")
        
        if task.shouldComplete {
            completeBiddingTask(task)
        }
    }
    
    // 添加失败的广告
    public func addFailureItem(_ item: XMLADXLoaderItem) {
        let taskKey = (item.item?.xml_posiName ?? "") + "\(item.itemEnterTime)"
        
        taskLock.wait()
        defer { taskLock.signal() }
        
        guard let task = biddingTasks[taskKey], !task.isCompleted else { 
            XMLADXUtils.consoleLog("⚠️ 竞价任务不存在或已完成: \(taskKey)")
            return 
        }
        
        task.addFailure(item)
        item.item?.requestStatus = .faild
        XMLADXUtils.consoleLog("❌ 竞价收到失败结果: \(taskKey), 当前完成数: \(task.completedCount)/\(task.totalLoaderCount), 广告主: \(item.item?.xml_advertiserName ?? "")")
        
        if task.shouldComplete {
            completeBiddingTask(task)
        }
    }
    
    // 强制完成竞价任务（用于超时等场景）
    public func forceCompleteTask(positionName: String, time: TimeInterval) {
        let taskKey = positionName + "\(time)"
        
        taskLock.wait()
        defer { taskLock.signal() }
        
        guard let task = biddingTasks[taskKey], !task.isCompleted else { return }
        
        XMLADXUtils.consoleLog("⏰ 强制完成竞价任务: \(taskKey)")
        task.isForcedComplete = true
        completeBiddingTask(task)
    }
}

// MARK: - Private Methods
extension XMLADXBiddingManager {
    
    private func completeBiddingTask(_ task: XMLADXBiddingTask) {
        task.isCompleted = true
        biddingTasks.removeValue(forKey: task.taskKey)
        
        XMLADXUtils.consoleLog("🏁 竞价任务完成: \(task.taskKey), 成功: \(task.successItems.count), 失败: \(task.failureItems.count)")
        
        // 异步执行竞价逻辑，避免阻塞
        DispatchQueue.global(qos: .userInitiated).async {
            self.performBidding(task)
        }
    }
    
    private func performBidding(_ task: XMLADXBiddingTask) {
        let startTime = Date().timeIntervalSince1970
        let winnerItem = biddingStrategy.selectWinner(from: task.successItems)
        let biddingTime = Date().timeIntervalSince1970 - startTime
        
        if let winner = winnerItem {
            XMLADXUtils.consoleLog("🏆 竞价获胜者: \(winner.item?.dspPositionId ?? "unknown"), 耗时: \(biddingTime)ms")
            
            // 如果获胜者来自缓存，清理对应的缓存
            if winner.isInCache {
                XMLADXCacheManager.cleanLoaderItemFromCache(winner)
                XMLADXUtils.consoleLog("🧹 已清理竞价获胜者的缓存: \(winner.item?.dspPositionId ?? "unknown")")
            }
            // 将成功和失败的物料合并一起上报
            let allItems = task.successItems + task.failureItems
            reportMobileRtbAds(allItems, winner: winner, isForcedComplete: task.isForcedComplete)
        } else {
            XMLADXUtils.consoleLog("💔 竞价无获胜者, 耗时: \(biddingTime)ms")
        }
        
        DispatchQueue.main.async {
            task.completion?(winnerItem, task.failureItems)
        }
    }
    
    
    private func reportMobileRtbAds(_ adItems: [XMLADXLoaderItem], winner: XMLADXLoaderItem, isForcedComplete: Bool) {
        var dic: [String: Any] = [:]
        let winnerItem = winner.item
        let responseId = winnerItem?.responseId
        dic["responseId"] = responseId
        let positionId = winnerItem?.positionId
        dic["positionId"] = positionId
        let sdkVersion = ""
        dic["sdkVersion"] = sdkVersion
        let version = Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString")
        dic["version"] = version
        
        var mobileRtbList = [[String: Any]]()
        for item in adItems {
            guard let adItem = item.item else { continue }
            
            var itemDic: [String: Any] = [:]
            let adId = adItem.adid
            let price = adItem.price
            itemDic["adId"] = adId
            itemDic["price"] = price
            // TODO: adUniqId
            
            // 加载状态
            var dspResult = -1
            if adItem.xml_isSDKAd() == true {
                if adItem.requestStatus == .success {
                    dspResult = 1
                } else if adItem.requestStatus == .faild {
                    dspResult = 0
                } else {
                    if isForcedComplete {
                        dspResult = 3
                    }
                }
            }
            itemDic["dspResult"] = dspResult
            itemDic["isCache"] = adItem.xml_inCache ? 1 : 0
            itemDic["rtbPrice"] = adItem.priceEncrypt
            itemDic["adSource"] = "\(adItem.adtype)"
            itemDic["slotId"] = adItem.dspPositionId
            
            // SDK广告回调状态处理
            // TODO: timeCost
            var sdkCallbackStatus = -10000
            var sdkCallbackMsg = adItem.xml_isSDKAd() ? "" : "xmly-该物料为adx物料"
            
            if adItem.xml_isSDKAd() {
                // SDK回调状态和消息处理
                if adItem.requestStatus == .success {
                    sdkCallbackStatus = 10000
                    let isWinner = winner.item === adItem ? "竞胜" : "竞败"
                    sdkCallbackMsg = "xmly-SDK请求成功-\(isWinner)"
                } else if adItem.requestStatus == .faild {
                    sdkCallbackStatus = item.errorCode
                    sdkCallbackMsg = "SDK请求失败，错误码：\(item.errorCode)，adType：\(adItem.adtype)"
                } else if isForcedComplete {
                    sdkCallbackStatus = -99999
                    sdkCallbackMsg = "xmly-sdk在规定时间内未返回"
                }
            }
            
            itemDic["sdkCallbackStatus"] = sdkCallbackStatus
            itemDic["sdkCallbackMsg"] = sdkCallbackMsg
            
            mobileRtbList.append(itemDic)
        }
        
        let mobileRtbListStr = (mobileRtbList as NSArray).jsonString()
        dic["mobileRtbReportList"] = mobileRtbListStr
        XMLADXUtils.consoleLog("xlog 日志上报 mobileRtbAds")
#if DEBUG
        // 强制修改typeSubtypeWhiteList，通过校验，仅debug生效
        forceUpdateDebugWhiteList()
#endif
        XMLXlog.LOG_INFO("XmAd", subModule: "mobileRtbAds", format: NSDictionary(dictionary: dic))
        
    }
    
    #if DEBUG
    /// 强制更新调试白名单，确保XmAd模块能通过校验（仅DEBUG模式下有效）
    private func forceUpdateDebugWhiteList() {
        let debugUploader = XMXlogDebugUploader.shareInstance()
        guard debugUploader.localUploadEnabled else { return }
        
        // 获取现有的白名单，如果没有则创建新的
        var whitelist = debugUploader.value(forKey: "typeSubtypeWhiteList") as? NSMutableDictionary
        if whitelist == nil {
            whitelist = NSMutableDictionary()
        }
        
        // 获取现有的XmAd配置，如果没有则创建新的
        var xmAdSubTypes = whitelist?.object(forKey: "XmAd") as? NSMutableArray
        if xmAdSubTypes == nil {
            xmAdSubTypes = NSMutableArray()
        }
        
        // 添加我们需要的subType（避免重复添加）
        if !xmAdSubTypes!.contains("mobileRtbAds") {
            xmAdSubTypes!.add("mobileRtbAds")
        }
        if !xmAdSubTypes!.contains("*") {
            xmAdSubTypes!.add("*")  // 通配符，允许所有subType
        }
        
        // 更新白名单
        whitelist?.setObject(xmAdSubTypes!, forKey: "XmAd" as NSString)
        debugUploader.setValue(whitelist, forKey: "typeSubtypeWhiteList")
        
        XMLADXUtils.consoleLog("🔧 已强制更新调试白名单: XmAd -> [mobileRtbAds, *]")
    }
    #endif
}

// MARK: - 竞价任务类
class XMLADXBiddingTask {
    let taskKey: String
    let totalLoaderCount: Int
    let completion: XMLADXLoaderManagerCompletion
    
    private(set) var successItems: [XMLADXLoaderItem] = []
    private(set) var failureItems: [XMLADXLoaderItem] = []
    var isCompleted: Bool = false
    var isForcedComplete: Bool = false
    
    init(taskKey: String, totalLoaderCount: Int, completion: XMLADXLoaderManagerCompletion) {
        self.taskKey = taskKey
        self.totalLoaderCount = totalLoaderCount
        self.completion = completion
    }
    
    func addSuccess(_ item: XMLADXLoaderItem) {
        successItems.append(item)
    }
    
    func addFailure(_ item: XMLADXLoaderItem) {
        failureItems.append(item)
    }
    
    var completedCount: Int {
        return successItems.count + failureItems.count
    }
    
    var shouldComplete: Bool {
        return completedCount >= totalLoaderCount
    }
}

