//
//  XMLADXBiddingStrategy.swift
//  XMADXModule
//
//  Created by Assistant on 2024/12/19.
//  Copyright © 2024 ximalaya. All rights reserved.
//

import Foundation

// MARK: - 竞价策略协议
public protocol XMLADXBiddingStrategy {
    func selectWinner(from items: [XMLADXLoaderItem]) -> XMLADXLoaderItem?
}

// MARK: - 默认竞价策略（按eCPM）
public class XMLADXDefaultBiddingStrategy: XMLADXBiddingStrategy {
    
    public func selectWinner(from items: [XMLADXLoaderItem]) -> XMLADXLoaderItem? {
        guard !items.isEmpty else { return nil }
        
        XMLADXUtils.consoleLog("🎯 开始竞价，候选广告数: \(items.count)")
        
        // 过滤价格不符合要求的SDK广告
        let filteredItems = items.filter { item in
            guard let adItem = item.item else { return false }
            
            // 如果是SDK广告，需要检查价格是否满足底价要求
            if adItem.xml_isSDKAd() {
                let price = adItem.price
                let bidMinPrice = adItem.bidMinPriceValue
                
                if price < bidMinPrice {
                    XMLADXUtils.consoleLog("❌ SDK广告价格不满足底价要求，被过滤: \(adItem.dspPositionId)(price:\(price) < bidMinPrice:\(bidMinPrice))")
                    return false
                }
            }
            
            return true
        }
        
        guard !filteredItems.isEmpty else {
            XMLADXUtils.consoleLog("⚠️ 所有广告都被价格过滤，无法进行竞价")
            return nil
        }
        
        XMLADXUtils.consoleLog("🎯 价格过滤后参与竞价的广告数: \(filteredItems.count)")
        
        // 遍历filteredItems，打印信息
        for item in filteredItems {
            XMLADXUtils.consoleLog("📊 竞价对比: \(item.item?.dspPositionId ?? "unknown")(adType:\(item.item?.adtype ?? 0), adid:\(item.item?.adid ?? ""), rankLevel:\(getRankLevel(for: item)), price:\(getPrice(for: item)), 广告主:\(item.item?.xml_advertiserName ?? ""))")
        }
        
        // 按rankLevel和price进行多级排序，rankLevel大的优先，price大的优先
        let winner = filteredItems.max { item1, item2 in
            let rankLevel1 = getRankLevel(for: item1)
            let rankLevel2 = getRankLevel(for: item2)
            let price1 = getPrice(for: item1)
            let price2 = getPrice(for: item2)
                        
            // 先比较rankLevel，rankLevel大的优先
            if rankLevel1 != rankLevel2 {
                return rankLevel1 < rankLevel2
            }
            // rankLevel相同时，比较price，price大的优先
            return price1 < price2
        }
        
        if let winner = winner {
            XMLADXUtils.consoleLog("🏆 竞价获胜者: \(winner.item?.dspPositionId ?? "unknown")(adType:\(winner.item?.adtype ?? 0), adid:\(winner.item?.adid ?? ""), rankLevel:\(getRankLevel(for: winner)), price:\(getPrice(for: winner)), 广告主:\(winner.item?.xml_advertiserName ?? ""))")
        }
        
        return winner
    }
    
    private func getRankLevel(for item: XMLADXLoaderItem) -> Int {
        // 获取广告的rankLevel字段
        return item.item?.rankLevel ?? 0
    }
    
    private func getPrice(for item: XMLADXLoaderItem) -> Double {
        // 直接返回广告的price字段
        return item.item?.price ?? 0.0
    }
    
}


