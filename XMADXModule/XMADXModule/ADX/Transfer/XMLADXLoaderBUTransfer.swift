//
//  XMLADXLoaderBUTransfer.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import BUAdSDK

@objc protocol XMLADXLoaderBUTransferDelegate {
    // 穿山甲SDK数据获取成功
    @objc optional func buAd(_ transfer: XMLADXLoaderBUTransfer, didSuccessWith dataSource: XMLADXLoaderItem?)
    // 穿山甲SDK数据获取失败
    @objc optional func buAd(_ transfer: XMLADXLoaderBUTransfer, didFailureWith error: Error?)
}

// 穿山甲SDK数据获取代理
class XMLADXLoaderBUTransfer: NSObject {
    weak var delegate: XMLADXLoaderBUTransferDelegate? = nil
    weak var data    : XMLADXLoaderItem?               = nil { didSet { self.item = data?.item } }
    var item         : XMLAdTypeItemModel?
}

// MARK: - 启动图 穿山甲 BUSplashAdDelegate Method
extension XMLADXLoaderBUTransfer: BUSplashAdDelegate {
    func splashAdLoadSuccess(_ splashAd: BUSplashAd) {
        data?.s_buAd      = splashAd
        if let mediaExt = splashAd.mediaExt as? [String: Any],
           let originPrice = mediaExt["price"] as? NSNumber {
            item?.price = originPrice.doubleValue / 100.0
            print("ad-splash csj item.price: \(item?.price ?? 0)")
        }
        data?.adEnterTime = Date().timeIntervalSince1970
        delegate?.buAd?(self, didSuccessWith: self.data)
    }
    
    func splashAdLoadFail(_ splashAd: BUSplashAd, error: BUAdError?) {
        delegate?.buAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
    
    func splashAdRenderSuccess(_ splashAd: BUSplashAd) { }
    
    func splashAdRenderFail(_ splashAd: BUSplashAd, error: BUAdError?) { }
    
    func splashAdWillShow(_ splashAd: BUSplashAd) { }
    
    func splashAdDidShow(_ splashAd: BUSplashAd) { }
    
    func splashAdDidClick(_ splashAd: BUSplashAd) { }
    
    func splashAdDidClose(_ splashAd: BUSplashAd, closeType: BUSplashAdCloseType) { }
    
    func splashAdViewControllerDidClose(_ splashAd: BUSplashAd) { }
    
    func splashDidCloseOtherController(_ splashAd: BUSplashAd, interactionType: BUInteractionType) { }
    
    func splashVideoAdDidPlayFinish(_ splashAd: BUSplashAd, didFailWithError error: Error?) { }
}

// MARK: - 激励视频(模版) 穿山甲 BUNativeExpressRewardedVideoAdDelegate Method
extension XMLADXLoaderBUTransfer: BUNativeExpressRewardedVideoAdDelegate {
    func nativeExpressRewardedVideoAdDidDownLoadVideo(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) {
        data?.v_buNEAd = rewardedVideoAd
        data?.adEnterTime = Date().timeIntervalSince1970
        delegate?.buAd?(self, didSuccessWith: self.data)
    }
    
    func nativeExpressRewardedVideoAd(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd, didFailWithError error: Error?) {
        delegate?.buAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
}

// MARK: - 全屏视频/插屏(模版) 穿山甲 BUNativeExpressFullscreenVideoAdDelegate Method
extension XMLADXLoaderBUTransfer: BUNativeExpressFullscreenVideoAdDelegate {
    func nativeExpressFullscreenVideoAdDidDownLoadVideo(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        if let isPlaque = self.item?.xml_showType().isPlaque, isPlaque {
            data?.p_buNEFAd = fullscreenVideoAd
        } else {
            data?.v_buNEFAd = fullscreenVideoAd
        }
        data?.adEnterTime = Date().timeIntervalSince1970
        delegate?.buAd?(self, didSuccessWith: self.data)
    }
    
    func nativeExpressFullscreenVideoAd(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd, didFailWithError error: Error?) {
        delegate?.buAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
}

// MARK: - 信息流(非模版) 穿山甲 BUNativeAdsManagerDelegate Method
extension XMLADXLoaderBUTransfer: BUNativeAdsManagerDelegate {
    func nativeAdsManagerSuccess(toLoad adsManager: BUNativeAdsManager, nativeAds nativeAdDataArray: [BUNativeAd]?) {
        if let nativeAd = nativeAdDataArray?.first {
            if let isPlaque = self.item?.xml_showType(), isPlaque == .lanPlaque {
                data?.p_buNaAd = nativeAd
            } else {
                data?.f_buAd = nativeAd
            }
            data?.adEnterTime = Date().timeIntervalSince1970
            delegate?.buAd?(self, didSuccessWith: self.data)
        } else {
            delegate?.buAd?(self, didFailureWith: nil)
            XMLADXReportManager.sendSDKErrorInfo(item, error: nil)
        }
    }
    
    func nativeAdsManager(_ adsManager: BUNativeAdsManager, didFailWithError error: Error?) {
        delegate?.buAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
}

// MARK: - 信息流(模版) 穿山甲 BUNativeExpressAdViewDelegate Method
extension XMLADXLoaderBUTransfer: BUNativeExpressAdViewDelegate {
    func nativeExpressAdSuccess(toLoad nativeExpressAd: BUNativeExpressAdManager, views: [BUNativeExpressAdView]) {
        if let element = views.randomElement() {
            element.render()
        } else {
            delegate?.buAd?(self, didFailureWith: nil)
        }
    }
    
    func nativeExpressAdFail(toLoad nativeExpressAd: BUNativeExpressAdManager, error: Error?) {
        delegate?.buAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
    
    func nativeExpressAdViewRenderSuccess(_ nativeExpressAdView: BUNativeExpressAdView) {
        if nativeExpressAdView.isReady {
            data?.f_buNEAd = nativeExpressAdView
            data?.adEnterTime = Date().timeIntervalSince1970
            delegate?.buAd?(self, didSuccessWith: self.data)
        } else {
            delegate?.buAd?(self, didFailureWith: nil)
            XMLADXReportManager.sendSDKErrorInfo(item, error: nil)
        }
    }
    
    func nativeExpressAdViewRenderFail(_ nativeExpressAdView: BUNativeExpressAdView, error: Error?) {
        delegate?.buAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
}
