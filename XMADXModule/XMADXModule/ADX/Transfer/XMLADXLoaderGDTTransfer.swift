//
//  XMLADXLoaderGDTTransfer.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import GDTMobSDK

@objc protocol XMLADXLoaderGDTTransferDelegate {
    // 广点通SDK数据获取成功
    @objc optional func gdtAd(_ transfer: XMLADXLoaderGDTTransfer, didSuccessWith dataSource: XMLADXLoaderItem?)
    // 广点通SDK数据获取失败
    @objc optional func gdtAd(_ transfer: XMLADXLoaderGDTTransfer, didFailureWith error: Error?)
}

// 广点通SDK数据获取代理
class XMLADXLoaderGDTTransfer: NSObject {
    weak var delegate: XMLADXLoaderGDTTransferDelegate? = nil
    weak var data    : XMLADXLoaderItem?                = nil { didSet { self.item = data?.item } }
    var item         : XMLAdTypeItemModel?              = nil
}

// MARK: - 启动图 广点通 GDTSplashAdDelegate Method
extension XMLADXLoaderGDTTransfer: GDTSplashAdDelegate {
    func splashAdDidLoad(_ splashAd: GDTSplashAd!) {
        data?.s_gdtAd = splashAd
        item?.price = Double(splashAd.eCPM()) / 100.0
        print("ad-splash gdt item.price: \(item?.price ?? 0)")
        data?.adEnterTime = Date().timeIntervalSince1970
        delegate?.gdtAd?(self, didSuccessWith: self.data)
    }
    
    func splashAdFail(toPresent splashAd: GDTSplashAd!, withError error: Error!) {
        delegate?.gdtAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
}

// MARK: - 激励视频 广点通 GDTRewardedVideoAdDelegate Method
extension XMLADXLoaderGDTTransfer: GDTRewardedVideoAdDelegate {
    func gdt_rewardVideoAdVideoDidLoad(_ rewardedVideoAd: GDTRewardVideoAd) {
        data?.v_gdtAd = rewardedVideoAd
        data?.adEnterTime = Date().timeIntervalSince1970
        delegate?.gdtAd?(self, didSuccessWith: self.data)
    }
    
    func gdt_rewardVideoAd(_ rewardedVideoAd: GDTRewardVideoAd, didFailWithError error: Error) {
        delegate?.gdtAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
}

// MARK: - 信息流 广点通(非模版) GDTUnifiedNativeAdDelegate Method
extension XMLADXLoaderGDTTransfer: GDTUnifiedNativeAdDelegate {
    public func gdt_unifiedNativeAdLoaded(_ unifiedNativeAdDataObjects: [GDTUnifiedNativeAdDataObject]?, error: Error?) {
        if let dataObjects = unifiedNativeAdDataObjects, let obj = dataObjects.first {
            if let isPlaque = self.item?.xml_showType(), isPlaque == .lanPlaque {
                data?.p_gdtNaAd = obj
            } else {
                data?.f_gdtAd = obj
            }
            data?.adEnterTime = Date().timeIntervalSince1970
            delegate?.gdtAd?(self, didSuccessWith: self.data)
        } else {
            delegate?.gdtAd?(self, didFailureWith: error)
            XMLADXReportManager.sendSDKErrorInfo(item, error: error)
        }
    }
}

// MARK: - 插屏 广点通 GDTUnifiedInterstitialAdDelegate Method
extension XMLADXLoaderGDTTransfer: GDTUnifiedInterstitialAdDelegate {
    func unifiedInterstitialSuccess(toLoad unifiedInterstitial: GDTUnifiedInterstitialAd) {
        data?.p_gdtAd = unifiedInterstitial
        data?.adEnterTime = Date().timeIntervalSince1970
        delegate?.gdtAd?(self, didSuccessWith: self.data)
    }
    
    func unifiedInterstitialFail(toLoad unifiedInterstitial: GDTUnifiedInterstitialAd, error: Error) {
        delegate?.gdtAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
}
