//
//  XMLADXLoaderBDTransfer.swift
//  XMADXModule
//
//  Created by yangle<PERSON> on 2020/6/9.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import BaiduMobAdSDK

@objc protocol XMLADXLoaderBDTransferDelegate {
    // 百度百青藤SDK数据获取成功
    @objc optional func bdAd(_ transfer: XMLADXLoaderBDTransfer, didSuccessWith dataSource: XMLADXLoaderItem?)
    // 百度百青藤SDK数据获取失败
    @objc optional func bdAd(_ transfer: XMLADXLoaderBDTransfer, didFailureWith error: Error?)
}

@objc protocol XMLADXLoaderBDTriggerBridgeDelegate {
    // 广告请求成功(桥接)
    @objc optional func nativeAdObjectsSuccessLoad(_ nativeAds: [Any]!, nativeAd: BaiduMobAdNative!)
    // 广告请求失败(桥接)
    @objc optional func nativeAdsFailLoad(_ reason: BaiduMobFailReason, nativeAd: BaiduMobAdNative!)
    // 广告点击(桥接)
    @objc optional func nativeAdClicked(_ nativeAdView: UIView!, nativeAdDataObject object: BaiduMobAdNativeAdObject!)
    // 广告曝光(桥接)
    @objc optional func nativeAdExposure(_ nativeAdView: UIView!, nativeAdDataObject object: BaiduMobAdNativeAdObject!)
    // 广告详情消失(桥接)
    @objc optional func didDismissLandingPage(_ nativeAdView: UIView!)
}

// 百度百青藤SDK数据获取代理
class XMLADXLoaderBDTransfer: NSObject {
    weak var delegate: XMLADXLoaderBDTransferDelegate?      = nil
    weak var data    : XMLADXLoaderItem?                    = nil { didSet { self.item = data?.item } }
    weak var bridge  : XMLADXLoaderBDTriggerBridgeDelegate? = nil
    var item         : XMLAdTypeItemModel?
}

// MARK: - 启动图 百度百青藤 BaiduMobAdSplashDelegate Method
extension XMLADXLoaderBDTransfer: BaiduMobAdSplashDelegate {
    // 应用的APPID
    func publisherId() -> String! { kBDAdSDKAppId }
    
    // 开屏广告请求成功
    func splashAdLoadSuccess(_ splash: BaiduMobAdSplash!) { }
    
    // 开屏广告请求失败
    func splashAdLoadFailCode(_ errCode: String!, message: String!, splashAd splash: BaiduMobAdSplash!) {
        splash.delegate   = nil
        delegate?.bdAd?(self, didFailureWith: nil)
        XMLADXReportManager.sendSDKErrorInfo(item, error: nil)
    }
    
    // 广告展示成功
    func splashSuccessPresentScreen(_ splash: BaiduMobAdSplash!) { }
    
    // 广告展示失败
    func splashlFailPresentScreen(_ splash: BaiduMobAdSplash!, withError reason: BaiduMobFailReason) { }
    
    // 广告被点击
    func splashDidClicked(_ splash: BaiduMobAdSplash!) { }
    
    // 广告详情页消失
    func splashDidDismissLp(_ splash: BaiduMobAdSplash!) { }
    
    // 广告展示结束
    func splashDidDismissScreen(_ splash: BaiduMobAdSplash!) { }
    
    // 广告加载完成
    func splashDidReady(_ splash: BaiduMobAdSplash!, andAdType adType: String!, videoDuration: Int) {
        data?.s_bdAd      = splash
        data?.item?.price = (Double(splash.getECPMLevel()) ?? 0) / 100.0
        print("ad-splash baidu item.price: \(data?.item?.price ?? 0)")
        data?.adEnterTime = Date().timeIntervalSince1970
        delegate?.bdAd?(self, didSuccessWith: self.data)
    }
}

// MARK: - 激励视频 百度百青藤 BaiduMobAdRewardVideoDelegate Method
extension XMLADXLoaderBDTransfer: BaiduMobAdRewardVideoDelegate {
    // 激励视频广告请求成功
    func rewardedAdLoadSuccess(_ video: BaiduMobAdRewardVideo!) {
        
    }
    
    // 激励视频广告请求失败
    func rewardedAdLoadFailCode(_ errCode: String!, message: String!, rewardedAd video: BaiduMobAdRewardVideo!) {
        video.delegate   = nil
        delegate?.bdAd?(self, didFailureWith: nil)
        XMLADXReportManager.sendSDKErrorInfo(item, error: nil)
    }
    // 视频缓存成功
    func rewardedVideoAdLoaded(_ video: BaiduMobAdRewardVideo!) {
        data?.v_bdAd      = video
        data?.adEnterTime = Date().timeIntervalSince1970
        delegate?.bdAd?(self, didSuccessWith: self.data)
    }
    
    // 视频缓存失败
    func rewardedVideoAdLoadFailed(_ video: BaiduMobAdRewardVideo!, withError reason: BaiduMobFailReason) {
        video.delegate   = nil
        delegate?.bdAd?(self, didFailureWith: nil)
        XMLADXReportManager.sendSDKErrorInfo(item, error: nil)
    }
}

// MARK: - 信息流 百度百青藤(非模版) BaiduMobAdNativeAdDelegate Method
extension XMLADXLoaderBDTransfer: BaiduMobAdNativeAdDelegate {
    // 广告请求成功
    func nativeAdObjectsSuccessLoad(_ nativeAds: [Any]!, nativeAd: BaiduMobAdNative!) {
        if let obj = nativeAds.first as? BaiduMobAdNativeAdObject {
            data?.f_bdAd      = obj
            data?.adEnterTime = Date().timeIntervalSince1970
            delegate?.bdAd?(self, didSuccessWith: self.data)
        } else {
            nativeAd.adDelegate = nil
            delegate?.bdAd?(self, didFailureWith: nil)
        }
    }
    
    // 广告请求失败
    func nativeAdsFailLoadCode(_ errCode: String!, message: String!, nativeAd: BaiduMobAdNative!, adObject: BaiduMobAdNativeAdObject!) {
        nativeAd.adDelegate = nil
        delegate?.bdAd?(self, didFailureWith: nil)
        XMLADXReportManager.sendSDKErrorInfo(item, error: nil)
    }
    
    func nativeAdClicked(_ nativeAdView: UIView!, nativeAdDataObject object: BaiduMobAdNativeAdObject!) {
        bridge?.nativeAdClicked?(nativeAdView, nativeAdDataObject: object)
    }
    
    func nativeAdExposure(_ nativeAdView: UIView!, nativeAdDataObject object: BaiduMobAdNativeAdObject!) {
        bridge?.nativeAdExposure?(nativeAdView, nativeAdDataObject: object)
    }
    
    func didDismissLandingPage(_ nativeAdView: UIView!) {
        bridge?.didDismissLandingPage?(nativeAdView)
    }
}
