//
//  XMLADXLoaderJDTransfer.swift
//  XMADXModule
//
//  Created by  周玉杰 on 2021/12/15.
//  Copyright © 2021 ximalaya. All rights reserved.
//

import Foundation
import JADYun

@objc protocol XMLADXLoaderJDTransferDelegate {
    // 京东云SDK数据获取成功
    @objc optional func jdAd(_ transfer: XMLADXLoaderJDTransfer, didSuccessWith dataSource: XMLADXLoaderItem?)
    // 京东云SDK数据获取失败
    @objc optional func jdAd(_ transfer: XMLADXLoaderJDTransfer, didFailureWith error: Error?)
}

// 京东云SDK数据获取代理
class XMLADXLoaderJDTransfer: NSObject {
    weak var delegate: XMLADXLoaderJDTransferDelegate?      = nil
    weak var data    : XMLADXLoaderItem?                    = nil { didSet { self.item = data?.item } }
    var item         : XMLAdTypeItemModel?
}

// MARK: - 启动图(模版) 京东云 JADSplashViewDelegate Method
extension XMLADXLoaderJDTransfer: JADSplashViewDelegate {
    // 广告数据：加载完成
    func jadSplashViewDidLoadSuccess(_ splashView: JADSplashView) {
        data?.s_jdAd = splashView
        data?.item?.price = Double(splashView.price) / 100.0;
        print("ad-splash jad item.price: \(item?.price ?? 0)")
        data?.adEnterTime = Date().timeIntervalSince1970
        delegate?.jdAd?(self, didSuccessWith: self.data)
    }
    // 广告数据：加载失败
    func jadSplashViewDidLoadFailure(_ splashView: JADSplashView, error: (any Error)?) {
        delegate?.jdAd?(self, didFailureWith: error)
        print("ad-splash jad error: \(error?.localizedDescription ?? "未知错误")")
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
    
    func jadSplashViewDidRenderSuccess(_ splashView: JADSplashView) {
        
    }
    
    func jadSplashViewDidRenderFailure(_ splashView: JADSplashView, error: (any Error)?) {
        
    }
    
    func jadSplashViewDidExposure(_ splashView: JADSplashView) {
        
    }
    
    func jadSplashViewDidClick(_ splashView: JADSplashView) {
        
    }
    
    func jadSplashViewDidClose(_ splashView: JADSplashView) {
        
    }
    
    func jadSplashViewDidCloseOtherController(_ splashView: JADSplashView, interactionType: JADInteractionType) {
        
    }
    
    func jadSplashView(_ splashView: JADSplashView, countDown: Int32) {
        
    }
}

// MARK: - 信息流(非模版) 京东云 JADNativeAdDelegate Method
extension XMLADXLoaderJDTransfer: JADNativeAdDelegate {
    // 广告数据：加载成功
    func jadNativeAdDidLoadSuccess(_ nativeAd: JADNativeAd) {
        if (nativeAd.data?.first != nil) {
            data?.f_jdAd = nativeAd
            data?.adEnterTime = Date().timeIntervalSince1970
            delegate?.jdAd?(self, didSuccessWith: self.data)
        } else {
            nativeAd.delegate = nil
            delegate?.jdAd?(self, didSuccessWith: nil)
            XMLADXReportManager.sendSDKErrorInfo(item, error: nil)
        }
    }
    // 广告数据：加载失败
    func jadNativeAdDidLoadFailure(_ nativeAd: JADNativeAd, error: (any Error)?) {
        delegate?.jdAd?(self, didFailureWith: error)
        XMLADXReportManager.sendSDKErrorInfo(item, error: error)
    }
    
    func jadNativeAdDidExposure(_ nativeAd: JADNativeAd) {
        
    }
    
    func jadNativeAdDidClick(_ nativeAd: JADNativeAd, with view: UIView?) {
        
    }
    
    func jadNativeAdDidClose(_ nativeAd: JADNativeAd, with view: UIView?) {
        
    }
    
    func jadNativeAdDidCloseOtherController(_ nativeAd: JADNativeAd, interactionType: JADInteractionType) {
        
    }
    
}
