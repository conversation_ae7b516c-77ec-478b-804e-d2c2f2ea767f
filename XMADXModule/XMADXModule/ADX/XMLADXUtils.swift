//
//  XMLADXUtils.swift
//  tingLite
//
//  Created by ya<PERSON><PERSON> on 2020/5/14.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import XMConfigModule
import XMCategories
import CommonCrypto

// 远程IP本地Key
fileprivate let kXMLADXUtilsClientIPSaveKey: String = "kXMLADXUtilsClientIPSaveKey"
// ADX解密Key
fileprivate let kXMLADXUtilsDecryptionKey: NSString = NSString(string: "1tyt1zuKMloXu/prwDTm5Q==")
// 价格加密解密Key
fileprivate let kXMLADXUtilsPriceKey: String = "chbWyWugtSYjewMAZrVA/w=="

public class XMLADXUtils: NSObject {
    // 对外调用对象
    public  static func shared() -> XMLADXUtils { instance }
    // 实际持有对象
    private static let instance = XMLADXUtils()
    // 远程IP
    private var _clientIP: String = ""
}

// MARK: - 辅助 Method
extension XMLADXUtils {
    // 获取设备MAC地址
    static func getMacAddress() -> String {
        let localMacAddress: String = (UserDefaults.standard.value(forKey: "kXMLADXUtilsMacAddressKey") as? String) ?? ""
        guard localMacAddress.isEmpty else { return localMacAddress }
        let index   = Int32(if_nametoindex("en0"))
        let bsdData = "en0".data(using: .utf8)!
        var mib : [Int32] = [CTL_NET,AF_ROUTE,0,AF_LINK,NET_RT_IFLIST,index]
        var len = 0
        if sysctl(&mib,UInt32(mib.count), nil, &len,nil,0) < 0 {
            return "00:00:00:00:00:00"
        }
        var buffer = [CChar].init(repeating: 0, count: len)
        if sysctl(&mib, UInt32(mib.count), &buffer, &len, nil, 0) < 0 {
            return "00:00:00:00:00:00"
        }
        let infoData = NSData(bytes: buffer, length: len)
        var interfaceMsgStruct = if_msghdr()
        infoData.getBytes(&interfaceMsgStruct, length: MemoryLayout.size(ofValue: if_msghdr()))
        let socketStructStart = MemoryLayout.size(ofValue: if_msghdr()) + 1
        let socketStructData = infoData.subdata(with: NSRange(location: socketStructStart, length: len - socketStructStart))
        let rangeOfToken = socketStructData.range(of: bsdData, options: NSData.SearchOptions(rawValue: 0), in: Range.init(uncheckedBounds: (0, socketStructData.count)))
        let start = rangeOfToken?.count ?? 0 + 3
        let end = start + 6
        let range1 = start..<end
        var macAddressData = socketStructData.subdata(in: range1)
        let macAddressDataBytes: [UInt8] = [UInt8](repeating: 0, count: 6)
        macAddressData.append(macAddressDataBytes, count: 6)
        let macaddress = String.init(format: "%02X:%02X:%02X:%02X:%02X:%02X", macAddressData[0], macAddressData[1], macAddressData[2], macAddressData[3], macAddressData[4], macAddressData[5])
        UserDefaults.standard.setValue(macaddress, forKey: "kXMLADXUtilsMacAddressKey")
        UserDefaults.standard.synchronize()
        return macaddress
    }
}

// MARK: - IP相关 Method
extension XMLADXUtils {
    // 获取投放系统服务器观察到的用户远程IP
    public static func clientIP() -> String {
        var clientIP: String = XMLADXUtils.shared()._clientIP
        if clientIP.isEmpty {
            clientIP = getLocalIP() ?? ""
        }
        return XMLADXUtils.shared()._clientIP
    }
    
    // 更新远程IP
    public static func updateClientIP(_ ip: String) {
        guard ip.isEmpty == false else { return }
        XMLADXUtils.shared()._clientIP = ip
        UserDefaults.standard.setValue(ip, forKey: kXMLADXUtilsClientIPSaveKey)
        UserDefaults.standard.synchronize()
    }
    
    // 获取本地IP
    public static func getLocalIP() -> String? {
        var address : String?
        // Get list of all interfaces on the local machine:
        var ifaddr: UnsafeMutablePointer<ifaddrs>? = nil
        guard getifaddrs(&ifaddr) == 0 else { return nil }
        guard let firstAddr = ifaddr else { return nil }
        // For each interface ...
        for ifptr in sequence(first: firstAddr, next: { $0.pointee.ifa_next }) {
            let interface = ifptr.pointee
            // Check for IPv4 or IPv6 interface:
            let addrFamily = interface.ifa_addr.pointee.sa_family
            if addrFamily == UInt8(AF_INET) || addrFamily == UInt8(AF_INET6) {
                // Check interface name:
                let name = String(cString: interface.ifa_name)
                if name == "en0" {
                    // Convert interface address to a human readable string:
                    var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                    getnameinfo(interface.ifa_addr, socklen_t(interface.ifa_addr.pointee.sa_len),
                                &hostname, socklen_t(hostname.count),
                                nil, socklen_t(0), NI_NUMERICHOST)
                    address = String(cString: hostname)
                }
            }
        }
        freeifaddrs(ifaddr)
        return address
    }
}

// MARK: - 对外 Method
extension XMLADXUtils {
    // 喜马广告上报链接参数替换
    public static func stringByRepalceADParamsWithURL(_ url: String, item: XMLAdTypeItemModel?) -> String {
        var result: String = url
        result = result.replacingOccurrencesInAdWay(of: "OS"        , with: OS())
        result = result.replacingOccurrencesInAdWay(of: "IP"        , with: clientIP())
        result = result.replacingOccurrencesInAdWay(of: "IDFA"      , with: IDFA())
        result = result.replacingOccurrencesInAdWay(of: "IMEI"      , with: IMEI())
        result = result.replacingOccurrencesInAdWay(of: "ANDROIDID" , with: ANDROIDID())
        result = result.replacingOccurrencesInAdWay(of: "ANDROIDID1", with: ANDROIDID1())
        result = result.replacingOccurrencesInAdWay(of: "OAID"      , with: OAID())
        result = result.replacingOccurrencesInAdWay(of: "MAC1"      , with: MAC1())
        result = result.replacingOccurrencesInAdWay(of: "MAC"       , with: MAC())
        result = result.replacingOccurrencesInAdWay(of: "DUID"      , with: DUID())
        result = result.replacingOccurrencesInAdWay(of: "APP"       , with: APP())
        result = result.replacingOccurrencesInAdWay(of: "TS"        , with: TS())
        result = result.replacingOccurrencesInAdWay(of: "UA"        , with: UA())
        if let beginPixel = item?.xml_beginPixel, let endPixel = item?.xml_endPixel,
           Int64(beginPixel.x) >= 0, Int64(endPixel.x) >= 0,
           Int64(beginPixel.y) >= 0, Int64(endPixel.y) >= 0 {
            result = result.replacingOccurrencesInAdWay(of: "DOWN_X", with: "\(Int64(beginPixel.x))")
            result = result.replacingOccurrencesInAdWay(of: "DOWN_Y", with: "\(Int64(beginPixel.y))")
            result = result.replacingOccurrencesInAdWay(of: "UP_X"  , with: "\(Int64(endPixel.x))")
            result = result.replacingOccurrencesInAdWay(of: "UP_Y"  , with: "\(Int64(endPixel.y))")
        }
        result = result.replacingOccurrencesInAdWay(of: " "         , with: " ", isIgnoreFormat: true)    // 空格转码传输
        return result
    }
    
    // 获取操作系统  0-Android 1-iOS 2-Windows Phone 3-其他
    public static func OS() -> String {
        return "1"
    }
    
    // IDFA(iOS设备ID)
    public static func IDFA() -> String {
        let idfa = XMConfig.shared().uuid
        return idfa
    }
    
    // IMEI(Android设备ID)
    public static func IMEI() -> String {
        return ""
    }
    
    // ANDROIDID(Android设备ID)
    public static func ANDROIDID() -> String {
        return ""
    }
    
    // ANDROIDID1(Android设备ID)
    public static func ANDROIDID1() -> String {
        return ""
    }
    
    // OAID(Android设备ID)
    public static func OAID() -> String {
        return ""
    }
    
    // MAC地址(设备硬件地址): 去除分隔符":"的大写MAC地址取MD5摘要
    public static func MAC() -> String {
        return getMacAddress().replacingOccurrences(of: ":", with: "").md5()
    }
    
    // MAC地址(设备硬件地址): 保留分隔符":"的大写MAC地址取MD5摘要
    public static func MAC1() -> String {
        return getMacAddress().md5()
    }
    
    // DUID(WindowsPhone设备ID)
    public static func DUID() -> String {
        return ""
    }
    
    // APP名称
    public static func APP() -> String {
        let app: String = "喜马拉雅极速版"
        return app
    }
    
    // 时间戳: 精确到毫秒级
    public static func TS() -> String {
        let ts: String = "\(Int64(Date().timeIntervalSince1970 * 1000.0))"
        return ts
    }
    
    // 系统User-Agent
    public static func UA() -> String {
        let ua = XMConfig.systemUserAgent()
        return ua
    }
    
    // 设备控制台输出日志
    public static func consoleLog(_ log: String, onlyDebug: Bool = true) {
        if onlyDebug {
            #if DEBUG
            NSLog(">|< |ADX| %@", log)
            #endif
        } else {
            NSLog(">|< |ADX| %@", log)
        }
    }
    
    /// 解密价格字符串
    /// - Parameter priceString: 加密的价格字符串
    /// - Returns: 解密后的价格字符串，失败返回nil
    public static func decryptionPrice(with priceString: String?) -> String? {
        guard let priceString = priceString, !priceString.isEmpty else { return nil }
        
        let privateKey = kXMLADXUtilsPriceKey
        
        guard let keyData = Data(base64Encoded: privateKey),
              let textData = Data(base64Encoded: priceString) else {
            return nil
        }
        
        guard let decodedData = aes256Decrypt(keyData: keyData, data: textData) else {
            return nil
        }
        
        return String(data: decodedData, encoding: .utf8)
    }
    
    /// AES256解密
    /// - Parameters:
    ///   - keyData: 密钥数据
    ///   - data: 待解密数据
    /// - Returns: 解密后的数据，失败返回nil
    private static func aes256Decrypt(keyData: Data, data: Data) -> Data? {
        let keyLen: Int
        
        switch keyData.count {
        case kCCKeySizeAES128:
            keyLen = kCCKeySizeAES128
        case kCCKeySizeAES192:
            keyLen = kCCKeySizeAES192
        case kCCKeySizeAES256:
            keyLen = kCCKeySizeAES256
        default:
            return nil
        }
        
        let dataLength = data.count
        let bufferSize = dataLength + kCCBlockSizeAES128
        var buffer = Data(count: bufferSize)
        var numBytesDecrypted: size_t = 0
        
        let cryptStatus = buffer.withUnsafeMutableBytes { bufferBytes in
            data.withUnsafeBytes { dataBytes in
                keyData.withUnsafeBytes { keyBytes in
                    CCCrypt(
                        CCOperation(kCCDecrypt),
                        CCAlgorithm(kCCAlgorithmAES128),
                        CCOptions(kCCOptionPKCS7Padding | kCCOptionECBMode),
                        keyBytes.bindMemory(to: UInt8.self).baseAddress,
                        keyLen,
                        nil,
                        dataBytes.bindMemory(to: UInt8.self).baseAddress,
                        dataLength,
                        bufferBytes.bindMemory(to: UInt8.self).baseAddress,
                        bufferSize,
                        &numBytesDecrypted
                    )
                }
            }
        }
        
        guard cryptStatus == CCCryptorStatus(kCCSuccess) else {
            return nil
        }
        
        return Data(buffer.prefix(numBytesDecrypted))
    }
    
    /// 加密SDK返回的价格
    /// - Parameter content: 要加密的价格内容
    /// - Returns: 加密后的base64字符串，失败返回nil
    public static func encryptPriceWithAES(content: String) -> String? {
        let key = kXMLADXUtilsPriceKey
        
        guard let contentData = content.data(using: .utf8),
              let keyData = Data(base64Encoded: key) else {
            return nil
        }
        
        let dataLength = contentData.count
        let encryptSize = dataLength + kCCBlockSizeAES128
        var encryptedData = Data(count: encryptSize)
        var actualOutSize: size_t = 0
        
        let cryptStatus = encryptedData.withUnsafeMutableBytes { encryptedBytes in
            contentData.withUnsafeBytes { contentBytes in
                keyData.withUnsafeBytes { keyBytes in
                    CCCrypt(
                        CCOperation(kCCEncrypt),
                        CCAlgorithm(kCCAlgorithmAES128),
                        CCOptions(kCCOptionPKCS7Padding | kCCOptionECBMode),
                        keyBytes.bindMemory(to: UInt8.self).baseAddress,
                        kCCKeySizeAES128,
                        keyBytes.bindMemory(to: UInt8.self).baseAddress,
                        contentBytes.bindMemory(to: UInt8.self).baseAddress,
                        dataLength,
                        encryptedBytes.bindMemory(to: UInt8.self).baseAddress,
                        encryptSize,
                        &actualOutSize
                    )
                }
            }
        }
        
        guard cryptStatus == CCCryptorStatus(kCCSuccess) else {
            return nil
        }
        
        let resultData = Data(encryptedData.prefix(actualOutSize))
        return resultData.base64EncodedString(options: .lineLength64Characters)
    }
}

// MARK: - String拓展 Method
extension String {
    // 喜马广告上报参数替换方式
    public func replacingOccurrencesInAdWay(of occur: String, with content: String, isIgnoreFormat: Bool = false) -> String {
        let realContent: String = content.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        var result: String = ""
        if isIgnoreFormat {
            result = self.replacingOccurrences(of: occur         , with: realContent, options: .caseInsensitive, range: nil)
        } else {
            if self.contains("__\(occur)__") {
                result = self.replacingOccurrences(of: "__\(occur)__", with: realContent, options: .caseInsensitive, range: nil)
            } else if result.contains("{\(occur)}") {
                result = result.replacingOccurrences(of: "{\(occur)}"  , with: realContent, options: .caseInsensitive, range: nil)
            } else {
                result = self
            }
        }
        return result
    }
    
    // 移除喜马广告iting UserAgent
    public func removeiTingUA(_ isInApp: Bool = true) -> String {
        // 1. 保证待处理url不为空
        guard self.isEmpty == false else { return self }
        // 2. 保证待处理url为http/https形式
        guard self.lowercased().contains("http") else { return self }
        var result: String = self
        if isInApp { result += "_xmlad=1" }
        return result
    }
    
    // adxToken解码
    static public func adxDecryption(with token: String) -> String {
        guard token.count > 0 else { return "" }
        let decryptKeyData: Data   = kXMLADXUtilsDecryptionKey.base64DecodedData()
        let decryptTxtData: Data   = NSString(string: token).base64DecodedData()
        let decodedTxtData: Data   = NSData.aes256ParmDecrypt(withKeyData: decryptKeyData, decryptData: decryptTxtData)
        let decodedToken  : String = String(data: decodedTxtData, encoding: .utf8) ?? ""
        return decodedToken
    }
    
    // 拆分URL
    static public func disassemble(_ value: String?) -> (domain: String, params: [String: Any], paramString: String)? {
        guard let url = value, url.count > 0 else { return nil }
        var fDatas: [String] = url.components(separatedBy: "?")
        guard let domain: String = fDatas.first, domain.count > 0 else { return nil }
        // 移除首部数据
        fDatas.removeFirst()
        let paramString: String = fDatas.joined(separator: "?")
        var pValue: [String: Any] = [:]
        for item in paramString.components(separatedBy: "&") {
            var contents: [String] = item.components(separatedBy: "=")
            if let key = contents.first, key.count > 0 {
                if contents.count > 1 {
                    contents.removeFirst()
                    pValue[key] = contents.joined(separator: "=")
                } else {
                    pValue[key] = ""
                }
            }
        }
        return (domain, pValue, paramString)
    }
}

// MARK: - 广告KVO方法专属类
class XMLADOCMethod: NSObject {
    @objc func stop() { }
    @objc func setMuted(_ status: Bool) { }
}
