//
//  XMLAdRequestData.swift
//  XMADXModule
//
//  Created by yangle<PERSON> on 2020/8/10.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import XMNetworkModule
import XMConfigModule
import XMBase
import SwiftyJSON
import RouterModule

// 启动图
public let kXMLAdRequestLoadingDomain = "\(xmEnvironmentUrl(kAdSeServerUrl)!)subapp/loading"
// 播放页
public let kXMLAdRequestDirectDomain  = "\(xmEnvironmentUrl(kAdSeServerUrl)!)subapp/direct"
// 信息流
public let kXMLAdRequestFeedDomain    = "\(xmEnvironmentUrl(kAdSeServerUrl)!)subapp/feed"
// 奖励
public let kXMLAdRequestAwardDomain   = "\(xmEnvironmentUrl(kAdSeServerUrl)!)subapp/award"
// 个人中心
public let kXMLAdRequestPersonDomain  = "\(xmEnvironmentUrl(kAdSeServerUrl)!)subapp/person"
// 弹窗
public let kXMLAdRequestPopupDomain   = "\(xmEnvironmentUrl(kAdSeServerUrl)!)subapp/popup"
// 新版
public let kXMLAdRequestCommonDomain  = "\(xmEnvironmentUrl(kAdSeServerUrl)!)subapp/common"
// 付费解锁
public let kXMLPulAdRequestDomain     = "\(xmEnvironmentUrl(kAdSeServerUrl)!)incentive/subapp/incentive"

// 应用在ADX内ID
private let appId: String = "1463"

public enum XMLAdRequestPositionType {
    case splash
    case playPageBottom(Int64)
    case personBottom
    case signPopup
    case openBoxPopup
    case homeTimePopup
    case homeFeedPopup
    case customPopup([String: Any])    // 自定义弹窗
    case customVideo([String: Any])    // 自定义激励视频
    case customPlaque([String: Any])   // 自定义插屏
    case customFeed ([String: Any])    // 自定义信息流
    
    var name: String {
        switch self {
        case .splash:                  return "loading"
        case .playPageBottom:          return "sub_play_convention_large"
        case .personBottom:            return XMConfig.shared().isMinimalism ? "sub_personal_center_large_immerse" : "sub_personal_center_large"
        case .signPopup:               return "sub_sign_popup_large"
        case .openBoxPopup:            return "sub_award_popup_large"
        case .homeTimePopup:           return "ontimeAward_popup_large"
        case .homeFeedPopup:           return XMConfig.shared().isMinimalism ? "sub_flow_large_immerse" : "sub_flow_large"
        case .customPopup(let params) : return (params["positionName"] as? String) ?? ""
        case .customVideo(let params) : return (params["positionName"] as? String) ?? ""
        case .customPlaque(let params): return (params["positionName"] as? String) ?? ""
        case .customFeed (let params) : return (params["positionName"] as? String) ?? ""
        }
    }
    
    var domain: String {
        switch self {
        case .splash:         return kXMLAdRequestLoadingDomain
        case .playPageBottom: return kXMLAdRequestDirectDomain
        case .personBottom:   return kXMLAdRequestPersonDomain
        case .signPopup:      return kXMLAdRequestPopupDomain
        case .openBoxPopup:   return kXMLAdRequestPopupDomain
        case .homeTimePopup:  return kXMLAdRequestAwardDomain
        case .homeFeedPopup:  return kXMLAdRequestFeedDomain
        case .customPopup:    return kXMLAdRequestCommonDomain
        case .customVideo:    return kXMLAdRequestCommonDomain
        case .customPlaque:   return kXMLAdRequestCommonDomain
        case .customFeed :    return kXMLAdRequestCommonDomain
        }
    }
    
    var isGetMethod: Bool {
        switch self {
        case .splash:         return false
        case .playPageBottom: return false
        case .personBottom:   return false
        case .signPopup:      return false
        case .openBoxPopup:   return false
        case .homeTimePopup:  return false
        case .homeFeedPopup:  return true
        case .customPopup:    return false
        case .customVideo:    return false
        case .customPlaque:   return false
        case .customFeed :    return false
        }
    }
    
    var params: [String : Any] {
        var param: [String: Any] = ["appid"    : appId,
                                    "device"   : "iphone",
                                    "name"     : self.name,
                                    "network"  : XMConfig.getStatisticNetwork(),
                                    "operator" : XMConfig.getStatisticOperator(),
                                    "uid"      : XMSettings.shared().isLoggedIn ? (XMSettings.shared().userModel.user?.uid ?? 0) : -1,
                                    "userAgent": XMConfig.systemUserAgent(),
                                    "version"  : XMSYSTEM_BUNDLEVERSION,
                                    "xt"       : Int64(Date().timeIntervalSince1970 * 1000),
                                    "isDisplayedInScreen": XMLADXManager.shared().isEnterForeground ? 1 : 0]
        switch self {
        case .playPageBottom(let trackId): param["trackId"] = trackId
        case .customPopup(let params),
             .customVideo(let params),
             .customPlaque(let params),
             .customFeed (let params):
            params.enumerated().forEach { (_, item) in
                if item.key.contains("adx_"), let saveKey = item.key.components(separatedBy: "adx_").last, !saveKey.isEmpty {
                    param[saveKey] = item.value
                }
            }
        default: break
        }
        return param
    }
    
    // 加载器类型
    var loaderType: XMLADXLoaderType {
        switch self {
        case .splash:                  return .splash
        case .customVideo(_):          return .video
        case .customPlaque(_):         return .plaque
        default:                       return .flow
        }
    }
    
    static func == (lhs: XMLAdRequestPositionType, rhs: XMLAdRequestPositionType) -> Bool {
        switch (lhs, rhs) {
        case (.splash               , .splash               ): return true
        case (.personBottom         , .personBottom         ): return true
        case (.signPopup            , .signPopup            ): return true
        case (.openBoxPopup         , .openBoxPopup         ): return true
        case (.homeTimePopup        , .homeTimePopup        ): return true
        case (.homeFeedPopup        , .homeFeedPopup        ): return true
        case (.playPageBottom(let l), .playPageBottom(let r)): return l == r
        case (.customPopup(let l)   , .customPopup(let r)   ): return ((l["positionName"] as? String) ?? "") == ((r["positionName"] as? String) ?? "")
        case (.customVideo(let l)   , .customVideo(let r)   ): return ((l["positionName"] as? String) ?? "") == ((r["positionName"] as? String) ?? "")
        case (.customPlaque(let l)  , .customPlaque(let r)  ): return ((l["positionName"] as? String) ?? "") == ((r["positionName"] as? String) ?? "")
        case (.customFeed(let l)    , .customFeed(let r)    ): return ((l["positionName"] as? String) ?? "") == ((r["positionName"] as? String) ?? "")
        default: return false
        }
    }
}

// 广告后台类型
public enum XMLAdType: Int {
    case undefine  = -1
    case himalaya  = 0
    case alimama   = 1
    case google    = 2
    case baidu     = 3     // 百度(接口已屏蔽)
    case gdt       = 4     // 广点通
    case inMob     = 5
    case madHouse  = 6
    case tanx      = 7
    case gdtSplash = 8     // 广点通开屏或广点通模版渲染
    case bu        = 10014 // 穿山甲非模版渲染
    case bune      = 10026 // 穿山甲模版渲染
    case bdne      = 10033 // 百度百青藤模版渲染
    case bd        = 10034 // 百度百青藤自渲染
    case ks        = 10037 // 快手
    case xmlAudio  = 80000 // 喜马拉雅(音频)(极速版自定制)
    case jdTest    = 10096 // 京东 测试环境
    case jdPro     = 10074 // 京东 生产环境
    
    // 此版本是否支持
    var isValid: Bool {
        switch self {
        case .bu,
             .bune,
             .gdt,
             .gdtSplash,
             .himalaya,
             .bd,
             .bdne,
             .xmlAudio,
             .jdTest,
             .jdPro:
            return true
        default:  return false
        }
    }
    
    // 此版本是否喜马拉雅物料
    var isHimalaya: Bool {
        switch self {
        case .himalaya:
            return true
        default:  return false
        }
    }
    
    // 此版本是否喜马拉雅(SDK)物料
    var isXML: Bool {
        switch self {
        case .xmlAudio:
            return true
        default:  return false
        }
    }
    
    // 此版本是否穿山甲物料
    var isBU: Bool {
        switch self {
        case .bu,
             .bune:
            return true
        default:  return false
        }
    }
    
    // 此版本是否广点通物料
    var isGDT: Bool {
        switch self {
        case .gdt,
             .gdtSplash:
            return true
        default:  return false
        }
    }
    
    // 此版本是否百度百青藤物料
    var isBD: Bool {
        switch self {
        case .bd,
             .bdne:
            return true
        default:  return false
        }
    }
    
    // 此版本是否京东物料
    var isJD: Bool {
        switch self {
        case .jdTest,
             .jdPro:
            return true
        default:  return false
        }
    }
    
    // SDK在喜马业务上报所对应Type
    public var sdkType: Int {
        switch self {
        case .gdt,
             .gdtSplash: return 1
        case .bu,
             .bune     : return 2
        case .himalaya,
             .xmlAudio : return 3
        case .bd,
             .bdne     : return 5
        case .ks       : return 6
        case .jdTest,
             .jdPro: return 7
        default        : return 0
        }
    }
}

// 广告加载阶段
public enum XMLAdLoaderPhase: Int {
    case asyncLoader   = 0  // 普通异步SDK并发加载
    case preLoader     = 1  // 预处理SDK并发加载

    // 此版本是否提前请求
    var preReq: Bool {
        switch self {
        case .preLoader: return true
        default        : return false
        }
    }
}

// sdk广告加载状态
public enum XMLAdRequestStatus: Int {
    case normal = 0
    case loading = 1
    case success = 2
    case faild = 3
}

// 广告后台类型
// http://ops.ximalaya.com/api-manager-backend/router-page/projectApiLook/353/59568?activeTab=0
public class XMLAdTypeItemModel {
    var adBucketIds           : String = ""          // 焦点图使用字段
    var adMark                : String = ""          // 广告显示时的样式图片(旧版广告logo)
    var adUserType            : String = ""          // 广告类型 "SALE":销售广告 "PAY":付费内容广告 "OTHER":其他广告
    var adid                  : String = ""          // 物料ID
    var adpr                  : String = ""          // 品效中的竞价价格
    var adtype                : Int = 0              // 广告数据源，和dspId一致。例如 0：喜马拉雅后台物料
    var apkUrl                : String = ""          // 下载APP链接，apk包地址
    var auto                  : Bool = false         // 是否静默下载APP
    var bgCover               : String = ""          // 背景图地址
    var bootUps               : XMLAdBootUps? = nil  // 多屏长图
    var bucketIds             : String = ""          // 分桶信息
    var buttonText            : String = ""          // 按钮文案
    var commonReportMap       : String = ""          // 广告上报透传信息
    var chargeMethod          : String = ""          // 广告投放方式
    var clickTokens           : [String] = []        // 广告点击时携带Token
    var clickType             : Int = 0              // 点击动作 1:可以点击 2:不可点击
    var comboAd               : Bool = false         // 是否是套餐
    var cover                 : String = ""          // [重要]图片类素材的图片地址，或是视频类素材的备选图地址（在视频下载失败时展示该图片）
    var description           : String = ""          // 广告副标题
    var displayType           : Int = 1              // [忽略]展示类型：此处默认是1
    var dpRealLink            : String = ""          // deeplink或UniversalLink
    var dsp                   : Bool = false         // 是否是DSP
    var dspPositionId         : String = ""          // SDK接入DSP的请求ID
    var dynamicCover          : String = ""          // 动态图地址
    var endAt                 : Int64 = 0            // 终止投放时间
    var floatingLayerGuideCopy: String = ""          // 非全屏开机静态启动图 动态浮层引导文案
    
    var shareData             : String = ""
    var isShareFlag           : Bool = false         // 是否分享的标识
    var morePics              : [String] = []        // 撒花样式iconURL
    var showTokens            : [String] = []        // 广告展示时携带Token
    var showTokenEnable       : Bool = false         // 广告展示时是否需要携带Token
    var clickTokenEnable      : Bool = false         // 广告点击时是否需要携带Token
    var recSrc                : String = ""          // CTR算法追踪字段
    var recTrack              : String = ""          // CTR算法追踪字段
    var link                  : String = ""          // 点击统计地址
    var realLink              : String = ""          // 落地页地址
    var iconUrl               : String = ""          // icon地址
    var isLandScape           : Bool = false         // 是否支持横屏
    var isInternal            : Int = 0              // 是否内链； 0：否；1：是
    var planId                : String = ""          // 计划ID
    var showstyle             : Int = 0              // 广告位样式ID 46:图文样式 47:纯音贴
    var name                  : String = ""          // 广告标题
    var scheme                : String = ""          // 开机大图不适用
    var linkType              : Int = 0              // 跳转类型：1、跳转落地页；2、下载APP
    var openlinkType          : Int = 0              // 广告跳转打开类型 0:应用内打开 1:第三方浏览器打开 2:喜马拉雅活动 3:拨打电话
    var loadingShowTime       : String = ""          // [忽略]开机大图跳过广告倒计时，单位ms
    
    var lbs                   : Bool = false         // 是否商圈定向
    var responseId            : String = ""          // 用于跟踪整个session的全局唯一id
    var inScreenSource        : Int = 0              // 0 不需要水印 1 文字 2 logo
    var materialProvideSource : String = ""          // 水印文字内容或logo地址
    var guidanceText          : String = ""          // 引导文案
    var soundUrl              : String = ""          // 纯音贴声音返回
    var positionId            : Int64? = nil         // 广告代码位信息所匹配的ID
    
    var isNeedPreRequest      : Bool = false         // 是否需要预加载
    var preRequestEffectTime  : Double = 0.0         // 预加载有效时间
    
    var thirdClickStatUrls    : [String] = []        // 广告点击时需要数据上报
    var thirdShowStatUrls     : [String] = []        // 广告展示时需要数据上报(与thirdStatUrl)全部触发
    var thirdStatUrl          : String = ""          // 广告展示时需要数据上报(与thirdShowStatUrls)全部触发
    var videoCover            : String = ""          // [重要]视频类素材的视频地址(开屏)
    var videoUrl              : String = ""          // [重要]视频类素材的视频地址(非开屏)
    var videoDurationTime     : Double = 0.0         // 视频时长（服务端预留字段，目前只提供默认值0）
    var volume                : Double = 0.0         // 广告音量百分比(范围0~100)
    var zipUrl                : String = ""          // H5资源包url
    var position              : Int = 0              // 信息流类广告的展示位置
    var isTrueExposure        : Bool = false         // 是否需要上报真实曝光
    var subCover              : String = ""          // [忽略]子样式的素材图片地址
    var subName               : String = ""          // [忽略]字样式的素材文字标题内容
    var jumpModeType          : Int = 0              // 开屏广告交互样式 0：无任何交互模式 3：按钮变色模式 4：滑动模式 9：摇一摇模式
    var clickableAreaType     : Int = 0              // 开屏广告可点击区域 1：全屏可点，即不限点 2：区域可点，即限点
    var adJumpText            : String = ""          // 开屏广告引导文案
    
    var slotRealBid           : Bool = false         // 标识该广告是否是实时竞价的
    var isMobileRtb           : Bool = false         // 是否客户端竞价广告
    var slotAdm               : String = ""          // 广点通或穿山甲返回的广告内容，直接透传给客户端
    
    private var _priceEncrypt : String = ""          // 原始价格加密字符串存储
    var priceEncrypt: String {                       // 价格加密字符串
        get {
            if xml_isSDKAd() {
                // SDK广告需要加密price（取4位小数）
                let priceString = String(format: "%.4f", _price)
                return XMLADXUtils.encryptPriceWithAES(content: priceString) ?? ""
            } else {
                // 非SDK广告直接返回存储的值
                return _priceEncrypt
            }
        }
        set {
            _priceEncrypt = newValue
        }
    }
    private var _price        : Double = 0.0         // 原始价格值
    var price: Double {
        get {
            if xml_isSDKAd() {
                // SDK广告直接返回原始price字段
                return _price
            } else {
                // 非SDK广告需要解密priceEncrypt
                if let decryptedPrice = XMLADXUtils.decryptionPrice(with: priceEncrypt) {
                    return Double(decryptedPrice) ?? 0.0
                }
                return 0.0
            }
        }
        set {
            _price = newValue
        }
    }
    var rankLevel             : Int = 0              // 排序等级
    
    var bidMinPrice: String = ""                  // 底价字符串
    var bidMinPriceValue: Double {                // 底价数值
        get {
            if xml_isSDKAd() {
                if let decryptedPrice = XMLADXUtils.decryptionPrice(with: self.bidMinPrice) {
                    return Double(decryptedPrice) ?? 0.0
                }
                return 0.0
            }
            return 0.0
        }
    }
    
    var requestStatus         : XMLAdRequestStatus = .normal // 竞价请求状态
    
    var businessExtraInfo     : XMLBusinessExtraInfoData? = nil // 播音类广告合规弹窗信息

    // 代码位名称(非网络获取)
    var xml_posiName         : String           = ""
    // 代码位优先级(0最大)
    var xml_index            : Int              = Int.max
    // 代码位请求用户ID
    var xml_userId           : UInt             = 0
    // 对应广告源数据是否有缓存
    var xml_inCache          : Bool             = false
    // 指定渲染尺寸
    var xml_renderSize       : CGSize?          = nil
    // 是否为ADX数据
    var xml_isADX            : Bool             = true
    
    // 广告提供方名称
    var xml_advertiserName: String {
        let adType = xml_AdType()
        switch adType {
        case .bu, .bune:
            return "穿山甲"
        case .gdt, .gdtSplash:
            return "广点通"
        case .bd, .bdne:
            return "百度"
        case .jdTest, .jdPro:
            return "京东"
        case .ks:
            return "快手"
        case .himalaya:
            return "喜马拉雅"
        case .xmlAudio:
            return "喜马拉雅"
        default:
            return "未知"
        }
    }
    // 该条Item客户端创建时间
    var xml_createTime       : TimeInterval     = 0.0
    // 该条Item当前加载阶段
    var xml_loaderPhase      : XMLAdLoaderPhase = .asyncLoader
    // 该条Item使用业务场景
    var xml_busiScene        : XMLADXBusiScene  = .normal
    // 该条Item开始点击时像素
    var xml_beginPixel       : CGPoint?         = nil
    // 该条Item结束点击时像素
    var xml_endPixel         : CGPoint?         = nil
    // 该条Item开始播放时间
    var xml_playTime         : TimeInterval     = 0.0
    // 该条Item结束播放时间
    var xml_endTime          : TimeInterval     = 0.0
    // 该条Item播放状态
    var xml_playStatus       : XMLAdItemStatus  = .default
    // 该条Item播放是否被手动暂停
    var xml_manualSkip       : Bool             = false
    // 该条Item播放是否已经ShowOb上报
    var xml_isShowObReported : Bool             = false
    // 该条Item播放是否已经tingShow上报
    var xml_isTingShowReport : Bool             = false
    // 该条Item是否为无声图文贴片(仅供上报使用)
    var xml_isSilentPatch    : Bool             = false
    // 该条Item业务展示是否为沉浸式贴片视频样式(仅供上报使用)
    var xml_isImmerseVPatch  : Bool             = false
    // 该条Item业务为书籍id(仅供上报使用)
    var xml_bookId           : Int              = 0
    // 是否为车友电台数据
    var xml_isPlayOnRadio    : Bool             = false
    
    // 代码位喜马业务ID
    var xml_positionId: Int64 {
        return positionId ?? XMLAdPositionInfoManager.convertToPositionId(xml_posiName)
    }
    
    // 标记当前广告是否上报过播放的tingshow 目前只有百度激励视频广告使用
    var hasVideoPlayTingShowed: Bool = false
    
    // 该条数据是否预加载有效
    public func xml_isPreValid() -> Bool {
        return isNeedPreRequest && (Date().timeIntervalSince1970 - xml_createTime) < preRequestEffectTime
    }
    
    public func update(with extraInfo: [String: Any]? = nil) {
        self.xml_renderSize = self.xml_showType().isVerticalPatch ? extraInfo?["verRenderSize"] as? CGSize : extraInfo?["renderSize"] as? CGSize
        self.xml_busiScene  = XMLADXBusiScene(rawValue: (extraInfo?["busiScene"] as? Int) ?? 0) ?? .normal
        self.xml_bookId = (extraInfo?["bookId"] as? Int) ?? 0
        self.xml_isPlayOnRadio = (extraInfo?["adx_isPlayOnRadio"] as? String) == "true"
    }
    
    public func xml_AdType() -> XMLAdType {
        var type: XMLAdType = XMLAdType(rawValue: self.adtype) ?? .undefine
        // 喜马物料(音频特殊处理，走并发)
        if type == .himalaya, !self.soundUrl.isEmpty { type = .xmlAudio }
        return type
    }
    
    public func xml_isSDKValid() -> Bool {
        if xml_AdType().isBU || xml_AdType().isGDT || xml_AdType().isBD || xml_AdType().isJD {
            return self.dspPositionId.count > 0
        } else if xml_AdType().isXML { // 特殊标记(喜马物料类型(非兜底)添加于此)
            return true
        }
        return true
    }
    
    public func xml_isSDKAd() -> Bool {
        if xml_AdType().isBU || xml_AdType().isGDT || xml_AdType().isBD || xml_AdType().isJD {
            return true
        } else if xml_AdType().isXML { // 特殊标记(喜马物料类型(非兜底)添加于此)
            return true
        }
        return false
    }
    
    public func xml_isxmVideo() -> Bool {
        return videoCover.count > 0 || videoUrl.count > 0
    }
    
    public func xml_isxmAudio() -> Bool {
        return adtype == 0 && soundUrl.count > 0
    }
    
    public func xml_isTemplate() -> Bool {
        return xml_AdType() == .bune || xml_AdType() == .bdne || xml_AdType() == .gdtSplash
    }
    
    public func xml_isNeedAdjuestMaxLimitTime(_ isVideo: Bool) -> Bool {
        return xml_isSDKValid() && (isVideo || xml_isTemplate())
    }
    
    public func xml_showType() -> XMLADXShowStyle {
        return XMLADXShowStyle(rawValue: self.showstyle) ?? .undefine
    }
    
    public func xml_isxmCanClick() -> Bool {
        return clickType == 1
    }
    
    public func skipAdItemPlay() {
        self.xml_manualSkip = true
    }
    
    public func adItemStatus() -> XMLAdItemStatus {
        return self.xml_playStatus
    }
    
    // 获取合适的本地展示Token
    public func xml_validShowTokenInfo() -> (needMore: Bool, token: String) {
        guard self.showTokenEnable else { return (false, "") }
        self.showTokens = self.showTokens.filter({ !$0.isEmpty })
        guard self.showTokens.isEmpty == false else { return (true, "") }
        let token: String = showTokens.removeFirst()
        return (false, token)
    }
    
    // 获取合适的本地点击Token
    public func xml_validClickTokenInfo() -> (needMore: Bool, token: String) {
        guard self.clickTokenEnable else { return (false, "") }
        self.clickTokens = self.clickTokens.filter({ !$0.isEmpty })
        guard self.clickTokens.isEmpty == false else { return (true, "") }
        let token: String = clickTokens.removeFirst()
        return (false, token)
    }
    
    public class func statisticObject(_ positionName: String) -> XMLAdTypeItemModel {
        let item = XMLAdTypeItemModel(JSON(), responseId: "1")
        item.xml_posiName = positionName
        item.xml_isADX    = false
        return item
    }
    
    public class func buAd(_ slotId: String, posiName: String, isNEAd: Bool, extraInfo: [String: Any]? = nil) -> XMLAdTypeItemModel {
        let item: XMLAdTypeItemModel = XMLAdTypeItemModel(JSON(), responseId: "1")
        item.dspPositionId = slotId
        item.xml_posiName  = posiName
        item.adtype        = isNEAd ? XMLAdType.bune.rawValue : XMLAdType.bu.rawValue
        item.xml_isADX     = false
        item.update(with: extraInfo)
        return item
    }
    
    public class func ad(_ slotId: String, posiName: String, extraInfo: [String: Any]? = nil) -> XMLAdTypeItemModel {
        let item: XMLAdTypeItemModel = XMLAdTypeItemModel(JSON(), responseId: "1")
        item.dspPositionId = slotId
        item.xml_posiName  = posiName
        item.xml_isADX     = false
        item.showstyle     = (extraInfo?["showStyle"] as? Int) ?? 0
        item.update(with: extraInfo)
        let sdkPlatform     : String = (extraInfo?["sdkPlatform"] as? String) ?? ""
        let isRenderTemplate: Bool   = ((extraInfo?["renderTemplate"] as? String) ?? "0") == "1"
        if sdkPlatform == "GDT" {
            item.adtype    = isRenderTemplate ? XMLAdType.gdtSplash.rawValue : XMLAdType.gdt.rawValue
        } else if sdkPlatform == "BD" {
            item.adtype    = isRenderTemplate ? XMLAdType.bdne.rawValue : XMLAdType.bd.rawValue
        } else {
            item.adtype    = isRenderTemplate ? XMLAdType.bune.rawValue : XMLAdType.bu.rawValue
        }
        return item
    }
    
    public class func ad(_ slotId: String, posiName: String, adType: XMLAdType) -> XMLAdTypeItemModel {
        let item: XMLAdTypeItemModel = XMLAdTypeItemModel(JSON(), responseId: "1")
        item.dspPositionId = slotId
        item.xml_posiName  = posiName
        item.xml_isADX     = false
        item.adtype        = adType.rawValue
        return item
    }
    
    public init(_ json: JSON, responseId: String) {
        let jsonDictionary = json.dictionaryValue
        self.adBucketIds     = jsonDictionary["adBucketIds"]?.stringValue ?? ""
        self.adMark          = jsonDictionary["adMark"]?.stringValue ?? ""
        self.adUserType      = jsonDictionary["adUserType"]?.stringValue ?? ""
        self.adid            = jsonDictionary["adid"]?.stringValue ?? ""
        self.adpr            = jsonDictionary["adpr"]?.stringValue ?? ""
        self.adtype          = jsonDictionary["adtype"]?.intValue ?? 0
        self.apkUrl          = jsonDictionary["apkUrl"]?.stringValue ?? ""
        self.auto            = jsonDictionary["auto"]?.boolValue ?? false
        self.bgCover         = jsonDictionary["bgCover"]?.stringValue ?? ""
        self.bootUps         = XMLAdBootUps(jsonDictionary["bootUps"] ?? JSON())
        self.bucketIds       = jsonDictionary["bucketIds"]?.stringValue ?? ""
        self.buttonText      = jsonDictionary["buttonText"]?.stringValue ?? ""
        self.commonReportMap = jsonDictionary["commonReportMap"]?.stringValue ?? ""
        self.chargeMethod    = jsonDictionary["chargeMethod"]?.stringValue ?? ""
        self.clickTokens     = jsonDictionary["clickTokens"]?.arrayValue.map({ $0.stringValue }) ?? []
        self.clickType       = jsonDictionary["clickType"]?.intValue ?? 0
        self.comboAd         = jsonDictionary["comboAd"]?.boolValue ?? false
        self.cover           = jsonDictionary["cover"]?.stringValue ?? ""
        self.description     = jsonDictionary["description"]?.stringValue ?? ""
        self.displayType     = jsonDictionary["displayType"]?.intValue ?? 1
        self.dpRealLink      = jsonDictionary["dpRealLink"]?.stringValue ?? ""
        self.dsp             = jsonDictionary["dsp"]?.boolValue ?? false
        self.dspPositionId   = jsonDictionary["dspPositionId"]?.stringValue ?? ""
        self.dynamicCover    = jsonDictionary["dynamicCover"]?.stringValue ?? ""
        self.endAt           = jsonDictionary["endAt"]?.int64 ?? 0
        self.floatingLayerGuideCopy = jsonDictionary["floatingLayerGuideCopy"]?.stringValue ?? ""

        self.iconUrl = jsonDictionary["iconUrl"]?.stringValue ?? ""
        self.guidanceText = jsonDictionary["guidanceText"]?.stringValue ?? ""
        self.shareData = jsonDictionary["shareData"]?.stringValue ?? ""
        self.isShareFlag = jsonDictionary["isShareFlag"]?.boolValue ?? false
        
        self.morePics = jsonDictionary["morePics"]?.arrayValue.map({ $0.stringValue }) ?? []
        self.showTokens = jsonDictionary["showTokens"]?.arrayValue.map({ $0.stringValue }) ?? []
        self.showTokenEnable = jsonDictionary["showTokenEnable"]?.boolValue ?? false
        self.clickTokenEnable = jsonDictionary["clickTokenEnable"]?.boolValue ?? false
        self.recSrc = jsonDictionary["recSrc"]?.stringValue ?? ""
        self.recTrack = jsonDictionary["recTrack"]?.stringValue ?? ""
        self.link = jsonDictionary["link"]?.stringValue ?? ""
        if self.link.count <= 0 {
            self.link = jsonDictionary["linkUrl"]?.stringValue ?? ""
        }
        self.realLink = jsonDictionary["realLink"]?.stringValue ?? ""
        self.isLandScape = jsonDictionary["isLandScape"]?.boolValue ?? false
        self.isInternal = jsonDictionary["isInternal"]?.intValue ?? 0
        self.planId = jsonDictionary["planId"]?.stringValue ?? ""
        self.showstyle = jsonDictionary["showstyle"]?.intValue ?? 0
        self.name = jsonDictionary["name"]?.stringValue ?? ""
        self.scheme = jsonDictionary["scheme"]?.stringValue ?? ""
        self.linkType = jsonDictionary["linkType"]?.intValue ?? 0
        self.openlinkType = jsonDictionary["openlinkType"]?.intValue ?? 0
        self.loadingShowTime = jsonDictionary["loadingShowTime"]?.stringValue ?? ""
        
        self.lbs = jsonDictionary["lbs"]?.boolValue ?? false
        self.responseId = jsonDictionary["responseId"]?.stringValue ?? ""
        self.responseId = responseId
        self.inScreenSource = jsonDictionary["inScreenSource"]?.intValue ?? 0
        self.materialProvideSource = jsonDictionary["materialProvideSource"]?.stringValue ?? ""
        self.positionId = jsonDictionary["positionId"]?.int64
        
        self.isNeedPreRequest = jsonDictionary["isNeedPreRequest"]?.boolValue ?? false
        self.preRequestEffectTime = jsonDictionary["preRequestEffectTime"]?.doubleValue ?? 0.0
        self.soundUrl = jsonDictionary["soundUrl"]?.stringValue ?? ""

        self.thirdClickStatUrls = jsonDictionary["thirdClickStatUrls"]?.arrayValue.map({ $0.stringValue }) ?? []
        self.thirdShowStatUrls = jsonDictionary["thirdShowStatUrls"]?.arrayValue.map({ $0.stringValue }) ?? []
        self.thirdStatUrl = jsonDictionary["thirdStatUrl"]?.stringValue ?? ""
        self.videoCover = jsonDictionary["videoCover"]?.stringValue ?? ""
        self.videoUrl = jsonDictionary["videoUrl"]?.stringValue ?? ""
        self.videoDurationTime = jsonDictionary["videoDurationTime"]?.doubleValue ?? 0.0
        self.volume = jsonDictionary["volume"]?.doubleValue ?? 0.0
        self.zipUrl = jsonDictionary["zipUrl"]?.stringValue ?? ""
        self.position = jsonDictionary["position"]?.intValue ?? 0
        self.isTrueExposure = jsonDictionary["isTrueExposure"]?.boolValue ?? false
        self.subCover = jsonDictionary["subCover"]?.stringValue ?? ""
        self.subName = jsonDictionary["subName"]?.stringValue ?? ""
        self.jumpModeType = jsonDictionary["jumpModeType"]?.intValue ?? 0
        self.clickableAreaType = jsonDictionary["clickableAreaType"]?.intValue ?? 0
        self.adJumpText = jsonDictionary["adJumpText"]?.stringValue ?? ""
        self.slotRealBid = jsonDictionary["slotRealBid"]?.boolValue ?? false
        self.isMobileRtb = jsonDictionary["isMobileRtb"]?.boolValue ?? false
        self.slotAdm = jsonDictionary["slotAdm"]?.stringValue ?? ""
        self.priceEncrypt = jsonDictionary["priceEncrypt"]?.stringValue ?? ""
        self._price = jsonDictionary["price"]?.doubleValue ?? 0.0
        self.rankLevel = jsonDictionary["rankLevel"]?.intValue ?? 0
        self.bidMinPrice = jsonDictionary["bidMinPrice"]?.stringValue ?? ""
        self.businessExtraInfo = XMLBusinessExtraInfoData(jsonDictionary["businessExtraInfo"] ?? JSON())
        // 当前用户ID
        self.xml_userId     = XMSettings.shared().userModel.user?.uid ?? 0
        // 当条Item客户端生成时间
        self.xml_createTime = Date().timeIntervalSince1970
        // 若JSON中存在positionName则直接赋值
        self.xml_posiName   = jsonDictionary["positionName"]?.stringValue ?? ""
    }
    
    public func toStaticParams(_ toutiaoType: String, logType: String, positionName: String = "") -> [String: Any] {
        var modifiedCommonReportMap = commonReportMap
        
        // 如果是移动端RTB广告，需要替换commonReportMap中的价格字段
        if isMobileRtb && xml_isSDKAd() && !commonReportMap.isEmpty {
            if let data = commonReportMap.data(using: .utf8),
               let json = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                var mutableJson = json
                mutableJson["adxRtbSettlementPrice"] = priceEncrypt
                mutableJson["adxRtbRealTimePrice"] = priceEncrypt
                
                if let modifiedData = try? JSONSerialization.data(withJSONObject: mutableJson, options: []),
                   let modifiedString = String(data: modifiedData, encoding: .utf8) {
                    modifiedCommonReportMap = modifiedString
                }
            }
        }
        
        return ["adSource": "\(self.adtype)",
                "adpr": adpr,
                "albumId": RouterBridge(nil).playerCurrentAlbumId() ?? 0,
                "appId": appId,
                "broadcastId": 0,
                "bucketIds": bucketIds,
                "categoryId": 0,
                "completeType": 0,
                "frames": 0,
                "isDisplayedInScreen": XMLADXManager.shared().isEnterForeground ? 1 : 0,
                "keywordId": 0,
                "rec_src": recSrc,
                "rec_track": recTrack,
                "responseId": responseId,
                "showPlace": 0,
                "showToken": "",
                "showType": showstyle,
                "sourcePage": 0,
                "subcategoryId": 0,
                "time": "\(Int64(Date().timeIntervalSince1970 * 1000))",
                "toutiaoType": toutiaoType,
                "trackId": RouterBridge(nil).playerCurrentTrackId() ?? 0,
                "adItemId": adid,
                "finishSeconds": 0,
                "logType": logType,
                "positionName": xml_posiName,
                "totalSeconds": 0,
                "dspPositionId": dspPositionId,
                "commonReportMap": modifiedCommonReportMap,
                "positionId": xml_positionId,
                "mode": XMConfig.shared().isClassic ? "classic" : "immerse",
                "isPlayOnRadio": xml_isPlayOnRadio ? "true" : "false",
                "vender": xml_AdType().sdkType]
    }
    
    public func toClickParams(_ toutiaoType: String, logType: String, positionName: String) -> [String: Any] {
        return ["appid": appId,
                "device": XMConfig.shared().simpleModel,
                "adId": adid,
                "b_idfa": XMConfig.shared().idfa,
                "clickToken": "",
                "logType": logType,
                "time": "\(Int64(Date().timeIntervalSince1970 * 1000))",
                "trackId": RouterBridge(nil).playerCurrentTrackId() ?? 0,
                "adItemId": adid,
                "responseId": responseId,
                "adSource": adtype,
                "positionName": xml_posiName,
                "showPlace": "0",
                "toutiaoType": toutiaoType,
                "dspPositionId": dspPositionId,
                "positionId": xml_positionId,
                "mode": XMConfig.shared().isClassic ? "classic" : "immerse",
                "isPlayOnRadio": xml_isPlayOnRadio ? "true" : "false",
                "vender": xml_AdType().sdkType]
    }
    
    public func toV2UploaderParams(_ status: Int, useTime: TimeInterval) -> [String: Any] {
        var data: [String: Any] = ["adid"         : adid,
                                   "dspPositionId": dspPositionId,
                                   "useTime"      : "\(Int64(useTime))",
                                   "preRequest"   : xml_loaderPhase.preReq,
                                   "status"       : "\(status)"]
        if status != 5001 { data["sdkType"] = xml_AdType().sdkType }
        return ["appId": appId,
                "appName": "adMaterialStatus",
                "serviceId": "adRequestStatus",
                "deviceId": XMConfig.shared().uuid,
                "terminal": "iOS",
                "version": XMSYSTEM_BUNDLEVERSION,
                "responseId": responseId,
                "ts": "\(Int64(Date().timeIntervalSince1970 * 1000))",
                "positionId": xml_positionId,
                "isDisplayedInScreen": XMLADXManager.shared().isEnterForeground,
                "data": data]
    }
    
    public func toMaterialsUploaderParams(_ materials: [String: Any], goal: Int, type: Int) -> [String: Any] {
        let materialsV: String = convertObj2JSONString(materials)
        let url  : String = getPureResource((materials["coverUrl"] as? String) ?? "")
        let title: String = (materials["title"] as? String) ?? ""
        let video: String = getPureResource((materials["videoUrl"] as? String) ?? "")
        let data: [String: Any] = ["appName": "adShowMaterials",
                                   "serviceId": "adShowMaterials",
                                   "responseId": responseId,
                                   "ts": "\(Int64(Date().timeIntervalSince1970 * 1000))",
                                   "isDisplayedInScreen": XMLADXManager.shared().isEnterForeground,
                                   "positionId": xml_positionId,
                                   "adId": adid,
                                   "slotId": dspPositionId,
                                   "sdk": xml_AdType().sdkType,
                                   "appId": appId,
                                   "materials": materialsV,
                                   "imageMd5": url.isEmpty ? "" : url.md5(),
                                   "titleMd5": title.isEmpty ? "" : title.md5(),
                                   "videoMd5": video.isEmpty ? "" : video.md5(),
                                   "clickUrl": "",
                                   "promotionGoal": goal,
                                   "type": type]
        return data
    }
    
    public func toVideoParams() -> [String: Any] {
        return ["videoMs"        : Int64(videoDurationTime * 1000),
                "responseId"     : responseId,
                "adAppId"        : "1463",
                "positionId"     : xml_positionId,
                "adItemId"       : adid,
                "adSource"       : adtype,
                "commonReportMap": commonReportMap]
    }
    
    public class func isSupportADX(_ positionName: String) -> Bool {
        let item = XMLAdTypeItemModel(JSON(), responseId: "1")
        item.xml_posiName = positionName
        return item.xml_positionId != 0
    }
    
    public func convertObj2JSONString(_ obj: Any) -> String {
        var result: String = ""
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: obj, options: .prettyPrinted)
            let json = try JSON(data: jsonData, options: .mutableLeaves)
            result = json.rawString() ?? ""
        } catch let error { XMLADXUtils.consoleLog("ConvertObj2JSONString Failure, error = \(error.localizedDescription)") }
        return result
    }
    
    public func getPureResource(_ url: String) -> String {
        var result: String = url
        if result.contains("?") { result = result.components(separatedBy: "?").first ?? "" }
        return result
    }
}

// 付费解锁广告数据
public class XMLPaidUnlockAdTypeItemModel: XMLAdTypeItemModel {
    // 当前解锁次数
    public var pul_unlockTimes            : Int              = Int.max
    // 视频可关闭时间
    public var pul_videoCloseTime         : Int              = Int.max
    // 视频总时间
    public var pul_videoTime              : Int              = Int.max
    // 定帧总时间
    public var pul_endFrameTime           : Int              = Int.max
    // slotIds(三方代码位ID)
    public var pul_slotIds                : [String]         = []
    // dspId和slotIds对应(三方adType类型)
    public var pul_dspIds                 : [Int]            = []
    // dsp物料的类型 1：激励视频 2：全屏视频 3：draw信息流
    public var pul_dspAdTypes             : [Int]            = []
    // 当前锁使用的物料
    public var pul_curLoadIndex           : Int?             = nil
    // 待解锁专辑ID
    public var pul_unlockAlbumId          : UInt             = 0
    // 待解锁声音ID
    public var pul_unlockTrackId          : Int64            = 0
    // dsp物料信息
    public var pul_dspDatas               : [XMLPaidUnlockAdTypeDspData] = []
    // 来源代码位(非网络获取)
    public var pul_sourceName             : String           = ""
    // 当前锁使用的JSON串
    public var pul_json                   : JSON             = JSON()
    // 当前请求类型 null/0 为会员专辑 1 免费专辑
    public var pul_requestType            : Int              = 0
    // 当前请求待解锁音频Ids
    public var pul_trackIds               : [Int64]          = []
    
    /*
    dspId    : 广点通 4 ;穿山甲 10014 ;快手 10037
    dspAdType: 激励视频 1 ;全屏视频(模板) 2 ;draw信息流(模板) 3 ;激励视频(模板) 4 ;全屏视频 5 ;draw信息流  6
    */
    override init(_ json: JSON, responseId: String) {
        super.init(json, responseId: responseId)
        self.pul_json      = json
        self.xml_busiScene = .paidUnLock
        let jsonDictionary = json.dictionaryValue
        
        self.xml_posiName       = jsonDictionary["positionName"]?.stringValue ?? "sub_incentive"
        self.pul_unlockTimes    = max(0, jsonDictionary["unlockTimes"]?.intValue ?? 0)
        self.pul_videoCloseTime = max(0, jsonDictionary["videoCloseTime"]?.intValue ?? 0)
        self.pul_videoTime      = max(0, jsonDictionary["videoTime"]?.intValue ?? 0)
        self.pul_endFrameTime   = max(0, jsonDictionary["endFrameTime"]?.intValue ?? 0)
        
        let usr_dspDatas        = jsonDictionary["dspDatas"]?.arrayValue.map({ XMLPaidUnlockAdTypeDspData($0) }) ?? []
        // 广告素材相关
        usr_dspDatas.enumerated().forEach { (_, data) in
            if (data.dspAdType == 1 || data.dspAdType == 4), self.pul_dspDatas.count < 3,  // 首先保证为激励视频(当前存储上限为3条)
               let adType = XMLAdType(rawValue: data.dspId), adType.isValid, // SDK支持
               data.slotId.count > 0 { // 广告位有效
                if adType.isBU, data.dspAdType == 4 { // 调整模版dspId至ADX类型
                    data.dspId = 10026
                }
                self.pul_slotIds   .append(data.slotId)
                self.pul_dspIds    .append(data.dspId)
                self.pul_dspAdTypes.append(data.dspAdType)
                self.pul_dspDatas  .append(data)
            }
        }
    }
    
    // 当前素材是否有效
    public func pul_isValid() -> Bool {
        return !pul_dspAdTypes.isEmpty
    }
    
    public func switchNext() -> Bool {
        // 保证可以转换下一条
        guard pul_slotIds.isEmpty == false else { return false }
        // 上一次所使用的数据序号
        let lastIndex: Int = self.pul_curLoadIndex ?? -1
        // 保证下一条数据格式数据有效
        guard let slotId  = self.pul_slotIds[lastIndex + 1, true], slotId.isEmpty == false,
              let adTypeV = self.pul_dspIds[lastIndex + 1, true],
              let adType  = XMLAdType(rawValue: adTypeV), adType.isValid else { return false }
        // 广告基础数据绑定
        self.dspPositionId    = slotId
        self.adtype           = adTypeV
        // 更新序号
        self.pul_curLoadIndex = lastIndex + 1
        return true
    }
    
    public override func update(with extraInfo: [String: Any]? = nil) {
        super.update(with: extraInfo)
        self.pul_unlockAlbumId = (extraInfo?["adx_albumId"] as? UInt ) ?? 0
        self.pul_unlockTrackId = (extraInfo?["adx_trackId"] as? Int64) ?? 0
        self.pul_requestType   = (extraInfo?["adx_requestType"] as? Int) ?? 0
        self.pul_trackIds      = (extraInfo?["trackIds"] as? [Int64]) ?? []
    }
    
    public override func toV2UploaderParams(_ status: Int, useTime: TimeInterval) -> [String: Any] {
        var params: [String: Any] = super.toV2UploaderParams(status, useTime: useTime)
        if XMSettings.shared().isLoggedIn, let uid = XMSettings.shared().userModel.user?.uid {
            params["uid"] = uid
        } else {
            params["uid"] = -1
        }
        params["adNum"] = self.pul_curLoadIndex ?? 0
        params["unlockTimes"] = self.pul_unlockTimes
        params["positionName"] = self.xml_posiName
        params["sourceName"] = self.pul_sourceName
        params["albumId"] = self.pul_unlockAlbumId
        params["trackId"] = self.pul_unlockTrackId
        return params
    }
    
    public override func toStaticParams(_ toutiaoType: String, logType: String, positionName: String = "") -> [String: Any] {
        var params: [String: Any] = super.toStaticParams(toutiaoType, logType: logType, positionName: positionName)
        if XMSettings.shared().isLoggedIn, let uid = XMSettings.shared().userModel.user?.uid {
            params["uid"] = uid
        } else {
            params["uid"] = -1
        }
        params["adNum"] = self.pul_curLoadIndex ?? 0
        params["unlockTimes"] = self.pul_unlockTimes
        params["sdkType"] = self.xml_AdType().sdkType
        params["dspPositionId"] = self.dspPositionId
        params["albumId"] = self.pul_unlockAlbumId
        params["trackId"] = self.pul_unlockTrackId
        params["sourceName"] = self.pul_sourceName
        params["positionId"] = self.xml_positionId
        return params
    }
    
    public override func toClickParams(_ toutiaoType: String, logType: String, positionName: String) -> [String: Any] {
        var params: [String: Any] = super.toClickParams(toutiaoType, logType: logType, positionName: positionName)
        if XMSettings.shared().isLoggedIn, let uid = XMSettings.shared().userModel.user?.uid {
            params["uid"] = uid
        } else {
            params["uid"] = -1
        }
        params["adNum"] = self.pul_curLoadIndex ?? 0
        params["unlockTimes"] = self.pul_unlockTimes
        params["sdkType"] = self.xml_AdType().sdkType
        params["dspPositionId"] = self.dspPositionId
        params["albumId"] = self.pul_unlockAlbumId
        params["trackId"] = self.pul_unlockTrackId
        params["sourceName"] = self.pul_sourceName
        params["positionId"] = self.xml_positionId
        return params
    }
    
    public func toShowTimeParams(_ toutiaoType: String, playTime: TimeInterval, positionName: String = "") -> [String: Any] {
        var params: [String: Any] = super.toStaticParams(toutiaoType, logType: "showTime", positionName: positionName)
        if XMSettings.shared().isLoggedIn, let uid = XMSettings.shared().userModel.user?.uid {
            params["uid"] = uid
        } else {
            params["uid"] = -1
        }
        params["adNum"] = self.pul_curLoadIndex ?? 0
        params["unlockTimes"] = self.pul_unlockTimes
        params["sdkType"] = self.xml_AdType().sdkType
        params["dspPositionId"] = self.dspPositionId
        params["albumId"] = self.pul_unlockAlbumId
        params["trackId"] = self.pul_unlockTrackId
        params["showTimeMs"] = Int64(playTime * 1000)
        params["sourceName"] = self.pul_sourceName
        params["positionId"] = self.xml_positionId
        return params
    }
    
    public func copyOne() -> XMLPaidUnlockAdTypeItemModel {
        let item = XMLPaidUnlockAdTypeItemModel(self.pul_json, responseId: responseId)
        item.pul_unlockAlbumId = self.pul_unlockAlbumId
        item.pul_unlockTrackId = self.pul_unlockTrackId
        item.pul_curLoadIndex  = self.pul_curLoadIndex
        item.pul_videoTime     = self.pul_videoTime
        item.pul_sourceName    = self.pul_sourceName
        item.pul_trackIds      = self.pul_trackIds
        item.xml_index         = self.xml_index
        item.xml_renderSize    = self.xml_renderSize
        item.xml_busiScene     = self.xml_busiScene
        item.dspPositionId     = self.dspPositionId
        item.adtype            = self.adtype
        item.xml_posiName      = self.xml_posiName
        return item
    }
}

// 付费解锁广告请求参数
public class XMLPulAdRequestParams {
    class func reqParams(_ params: [String: Any]) -> [String : Any] {
        var param: [String: Any] = ["appid"    : appId,
                                    "device"   : "iphone",
                                    "network"  : XMConfig.getStatisticNetwork(),
                                    "operator" : XMConfig.getStatisticOperator(),
                                    "version"  : XMSYSTEM_BUNDLEVERSION,
                                    "uid"      : XMSettings.shared().isLoggedIn ? (XMSettings.shared().userModel.user?.uid ?? 0) : -1,
                                    "xt"       : Int64(Date().timeIntervalSince1970 * 1000)]
        params.enumerated().forEach { (_, item) in
            if item.key.contains("adx_"), let saveKey = item.key.components(separatedBy: "adx_").last, !saveKey.isEmpty {
                param[saveKey] = item.value
            }
        }
        return param
    }
    
    public class func adParams(_ albumId: UInt, trackId: Int64, adid: String, positionName: String, responseId: String, requestType: Int = 0, extra: [String: Any] = [:]) -> [String: Any] {
        var params: [String: Any] = ["adx_albumId"         : albumId,
                                     "adx_trackId"         : trackId,
                                     "adx_adid"            : adid,
                                     "adx_prevPositionName": positionName,
                                     "adx_prevResponseId"  : responseId,
                                     "adx_requestType"     : requestType,
                                     "positionName"        : positionName]
        extra.enumerated().forEach { (_, item) in
            params[item.key] = item.value
        }
        return params
    }
}
