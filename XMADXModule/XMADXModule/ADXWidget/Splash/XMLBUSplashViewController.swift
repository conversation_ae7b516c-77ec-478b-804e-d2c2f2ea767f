//
//  XMLBUSplashViewController.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/12.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import UIKit
import SnapKit
import BUAdSDK
import RouterModule
import BaseModule
import XMUtilModule
import XMConfigModule
import StoreKit

class XMLBUSplashViewController: UIViewController {
    
    public override var prefersStatusBarHidden: Bool { return true }

    class func controller(with item: XMLADXLoaderItem?, isAsRootVC: Bool = true) -> XMLBUSplashViewController {
        let controller = XMLBUSplashViewController()
        controller.item = item
        controller.isAsRootVC = isAsRootVC
        return controller
    }
    
    // 当前广告后台模型
    public var item: XMLADXLoaderItem? = nil {
        didSet {
            splashAdView = item?.s_buAd
            splashAdView?.delegate = self
        }
    }
        
    private var splashAdView: BUSplashAd? = nil
    
    private var isAsRootVC: Bool = false
    private var isNeedRecoveryNP: Bool = false
    private weak var lastWindow: UIWindow? = nil

    lazy var bottomImageView: UIImageView = {
        let imageView: UIImageView = UIImageView(image: UIImage(named: "launchLogo1"))
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    lazy var bottomView: UIView = {
        let container: UIView = UIView()
        return container
    }()
    
    lazy var maskWidget: XMLSplashJumpMaskWidget = {
        let container: XMLSplashJumpMaskWidget = XMLSplashJumpMaskWidget()
        return container
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        XMLADXUtils.consoleLog("🎉 穿山甲开屏出生啦 🎉")
        setupBaseUI()
        self.lastWindow = UIApplication.shared.keyWindow
    }
    
    func setupBaseUI() {
        self.view.backgroundColor = .white
        if let `splashAdView` = splashAdView {
//            self.view.addSubview(splashAdView)
            splashAdView.showSplashView(inRootViewController: self)
        }
        
        self.view.addSubview(bottomView)
        self.bottomView.snp.makeConstraints { (make) in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(UIScreen.main.bounds.height/5)
        }
        
        let imageSize: CGSize = UIImage(named: "launchLogo1")?.size ?? .zero
        self.bottomView.addSubview(bottomImageView)
        self.bottomImageView.snp.makeConstraints { (make) in
            make.centerX.centerY.equalToSuperview()
            make.size.equalTo(self.bottomLogoSize(imageSize.height == 0 ? 1 : imageSize.width/imageSize.height))
        }
        
        let closeBtnFrame: CGRect = CGRect(x: UIScreen.main.bounds.width - 70 - 8, y: 28.0 + (UIScreen.isPhoneXSeries ? 24.0 : 0.0), width: 70, height: 28)
        self.view.addSubview(maskWidget)
        maskWidget.show(36, containerBounds: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height/5.0*4.0), othersZones: [closeBtnFrame])
        maskWidget.isHidden = self.item?.item?.jumpModeType == 0
        maskWidget.isUserInteractionEnabled = !self.maskWidget.isHidden && self.item?.item?.clickableAreaType != 1
        
        self.view.addSubview(self.skipButton)
        self.skipButton.snp.makeConstraints { (make) in
            make.top.equalToSuperview().offset(28.0 + (UIScreen.isPhoneXSeries ? 24.0 : 0.0))
            make.right.equalToSuperview().offset(-8)
            make.size.equalTo(CGSize(width: 70, height: 28))
        }
        if let showTime = item?.item?.loadingShowTime, showTime.count > 0, let time = Int64(showTime) {
            self.setupTimerAndStart(max(time / 1000, 5))
        } else {
            self.setupTimerAndStart(5)
        }
        let delaySkip : Bool = XMLADXConfig.enableLoadingDelaySkip()
        XMLAPMLogger.info("skipButton add, delaySkip \(delaySkip)", moduler: .adx)
        // 是否开启开屏跳过延迟逻辑
        if delaySkip {
            self.skipButton.isHidden = true
            XMLAPMLogger.info("skipButton hidden", moduler: .adx)
            delay(withTimeInterval: 1) {
                [weak self] in self?.skipButton.isHidden = false
                XMLAPMLogger.info("skipButton show", moduler: .adx)
            }
        }
        
        _ = NotificationCenter.default.rx.notification(UIApplication.willResignActiveNotification).take(until: self.rx.deallocated).subscribe(onNext: { [weak self] _ in
            guard let wself = self, wself.isAsRootVC, wself.isClickedToAdPage else { return }
            RouterBridge(nil).setTabControllerWillShowAdDetailVC(true)
            if UIApplication.shared.keyWindow === wself.lastWindow {
                wself.view.removeFromSuperview()
            } else {
                wself.lastWindow?.makeKeyAndVisible()
            }
            UIApplication.shared.keyWindow?.rootViewController = (RouterBridge(nil).xmAppSceneControlElement(.tabBar) as? UIViewController) ?? UIViewController()
        })
        if RouterBridge(nil).isNPWindowShowing(), isAsRootVC {
            isNeedRecoveryNP = true
            RouterBridge(nil).removeNPWindow(false)
        }
    }
    
    fileprivate var isClickedToAdPage: Bool = false
    fileprivate var isClickedToSkip  : Bool = false

    func bottomLogoSize(_ imageScale: CGFloat = 1.0) -> CGSize {
        let scale: CGFloat = imageScale == 0 ? 1 : imageScale
        return CGSize(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.width/scale)
    }
    
    private var skipTimer      : XMSTimer? = nil
    private var timerCount     : Int64     = 5
    private lazy var skipButton: UIButton  = {
        let button = UIButton(type: .custom)
        button.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        button.setTitleColor(UIColor.white, for: .normal)
        button.titleLabel?.font = kPingFangFont(14)
        button.setTitle("跳过 5s", for: .normal)
        button.adjustsImageWhenHighlighted = false
        button.layer.cornerRadius = 14
        button.clipsToBounds = true
        button.addTarget(self, action: #selector(skipButtonClick(_:)), for: .touchUpInside)
        return button
    }()
    
    deinit { XMLADXUtils.consoleLog("🎉 穿山甲开屏释放啦 🎉") }
}

extension XMLBUSplashViewController: BUSplashAdDelegate {
    func splashAdDidClick(_ splashAd: BUSplashAd) {
        isClickedToAdPage = true
        if !isAsRootVC {
            self.splashAdView?.removeSplashView()
            self.view.removeFromSuperview()
            if UIApplication.shared.keyWindow === self.lastWindow { } else { self.lastWindow?.makeKeyAndVisible() }
        }
        RouterBridge(nil).removeNPWindow(false)
        RouterBridge(nil).hiddenGobalTask()
        
        XMLADXReportManager.sendClickReportInfo(self.item?.item, positionName: "loading", vender: 2)
    }
    
    func splashAdWillShow(_ splashAd: BUSplashAd) {
        XMLAPMLogger.info("BUSplash willVisible", moduler: .adx)
        self.view.bringSubviewToFront(self.skipButton)
        if RouterBridge(nil).isNPWindowShowing(), isAsRootVC {
            isNeedRecoveryNP = true
            RouterBridge(nil).removeNPWindow(false)
        }
        
        self.item?.uploadVisibleStatisticInfo()
        XMLADXReportManager.sendShowReportInfo(self.item?.item, positionName: "loading", vender: 2)
    }
    
    func p_splashAdWillClose(_ splashAd: BUSplashAd) {
        guard isClickedToAdPage == false else { return }
        guard isClickedToSkip == false else { return }
        self.skipTimer?.cancel()
        self.skipTimer = nil
        self.splashAdView?.removeSplashView()
        if isAsRootVC {
            if isNeedRecoveryNP {
                isNeedRecoveryNP = false
                RouterBridge(nil).showOnMainTab(false)
            }
            XMLADXReportManager.sendCoolSplashShowToAPM()
            if UIApplication.shared.keyWindow === self.lastWindow { self.view.removeFromSuperview() } else { self.lastWindow?.makeKeyAndVisible() }
            UIApplication.shared.keyWindow?.rootViewController = (RouterBridge(nil).xmAppSceneControlElement(.tabBar) as? UIViewController) ?? UIViewController()
            self.view.removeFromSuperview()
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "kXMAdDimssAdPresentNotificationName"), object: nil)
        } else {
            XMLADXWidgetManager.recoverBlackWidgetsIfNeed()
            self.view.removeFromSuperview()
            if UIApplication.shared.keyWindow === self.lastWindow { } else { self.lastWindow?.makeKeyAndVisible() }
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "applicationWillEnterForeground"), object: nil)
        }
    }
    
    func splashAdDidClose(_ splashAd: BUSplashAd, closeType: BUSplashAdCloseType) {
        self.p_splashAdWillClose(splashAd)
        
        if !isClickedToAdPage {
            splashAdView?.removeSplashView()
        }
        XMLADXManager.shared().cleanSplashContent()
    }
    
    func splashDidCloseOtherController(_ splashAd: BUSplashAd, interactionType: BUInteractionType) {
        self.skipTimer?.cancel()
        self.skipTimer = nil
        if isAsRootVC {
            if isNeedRecoveryNP {
                isNeedRecoveryNP = false
                RouterBridge(nil).showOnMainTab(false)
            }
            RouterBridge(nil).setTabControllerWillShowAdDetailVC(true)
            if UIApplication.shared.keyWindow === self.lastWindow {
                self.view.removeFromSuperview()
            } else {
                self.lastWindow?.makeKeyAndVisible()
            }
            UIApplication.shared.keyWindow?.rootViewController = (RouterBridge(nil).xmAppSceneControlElement(.tabBar) as? UIViewController) ?? UIViewController()
            XMLADXReportManager.sendCoolSplashShowToAPM()
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "kXMAdDimssAdPresentNotificationName"), object: nil)
        } else {
            XMLADXWidgetManager.recoverBlackWidgetsIfNeed()
            RouterBridge(nil).showOnMainTab(false)
            if let topVC = UIApplication.topViewController() { RouterBridge(nil).showGobalTask(animation: false, in: topVC) }
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "applicationWillEnterForeground"), object: nil)
        }
        XMLADXManager.shared().cleanSplashContent()
    }
    
    func splashAdLoadSuccess(_ splashAd: BUSplashAd) { }
    
    func splashAdLoadFail(_ splashAd: BUSplashAd, error: BUAdError?) { }
    
    func splashAdRenderSuccess(_ splashAd: BUSplashAd) { }
    
    func splashAdRenderFail(_ splashAd: BUSplashAd, error: BUAdError?) { }
    
    func splashAdDidShow(_ splashAd: BUSplashAd) { }
    
    func splashAdViewControllerDidClose(_ splashAd: BUSplashAd) {  }
    
    func splashVideoAdDidPlayFinish(_ splashAd: BUSplashAd, didFailWithError error: (any Error)?) { }
}

extension XMLBUSplashViewController {
    public func setupTimerAndStart(_ skipInterval: Int64? = nil) {
        self.timerCount = skipInterval ?? 5
        self.skipButton.setTitle("跳过 \(self.timerCount)s", for: .normal)
        self.skipTimer = XMSTimer(interval: .seconds(1), repeats: true, handler: { [weak self] (_) in
            guard let wself = self else { return }
            wself.timerCount -= 1
            wself.skipButton.setTitle("跳过 \(wself.timerCount)s", for: .normal)
            if wself.timerCount <= 0 {
                wself.skipButton.setTitle("跳过", for: .normal)
                wself.skipTimer?.cancel()
                XMLAPMLogger.info("skipButton timeout", moduler: .adx)
                wself.skipButtonClick(wself.skipButton)
            }
        })
        self.skipTimer?.resume()
    }
    
    @objc fileprivate func skipButtonClick(_ sender: UIButton) {
        XMLAPMLogger.info("skipButton click", moduler: .adx)
        guard !isClickedToSkip || (self.skipButton.title(for: .normal) ?? "") == "跳过" else { return }
        self.isClickedToSkip = true
        guard !isClickedToAdPage || (self.skipButton.title(for: .normal) ?? "") == "跳过" else { return }
        self.splashAdView?.removeSplashView()
        XMLAPMLogger.info("BUSplash removed", moduler: .adx)
        self.skipTimer?.cancel()
        self.skipTimer = nil
        if isAsRootVC {
            if isNeedRecoveryNP {
                isNeedRecoveryNP = false
                RouterBridge(nil).showOnMainTab(false)
            }
            XMLADXReportManager.sendCoolSplashShowToAPM()
            self.lastWindow?.makeKeyAndVisible()
            UIApplication.shared.keyWindow?.rootViewController = (RouterBridge(nil).xmAppSceneControlElement(.tabBar) as? UIViewController) ?? UIViewController()
            self.view.removeFromSuperview()
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "kXMAdDimssAdPresentNotificationName"), object: nil)
        } else {
            XMLADXWidgetManager.recoverBlackWidgetsIfNeed()
            self.view.removeFromSuperview()
            if UIApplication.shared.keyWindow === self.lastWindow { } else { self.lastWindow?.makeKeyAndVisible() }
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "applicationWillEnterForeground"), object: nil)
        }
        XMLADXManager.shared().cleanSplashContent()
    }
}
