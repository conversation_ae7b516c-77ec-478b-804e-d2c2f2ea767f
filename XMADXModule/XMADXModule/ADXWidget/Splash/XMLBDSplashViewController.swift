//
//  XMLBDSplashViewController.swift
//  XMADXModule
//
//  Created by yangle<PERSON> on 2020/6/9.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import UIKit
import SnapKit
import BaiduMobAdSDK
import RouterModule
import XMUtilModule
import BaseModule

class XMLBDSplashViewController: UIViewController {
    
    public override var prefersStatusBarHidden: Bool { return true }

    class func controller(with item: XMLADXLoaderItem?, isAsRootVC: Bool = true) -> XMLBDSplashViewController {
        let controller = XMLBDSplashViewController()
        controller.item = item
        controller.isAsRootVC = isAsRootVC
        return controller
    }
    
    // 当前广告后台模型
    public var item: XMLADXLoaderItem? = nil {
        didSet {
            splashAdView = item?.s_bdAd
            splashAdView?.delegate = self
            splashAdView?.presentAdViewController = self
        }
    }
    
    private var splashAdView: BaiduMobAdSplash? = nil
    
    private var isAsRootVC: Bool = false
    private var isNeedRecoveryNP: Bool = false

    private weak var lastWindow: UIWindow? = nil
    
    lazy var bottomImageView: UIImageView = {
        let imageView: UIImageView = UIImageView(image: UIImage(named: "launchLogo1"))
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    lazy var bottomView: UIView = {
        let container: UIView = UIView()
        return container
    }()
    
    lazy var maskWidget: XMLSplashJumpMaskWidget = {
        let container: XMLSplashJumpMaskWidget = XMLSplashJumpMaskWidget()
        return container
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        XMLADXUtils.consoleLog("🎉 百度开屏出生啦 🎉")
        setupBaseUI()
        self.lastWindow = UIApplication.shared.keyWindow
    }
    
    func setupBaseUI() {
        self.view.backgroundColor = .white
        if let splashAdBox = item?.s_reqBdAdBox {
            self.view.addSubview(splashAdBox)
        }
        
        self.view.addSubview(bottomView)
        self.bottomView.snp.makeConstraints { (make) in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(UIScreen.main.bounds.height/5)
        }
        
        let imageSize: CGSize = UIImage(named: "launchLogo1")?.size ?? .zero
        self.bottomView.addSubview(bottomImageView)
        self.bottomImageView.snp.makeConstraints { (make) in
            make.centerX.centerY.equalToSuperview()
            make.size.equalTo(self.bottomLogoSize(imageSize.height == 0 ? 1 : imageSize.width/imageSize.height))
        }
    }
    
    fileprivate var isClickedToAdPage: Bool = false
    
    func bottomLogoSize(_ imageScale: CGFloat = 1.0) -> CGSize {
        let scale: CGFloat = imageScale == 0 ? 1 : imageScale
        return CGSize(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.width/scale)
    }
    
    deinit {
        XMLADXUtils.consoleLog("🎉 百度开屏释放啦 🎉")
    }
}

extension XMLBDSplashViewController: BaiduMobAdSplashDelegate {
    func publisherId() -> String! { kBDAdSDKAppId }
    
    func splashAdLoadSuccess(_ splash: BaiduMobAdSplash!) { }
    
    func splashAdLoadFailCode(_ errCode: String!, message: String!, splashAd: BaiduMobAdSplash!) {
        splashViewCloseHandle()
    }
    
    func splashSuccessPresentScreen(_ splash: BaiduMobAdSplash!) {
        if RouterBridge(nil).isNPWindowShowing(), isAsRootVC {
            isNeedRecoveryNP = true
            RouterBridge(nil).removeNPWindow(false)
        }
        let closeBtnFrame: CGRect = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: UIScreen.naviBarHeight)
        self.view.addSubview(maskWidget)
        maskWidget.show(36, containerBounds: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height/5.0*4.0), othersZones: [closeBtnFrame])
        maskWidget.isHidden = self.item?.item?.jumpModeType == 0
        maskWidget.isUserInteractionEnabled = !self.maskWidget.isHidden && self.item?.item?.clickableAreaType != 1
        self.item?.uploadVisibleStatisticInfo()
        XMLADXReportManager.sendShowReportInfo(self.item?.item, positionName: "loading", vender: XMLAdType.bd.sdkType)
    }
    
    func splashlFailPresentScreen(_ splash: BaiduMobAdSplash!, withError reason: BaiduMobFailReason) { }
    
    func splashDidClicked(_ splash: BaiduMobAdSplash!) {
        isClickedToAdPage = true
        XMLADXReportManager.sendClickReportInfo(self.item?.item, positionName: "loading", vender: XMLAdType.bd.sdkType)
    }
    
    func splashDidDismissLp(_ splash: BaiduMobAdSplash!) {
        guard isClickedToAdPage else { return }
        splashViewCloseHandle()
    }
    
    func splashDidDismissScreen(_ splash: BaiduMobAdSplash!) {
//        guard isClickedToAdPage == false else { return }
        splashViewCloseHandle()
    }
    
    func splashDidReady(_ splash: BaiduMobAdSplash!, andAdType adType: String!, videoDuration: Int) { }
}

// MARK: - 辅助逻辑Method
extension XMLBDSplashViewController {
    func splashViewCloseHandle() {
        if isAsRootVC {
            if isNeedRecoveryNP {
                isNeedRecoveryNP = false
                RouterBridge(nil).showOnMainTab(false)
            }
            XMLADXReportManager.sendCoolSplashShowToAPM()
            if UIApplication.shared.keyWindow === self.lastWindow { } else { self.lastWindow?.makeKeyAndVisible() }
            UIApplication.shared.keyWindow?.rootViewController = (RouterBridge(nil).xmAppSceneControlElement(.tabBar) as? UIViewController) ?? UIViewController()
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "kXMAdDimssAdPresentNotificationName"), object: nil)
        } else {
            XMLADXWidgetManager.recoverBlackWidgetsIfNeed()
            self.view.removeFromSuperview()
            if UIApplication.shared.keyWindow === self.lastWindow { } else { self.lastWindow?.makeKeyAndVisible() }
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "applicationWillEnterForeground"), object: nil)
        }
        XMLADXManager.shared().cleanSplashContent()
    }
}
