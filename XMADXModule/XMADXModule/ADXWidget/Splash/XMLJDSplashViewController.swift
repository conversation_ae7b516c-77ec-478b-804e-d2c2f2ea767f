//
//  XMLJDSplashViewController.swift
//  XMADXModule
//
//  Created by  周玉杰 on 2021/12/15.
//  Copyright © 2021 ximalaya. All rights reserved.
//

import UIKit
import JADYun
import XMUtilModule
import BaseModule
import RouterModule

class XMLJDSplashViewController: UIViewController {
    
    public override var prefersStatusBarHidden: Bool { return true }

    class func controller(with item: XMLADXLoaderItem?, isAsRootVC: Bool = true) -> XMLJDSplashViewController {
        let controller = XMLJDSplashViewController()
        controller.item = item
        controller.isAsRootVC = isAsRootVC
        return controller
    }
    
    public var item: XMLADXLoaderItem? = nil {
        didSet {
            splashAdView = item?.s_jdAd
            splashAdView?.delegate = self
        }
    }
    
    private var splashAdView: JADSplashView? = nil
    private var isAsRootVC: Bool = false
    private weak var lastWindow: UIWindow? = nil
    fileprivate var isClickedToAdPage: Bool = false
    private var isNeedRecoveryNP: Bool = false

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        XMLADXUtils.consoleLog("🎉 京东开屏出生啦 🎉")
        setupShowNP(false)
        setupBaseUI()
        willResignActiveNotify()
        self.lastWindow = UIApplication.shared.keyWindow
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
    }
    
    func setupBaseUI() {
        self.view.backgroundColor = .white
        
        if let `splashAdView` = splashAdView {
            self.view.addSubview(splashAdView)
            splashAdView.snp.makeConstraints { (make) in
                make.left.top.right.equalToSuperview()
                make.height.equalTo(UIScreen.main.bounds.height * 4/5.0)
            }
        }
        
        self.view.addSubview(bottomView)
        self.bottomView.snp.makeConstraints { (make) in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(UIScreen.main.bounds.height * 1/5.0)
        }
        
        let imageSize: CGSize = UIImage(named: "launchLogo1")?.size ?? .zero
        self.bottomView.addSubview(bottomImageView)
        self.bottomImageView.snp.makeConstraints { (make) in
            make.centerX.centerY.equalToSuperview()
            make.size.equalTo(self.bottomLogoSize(imageSize.height == 0 ? 1 : imageSize.width/imageSize.height))
        }
    }
    
    func bottomLogoSize(_ imageScale: CGFloat = 1.0) -> CGSize {
        let scale: CGFloat = imageScale == 0 ? 1 : imageScale
        return CGSize(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.width/scale)
    }
    
    lazy var bottomView: UIView = {
        let container: UIView = UIView()
        return container
    }()
    
    lazy var bottomImageView: UIImageView = {
        let imageView: UIImageView = UIImageView(image: UIImage(named: "launchLogo1"))
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    lazy var maskWidget: XMLSplashJumpMaskWidget = {
        let container: XMLSplashJumpMaskWidget = XMLSplashJumpMaskWidget()
        return container
    }()
    
    deinit { XMLADXUtils.consoleLog("🎉 京东开屏释放啦 🎉") }
}

extension XMLJDSplashViewController: JADSplashViewDelegate {
    // 加载成功
    func jadSplashViewDidLoadSuccess(_ splashView: JADSplashView) { }
    // 加载失败
    func jadSplashViewDidLoadFailure(_ splashView: JADSplashView, error: (any Error)?) { }
    // 渲染成功
    func jadSplashViewDidRenderSuccess(_ splashView: JADSplashView) {
        let closeBtnFrame = getSkipWidgetFrame(splashView)
        self.view.addSubview(maskWidget)
        maskWidget.show(36, containerBounds: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height * 4 / 5.0), othersZones: [closeBtnFrame])
        maskWidget.isHidden = self.item?.item?.jumpModeType == 0
        maskWidget.isUserInteractionEnabled = !self.maskWidget.isHidden && self.item?.item?.clickableAreaType != 1
        
    }
    // 渲染失败
    func jadSplashViewDidRenderFailure(_ splashView: JADSplashView, error: (any Error)?) { }
    
    func jadSplashViewDidExposure(_ splashView: JADSplashView) {
        self.item?.uploadVisibleStatisticInfo()
        XMLADXReportManager.sendShowReportInfo(self.item?.item, positionName: "loading", vender: 2)
    }
    // 点击
    func jadSplashViewDidClick(_ splashView: JADSplashView) {
        isClickedToAdPage = true
        RouterBridge(nil).hiddenGobalTask()
        XMLADXReportManager.sendClickReportInfo(self.item?.item, positionName: "loading", vender: 2)
    }
    // 关闭回调，当点击跳过按钮或者用户点击广告时会直接触发此回调，建议在此回调方法中直接进行广告对象的移除动作，并将广告对象置为nil
    func jadSplashViewDidClose(_ splashView: JADSplashView) {
        if isAsRootVC && isClickedToAdPage {
            // 走进入通知关闭
        } else {
            closeAd()
        }
        
        
    }
    func jadSplashViewDidCloseOtherController(_ splashView: JADSplashView, interactionType: JADInteractionType) {
        closeAd()
    }
    
    func jadSplashView(_ splashView: JADSplashView, countDown: Int32) {
        
    }
    
}

extension XMLJDSplashViewController {
    fileprivate func setupShowNP(_ isShow: Bool) {
        if isShow {
            if isNeedRecoveryNP {
                isNeedRecoveryNP = false
                RouterBridge(nil).showOnMainTab(false)
            }
        } else {
            if RouterBridge(nil).isNPWindowShowing(), isAsRootVC {
                isNeedRecoveryNP = true
                RouterBridge(nil).removeNPWindow(false)
            }
        }
    }
    fileprivate func closeAd() {
        splashAdView?.removeFromSuperview()
        item = nil
        if isAsRootVC {
            setupShowNP(true)
            XMLADXReportManager.sendCoolSplashShowToAPM()
            self.lastWindow?.makeKeyAndVisible()
            UIApplication.shared.keyWindow?.rootViewController = (RouterBridge(nil).xmAppSceneControlElement(.tabBar) as? UIViewController) ?? UIViewController()
            self.view.removeFromSuperview()
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "kXMAdDimssAdPresentNotificationName"), object: nil)
        } else {
            XMLADXWidgetManager.recoverBlackWidgetsIfNeed()
            self.view.removeFromSuperview()
            if UIApplication.shared.keyWindow === self.lastWindow { } else { self.lastWindow?.makeKeyAndVisible() }
            XMLADXManager.shared().isInSplashADLogic = false
            NotificationCenter.default.post(name: NSNotification.Name(rawValue: "applicationWillEnterForeground"), object: nil)
        }
        XMLADXManager.shared().cleanSplashContent()
    }
    fileprivate func willResignActiveNotify() {
        _ = NotificationCenter.default.rx.notification(UIApplication.willResignActiveNotification).take(until: self.rx.deallocated).subscribe(onNext: { [weak self] _ in
            guard let wself = self, wself.isAsRootVC, wself.isClickedToAdPage else { return }
            wself.closeAd()
        })
    }

    fileprivate func getSkipWidgetFrame(_ splashView: JADSplashView) -> CGRect {
        let jdAdWidget = JADNativeAdWidget()
        var closeBtnFrame = jdAdWidget.skipWidget?.frame ?? CGRect()
        if closeBtnFrame.origin == CGPoint.zero {
            for subV in splashView.subviews {
                if subV.isKind(of: UIControl.classForCoder()) {
                    closeBtnFrame = subV.frame
                }
            }
        }
        return closeBtnFrame
    }
}
