//
//  XMLJDNativeAdTrigger.swift
//  XMADXModule
//
//  Created by  周玉杰 on 2021/12/16.
//  Copyright © 2021 ximalaya. All rights reserved.
//

import UIKit
import JADYun

class XMLJDNativeAdTrigger: XMLNativeAdTrigger {
    
    public class func trigger(with item: XMLADXLoaderItem?) -> XMLJDNativeAdTrigger {
        let trigger = XMLJDNativeAdTrigger()
        trigger.item = item
        return trigger
    }
    
    // 当前广告后台模型
    public weak var item: XMLADXLoaderItem? = nil {
        didSet {
            nativeAd = item?.f_jdAd
            nativeAd?.delegate = self
        }
    }
    
    private var nativeAd: JADNativeAd? = nil
}

// MARK: - 京东信息流(非模版)广告代理 JADNativeAdDelegate Method
extension XMLJDNativeAdTrigger: JADNativeAdDelegate {
    func jadNativeAdDidLoadSuccess(_ nativeAd: JADNativeAd) {
        
    }
    
    func jadNativeAdDidLoadFailure(_ nativeAd: JADNativeAd, error: (any Error)?) {
        
    }
    
    // 曝光
    func jadNativeAdDidExposure(_ nativeAd: JADNativeAd) {
        nativeAd.rootViewController = XMLADXManager.viewController
        jdNativeAdVisibleHandle()
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidBecomeVisible() }
    }
    // 点击
    func jadNativeAdDidClick(_ nativeAd: JADNativeAd, with view: UIView?) {
        jdNativeAdClickHandle()
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidClick() }
    }
    // 关闭
    func jadNativeAdDidClose(_ nativeAd: JADNativeAd, with view: UIView?) {
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidCloseOtherController() }
    }
    
    func jadNativeAdDidCloseOtherController(_ nativeAd: JADNativeAd, interactionType: JADInteractionType) {
        
    }
}

extension XMLJDNativeAdTrigger {
    
    func jdNativeAdVisibleHandle() {
        guard !isTingShowReported else { return }
        isTingShowReported = true
        XMLADXReportManager.sendADMaterial(self.item, scene: .exposure)
        XMLADXReportManager.sendShowReportInfo(self.item?.item, positionName: item?.positionName ?? "", vender: XMLAdType.bd.sdkType)
    }
    
    func jdNativeAdClickHandle() {
        XMLADXReportManager.sendADMaterial(self.item, scene: .click)
        XMLADXReportManager.sendClickReportInfo(self.item?.item, positionName: item?.positionName ?? "", vender: XMLAdType.bd.sdkType)
    }
    
}
