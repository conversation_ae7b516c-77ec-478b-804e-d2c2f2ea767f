//
//  XMLBUNativeAdTrigger.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/13.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import RouterModule
import BUAdSDK
import class MediaModule.XMMediaPlayer

class XMLBUNativeAdTrigger: XMLNativeAdTrigger {
    
    public class func trigger(with item: XMLADXLoaderItem?) -> XMLBUNativeAdTrigger {
        let trigger = XMLBUNativeAdTrigger()
        trigger.item = item
        return trigger
    }
    
    // 当前广告后台模型
    public weak var item: XMLADXLoaderItem? = nil {
        didSet {
            adxItem = item?.item
            item?.f_buAd?.delegate = self
            item?.f_reqBuNEAd?.delegate = self
            nativeAd = item?.f_buAd
            nativeAdView.videoAdView?.delegate = self
        }
    }
    
    private var nativeAd        : BUNativeAd?                 = nil {
        didSet { if let ad = nativeAd { self.nativeAdView.refreshData(ad) } }
    }
    public  var nativeAdView    : BUNativeAdRelatedView       = BUNativeAdRelatedView()
    
    public override func nativeAdVideoDetailDidCloseHandleIfNeed(_ isForce: Bool) {
        guard isFeedVJumped || isForce else { return }
//        buNativeAdVideoDetailDidCloseHandle(isForce)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { // 延时处理，有得广告SDK AudioSession配置 放在此消失后面，故延时
            XMLADXManager.shared().recoverAudioControlIfNeed()
            if #available(iOS 12.0, *) {
                RouterBridge(nil).playerResume()
            } else if #available(iOS 11.0, *) {
                XMMediaPlayer.shared().play()
                XMMediaPlayer.shared().play()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    XMMediaPlayer.shared().play()
                }
            } else {
                RouterBridge(nil).playerResume()
            }
        }
    }
}

// MARK: - 穿山甲信息流(非模版)广告代理 BUNativeAdDelegate Method
extension XMLBUNativeAdTrigger: BUNativeAdDelegate {
    func nativeAdDidBecomeVisible(_ nativeAd: BUNativeAd) {
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidBecomeVisible() }
        buNativeAdVisibleHandle()
    }
    
    func nativeAdDidCloseOtherController(_ nativeAd: BUNativeAd, interactionType: BUInteractionType) {
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidCloseOtherController() }
    }
    
    func nativeAdDidClick(_ nativeAd: BUNativeAd, with view: UIView?) {
        buNativeAdClickHandle()
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidClick() }
    }
}

// MARK: - 穿山甲信息流(模版)广告代理 BUNativeExpressAdViewDelegate Method
extension XMLBUNativeAdTrigger: BUNativeExpressAdViewDelegate {
    func nativeExpressAdViewWillShow(_ nativeExpressAdView: BUNativeExpressAdView) {
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidBecomeVisible() }
        buNativeAdVisibleHandle()
    }
    
    func nativeExpressAdViewDidClick(_ nativeExpressAdView: BUNativeExpressAdView) {
        buNativeAdClickHandle()
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidClick() }
    }

    func nativeExpressAdViewPlayerDidPlayFinish(_ nativeExpressAdView: BUNativeExpressAdView, error: Error?) { }
    
    func nativeExpressAdView(_ nativeExpressAdView: BUNativeExpressAdView, dislikeWithReason filterWords: [BUDislikeWords]) { }
    
    func nativeExpressAdViewWillPresentScreen(_ nativeExpressAdView: BUNativeExpressAdView) { }
    
    func nativeExpressAdViewDidCloseOtherController(_ nativeExpressAdView: BUNativeExpressAdView, interactionType: BUInteractionType) {
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidCloseOtherController() }
    }
}

// MARK: - 穿山甲信息流(Feed Video)广告代理 BUVideoAdViewDelegate Method
extension XMLBUNativeAdTrigger: BUVideoAdViewDelegate {
    func videoAdView(_ videoAdView: BUVideoAdView, didLoadFailWithError error: Error?) {
        if let obj = self.item { // 视频加载失败
            RouterBridge(nil).uploadAdFailureInfo(adObj: obj, status: 1001)
        }
    }
    
    func videoAdView(_ videoAdView: BUVideoAdView, stateDidChanged playerState: BUPlayerPlayState) {
        var statusCode: Int              = 0
        if playerState == .statePlaying { // 开始播放时间
            self.adxItem?.xml_playTime   = Date().timeIntervalSince1970
            self.adxItem?.xml_playStatus = .playing
        } else if !isFeedVJumped, playerState == .stateFailed || playerState == .stateStopped || playerState == .statePause {
            statusCode                   = 1
            guard (self.adxItem?.xml_manualSkip ?? false) || playerState == .stateFailed || playerState == .stateStopped else { return }
            statusCode                   = 2
            self.adxItem?.xml_endTime    = Date().timeIntervalSince1970
            self.adxItem?.xml_playStatus = .stopped
            let playTime : CGFloat = videoAdView.currentPlayTime()
            let totalTime: CGFloat = CGFloat(videoAdView.materialMeta?.videoDuration ?? 0)
            XMLADXReportManager.sendShowObReportInfo(self.adxItem, isFinish: (totalTime >= 1) && (playTime >= totalTime || abs(playTime - totalTime) < 0.1))
        }
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdViewStatusChanged?(statusCode) }
        if let obj = self.item, isFeedVJumped == false, playerState == .statePlaying { // 视频加载成功
            RouterBridge(nil).uploadVisibleStatisticInfo(adObj: obj)
        }
    }
    
    func playerDidPlayFinish(_ videoAdView: BUVideoAdView) { }

    // 视频贴片播放时点击回调
    func videoAdViewDidClick(_ videoAdView: BUVideoAdView) {
        XMLADXManager.shared().isInVideoDetailPage = true
        self.isFeedVJumped = true
        buNativeAdClickHandle()
        buNativeAdVideoDetailWillShowHandle()
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidClick() }
    }
    
    // 视频贴片结束时点击回调
    func videoAdViewFinishViewDidClick(_ videoAdView: BUVideoAdView) {
        XMLADXManager.shared().isInVideoDetailPage = true
        self.isFeedVJumped = true
        buNativeAdClickHandle()
        buNativeAdVideoDetailWillShowHandle()
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidClick() }
    }
    
    func videoAdViewDidCloseOtherController(_ videoAdView: BUVideoAdView, interactionType: BUInteractionType) {
        XMLADXManager.shared().isInVideoDetailPage = false
        self.isFeedVJumped = false
        buNativeAdVideoDetailDidCloseHandle()
        if flowView?.isHandleExtraCallBack() ?? false { flowView?.nativeAdDidCloseOtherController() }
    }
}

// MARK: - 辅助 Method
extension XMLBUNativeAdTrigger {
    func buNativeAdVisibleHandle() {
        guard !isTingShowReported else { return }
        isTingShowReported = true
        XMLADXReportManager.sendADMaterial(self.item, scene: .exposure)
        XMLADXReportManager.sendShowReportInfo(self.item?.item, positionName: item?.positionName ?? "", vender: 2)
    }
    
    func buNativeAdClickHandle() {
        XMLADXReportManager.sendADMaterial(self.item, scene: .click)
        XMLADXReportManager.sendClickReportInfo(self.item?.item, positionName: item?.positionName ?? "", vender: 2)
    }
    
    func buNativeAdVideoDetailWillShowHandle() {
        if XMLADXConfig.isPauseAudioWhenADSound() && RouterBridge(nil).playerIsPlaying() {
            if #available(iOS 12.0, *) {
                RouterBridge(nil).playerPause()
            } else if #available(iOS 11.0, *) {
                self.currPlayInterval = XMMediaPlayer.shared().currentTime ?? 0
                RouterBridge(nil).playerStop()
            } else {
                RouterBridge(nil).playerPause()
            }
            self.isPauseAudio = true
        }
    }
    
    func buNativeAdVideoDetailDidCloseHandle(_ isForce: Bool = false) {
        if isForce || (XMLADXConfig.isPauseAudioWhenADSound() && self.isPauseAudio) {
            self.isPauseAudio = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { // 延时处理，有得广告SDK AudioSession配置 放在此消失后面，故延时
                XMLADXManager.shared().recoverAudioControlIfNeed()
                if #available(iOS 12.0, *) {
                    RouterBridge(nil).playerResume()
                } else if #available(iOS 11.0, *) {
                    XMMediaPlayer.shared().play()
                    XMMediaPlayer.shared().play()
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                        XMMediaPlayer.shared().seekToTime(self.currPlayInterval)
                    }
                } else {
                    RouterBridge(nil).playerResume()
                }
            }
        }
    }
}
