//
//  XMLBURewardedVideoAdTrigger.swift
//  tingLite
//
//  Created by yangle<PERSON> on 2020/4/13.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import RouterModule
import BUAdSDK
import class MediaModule.XMMediaPlayer

class XMLBURewardedVideoAdTrigger: XMLRewardedVideoAdTrigger {
    
    public class func trigger(with item: XMLADXLoaderItem?, params: [String: Any]) -> XMLBURewardedVideoAdTrigger {
        let trigger = XMLBURewardedVideoAdTrigger()
        trigger.item = item
        trigger.params = params
        return trigger
    }
    
    // 当前广告后台模型
    public var item: XMLADXLoaderItem? = nil {
        didSet {
            neRewardedVideoAd = item?.v_buNEAd
            neRewardedVideoAd?.delegate = self
            neFullscreenVideoAd = item?.v_buNEFAd
            neFullscreenVideoAd?.delegate = self
            busiScene = item?.item?.xml_busiScene ?? .normal
        }
    }
        
    private var neRewardedVideoAd  : BUNativeExpressRewardedVideoAd?   = nil
    private var neFullscreenVideoAd: BUNativeExpressFullscreenVideoAd? = nil

    public func showAd(_ completion: @escaping (([String: Any]) -> Void)) {
        self.dismissCallback = completion
        if let topVC = UIApplication.topViewController(), NSStringFromClass(topVC.classForCoder).contains("XMPlayerViewController") {
            self.isTrigeFromPlayPage = true
        }
        guard let viewController = XMLADXManager.viewController else { buVideoAdDidCloseHandle();return }
        if let adItem = neRewardedVideoAd {
            buVideoAdRenderSuccessHandle(adItem, from: viewController)
        } else if let adItem = neFullscreenVideoAd {
            buVideoAdRenderSuccessHandle(adItem, from: viewController)
        } else {
            buVideoAdDidCloseHandle()
        }
    }
    
    override func timerReachLimitHandle() {
        // 保证当前业务必须是付费解锁(并且不处于详情页)
        guard (self.busiScene == .freeUnLock || self.busiScene == .downloadLimit), !self.isClickedLoadVideo else { return }
        // 当前视图消失
        UIApplication.topViewController()?.dismiss(animated: false, completion: {
            if let topVC = UIApplication.topViewController(), !NSStringFromClass(topVC.classForCoder).contains("XM") {
                topVC.dismiss(animated: false, completion: nil)
            }
        })
        // 消失回调
        self.buVideoAdDidCloseHandle()
    }
}

// MARK: - 穿山甲模版激励视频代理 BUNativeExpressRewardedVideoAdDelegate Method
extension XMLBURewardedVideoAdTrigger: BUNativeExpressRewardedVideoAdDelegate {
    func nativeExpressRewardedVideoAdDidLoad(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) { }
    
    func nativeExpressRewardedVideoAd(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd, didFailWithError error: Error?) {
        if busiScene != .normal, failRetry == false, let completion = self.dismissCallback { // 付费解锁场景加载失败二次重试机制
            failRetry = true
            showAd(completion)
        } else {
            if let obj = self.item { RouterBridge(nil).uploadAdFailureInfo(adObj: obj, status: 1001) }
            buVideoAdDidCloseHandle()
        }
        adLoaderStatus?.nativeVideoAdDidFailure(error: error)
    }
    
    func nativeExpressRewardedVideoAdViewRenderSuccess(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) { }
    
    func nativeExpressRewardedVideoAdViewRenderFail(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd, error: Error?) {
        if busiScene != .normal, failRetry == false, let completion = self.dismissCallback { // 付费解锁场景加载失败二次重试机制
            failRetry = true
            showAd(completion)
        } else {
            if let obj = self.item { RouterBridge(nil).uploadAdFailureInfo(adObj: obj, status: 1001) }
            buVideoAdDidCloseHandle()
        }
    }
    
    func nativeExpressRewardedVideoAdServerRewardDidFail(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) { }
    
    func nativeExpressRewardedVideoAdWillVisible(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) {
        self.buVideoAdWillShowHandle()
        if let exposureCallback = params["exposureCallback"] as? (() -> Void) {
            exposureCallback()
        }
    }
    
    func nativeExpressRewardedVideoAdDidClick(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) {
        self.isClickedLoadVideo = true
        buVideoAdClickHandle()
        adLoaderStatus?.nativeVideoAdClick()
    }
    
    func nativeExpressRewardedVideoAdServerRewardDidSucceed(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd, verify: Bool) {
        if !self.isVerifyVideoResult {
            self.isVerifyVideoResult = verify
        }
        adLoaderStatus?.nativeVideoAdFinishVerify(verify: verify)
    }
    
    func nativeExpressRewardedVideoAdDidClose(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) {
        buVideoAdDidCloseHandle()
        adLoaderStatus?.nativeVideoAdDidClose()
    }
    
    func nativeExpressRewardedVideoAdDidVisible(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in self?.buVideoAdDidExposedHandle() }
        adLoaderStatus?.nativeVideoAdDidVisible()
    }
    
    func nativeExpressRewardedVideoAdDidPlayFinish(_ rewardedVideoAd: BUNativeExpressRewardedVideoAd, didFailWithError error: Error?) {
        if error == nil, !self.isVerifyVideoResult {
            self.isVerifyVideoResult = true
        }
        adLoaderStatus?.nativeVideoAdDidPlayFinish(withError: error)
    }
}

// MARK: - 穿山甲模版全屏视频代理 BUNativeExpressFullscreenVideoAdDelegate Method
extension XMLBURewardedVideoAdTrigger: BUNativeExpressFullscreenVideoAdDelegate {
    
    func nativeExpressFullscreenVideoAdDidLoad(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) { }

    func nativeExpressFullscreenVideoAd(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd, didFailWithError error: Error?) {
        if busiScene == .paidUnLock, failRetry == false, let completion = self.dismissCallback { // 付费解锁场景加载失败二次重试机制
            failRetry = true
            showAd(completion)
        } else {
            if let obj = self.item { RouterBridge(nil).uploadAdFailureInfo(adObj: obj, status: 1001) }
            buVideoAdDidCloseHandle()
        }
    }

    func nativeExpressFullscreenVideoAdViewRenderSuccess(_ rewardedVideoAd: BUNativeExpressFullscreenVideoAd) { }
    
    func nativeExpressFullscreenVideoAdViewRenderFail(_ rewardedVideoAd: BUNativeExpressFullscreenVideoAd, error: Error?) {
        if busiScene == .paidUnLock, failRetry == false, let completion = self.dismissCallback { // 付费解锁场景加载失败二次重试机制
            failRetry = true
            showAd(completion)
        } else {
            if let obj = self.item { RouterBridge(nil).uploadAdFailureInfo(adObj: obj, status: 1001) }
            buVideoAdDidCloseHandle()
        }
    }

    func nativeExpressFullscreenVideoAdDidDownLoadVideo(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) { }
    
    func nativeExpressFullscreenVideoAdWillVisible(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        self.buVideoAdWillShowHandle()
        if let exposureCallback = params["exposureCallback"] as? (() -> Void) {
            exposureCallback()
        }
    }

    func nativeExpressFullscreenVideoAdDidVisible(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in self?.buVideoAdDidExposedHandle() }
    }

    func nativeExpressFullscreenVideoAdDidClick(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        self.isClickedLoadVideo = true
        buVideoAdClickHandle()
    }

    func nativeExpressFullscreenVideoAdDidClickSkip(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) { }

    func nativeExpressFullscreenVideoAdWillClose(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) { }

    func nativeExpressFullscreenVideoAdDidClose(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd) {
        buVideoAdDidCloseHandle()
    }

    func nativeExpressFullscreenVideoAdDidPlayFinish(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd, didFailWithError error: Error?) {
        if error == nil, !self.isVerifyVideoResult {
            self.isVerifyVideoResult = true
        }
    }

    func nativeExpressFullscreenVideoAdCallback(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd, with nativeExpressVideoAdType: BUNativeExpressFullScreenAdType) { }

    func nativeExpressFullscreenVideoAdDidCloseOtherController(_ fullscreenVideoAd: BUNativeExpressFullscreenVideoAd, interactionType: BUInteractionType) { }
}

// MARK: - 全屏视频广告辅助函数
extension XMLBURewardedVideoAdTrigger {
    
    func buVideoAdRenderSuccessHandle(_ obj: Any, from viewController: UIViewController) {
        var isSuccessShow: Bool = false
        if let rewardedVideoAd = obj as? BUNativeExpressRewardedVideoAd {
            isSuccessShow = rewardedVideoAd.show(fromRootViewController: viewController)
        } else if let fullscreenVideoAd = obj as? BUNativeExpressFullscreenVideoAd {
            isSuccessShow = fullscreenVideoAd.show(fromRootViewController: viewController)
        }
        if !isSuccessShow {
            buVideoAdDidCloseHandle()
        }
    }
    
    func buVideoAdWillShowHandle() {
        // 开启激励视频亮屏统计
        self.beginStatistic()
        videoStartTime = Date().timeIntervalSince1970
        isRemovedNPWindow = false
        if RouterBridge(nil).isNPWindowShowing() {
            isRemovedNPWindow = true
            RouterBridge(nil).removeNPWindow(true)
        }
        
        if XMLADXConfig.isPauseAudioWhenADSound() && RouterBridge(nil).playerIsPlaying() {
            if #available(iOS 12.0, *) {
                RouterBridge(nil).playerPause()
            } else if #available(iOS 11.0, *) {
                self.currPlayInterval = XMMediaPlayer.shared().currentTime ?? 0
                RouterBridge(nil).playerStop()
            } else {
                RouterBridge(nil).playerPause()
            }
            self.isPausedXMAudio = true
        }
        
        XMLADXWidgetManager.hiddenBlackWidgetsIfNeed()
        self.item?.uploadVisibleStatisticInfo()
        XMLADXReportManager.sendShowReportInfo(self.item?.item, positionName: (params["positionName"] as? String) ?? "", vender: XMLAdType.bu.sdkType)
    }
    
    func buVideoAdDidExposedHandle() {
        // 所有场景广告验证走SDK回调
        self.isVerifyVideoResult = false
        // 特殊业务处理
        if busiScene != .normal, let config = self.item?.item as? XMLPaidUnlockAdTypeItemModel {
            self.toolBar = XMLRewardedVideoToolBar.showIfNeed(config, isTrigeFromPlayPage: self.isTrigeFromPlayPage) { [weak self] (isCancel, isOpenVIP) in
                guard let wself = self else { return }
                if !isCancel { XMLADXReportManager.sendShowTimeReportInfo(wself.item?.item, playTime: TimeInterval(max(config.pul_videoTime, config.pul_videoCloseTime))) }
                if isOpenVIP && wself.busiScene == .paidUnLock {
                    wself.buVideoAdDidCloseHandle(false, vipOpenStatus: true)
                } else {
                    isCancel ? wself.buVideoAdDidCloseHandle(true) : wself.setupTimerAndStart(config.pul_endFrameTime)
                }
            }
        } else if busiScene == .adFree, let config = self.item?.item {
            XMLVideoADFreeTopMask.showIfNeed(config, totalTime: 0, isTrigeFromPlayPage: self.isTrigeFromPlayPage) { [weak self] isCancel, isOpenVIP  in
                guard let wself = self, isCancel else { return }
                wself.buVideoAdDidCloseHandle(isCancel, vipOpenStatus: isOpenVIP)
            }
        } else if self.neFullscreenVideoAd != nil {
            XMLVideoADFullTopMask.showIfNeed(XMLADXConfig.kFullVideoMaskHiddenTime(), callback: nil)
        } else if XMLADXConfig.enableVideoSuperviseControl() { // 是否开启监管模式
            XMLVideoADClaimTopMask.showIfNeed(XMLADXConfig.kVideoMantleCountDown(), isTrigeFromPlayPage: self.isTrigeFromPlayPage, callback: { [weak self] isCancel in
                guard let wself = self, isCancel else { return }
                wself.buVideoAdDidCloseHandle(isCancel, vipOpenStatus: false)
            })
        }
    }
    
    func buVideoAdClickHandle() {
        self.currentVC = UIApplication.topViewController()
        (UIApplication.shared.keyWindow?.viewWithTag(kXMLRewardedVideoToolBarTag) as? XMLRewardedVideoToolBar)?.currentVC = UIApplication.topViewController()
        (UIApplication.shared.keyWindow?.viewWithTag(kXMLRewardedVideoToolBarTag) as? XMLRewardedVideoToolBar)?.isClickedLoadVideo = true
        (UIApplication.shared.keyWindow?.viewWithTag(kXMLVideoADFreeTopMaskTag) as? XMLVideoADFreeTopMask)?.currentVC = UIApplication.topViewController()
        (UIApplication.shared.keyWindow?.viewWithTag(kXMLVideoADFreeTopMaskTag) as? XMLVideoADFreeTopMask)?.isClickedLoadVideo = true
        XMLADXReportManager.sendClickReportInfo(self.item?.item, positionName: (params["positionName"] as? String) ?? "", vender: XMLAdType.bu.sdkType)
    }
    
    func buVideoAdDidCloseHandle(_ isForceClose: Bool = false, vipOpenStatus: Bool = false) {
        // 特殊业务处理
        if busiScene != .normal {
            XMLRewardedVideoToolBar.hideIfNeed()
            XMLVideoADFreeTopMask.hideIfNeed()
        }
        //强制关闭视频
        if isForceClose {
            adLoaderStatus?.nativeVideoAdDidClose()
        }
        XMLADXWidgetManager.recoverBlackWidgetsIfNeed()
        if isRemovedNPWindow {
            isRemovedNPWindow = false
            RouterBridge(nil).showOnMainTab(true)
        }
        XMLVideoADClaimTopMask.hideIfNeed()
        // 结束激励视频亮屏统计
        self.endStatistic()
        self.isSuccessLoadVideo = self.isSuccessLoadVideo && self.isVerifyVideoResult
        let params: [String: Any] = ["isVerifyVideoResult": self.isVerifyVideoResult,
                                     "isClickedLoadVideo" : self.isClickedLoadVideo,
                                     "isSuccessLoadVideo" : self.isSuccessLoadVideo,
                                     "rewardedAdCloseCode": isForceClose  ? 1 : 0,
                                     "isOpenVIPStatus"    : vipOpenStatus ? 1 : 0]
        self.dismissCallback?(params)
        self.dismissCallback = nil
        if XMLADXConfig.isPauseAudioWhenADSound() && self.isPausedXMAudio {
            self.isPausedXMAudio = false
            let seekToTime: TimeInterval = self.currPlayInterval
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { // 延时处理，有得广告SDK AudioSession配置 放在此消失后面，故延时
                XMLADXManager.shared().recoverAudioControlIfNeed(true)
                if #available(iOS 14.0, *) {
                    XMMediaPlayer.shared().play()
                    XMMediaPlayer.shared().pause()
                    XMMediaPlayer.shared().play()
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                        XMMediaPlayer.shared().play()
                    }
                } else if #available(iOS 12.0, *) {
                    RouterBridge(nil).playerResume()
                } else if #available(iOS 11.0, *) {
                    XMMediaPlayer.shared().play()
                    XMMediaPlayer.shared().play()
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                        XMMediaPlayer.shared().seekToTime(seekToTime)
                    }
                } else {
                    RouterBridge(nil).playerResume()
                }
            }
        }
        XMLADXManager.resetRewardVideoEnvironment()
    }
}
