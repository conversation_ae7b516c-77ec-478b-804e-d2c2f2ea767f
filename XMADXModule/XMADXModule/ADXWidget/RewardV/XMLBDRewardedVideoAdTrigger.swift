//
//  XMLBDRewardedVideoAdTrigger.swift
//  XMADXModule
//
//  Created by yangle<PERSON> on 2020/6/11.
//  Copyright © 2020 ximalaya. All rights reserved.
//

import Foundation
import RouterModule
import BaiduMobAdSDK

class XMLBDRewardedVideoAdTrigger: XMLRewardedVideoAdTrigger {
    
    public class func trigger(with item: XMLADXLoaderItem?, params: [String: Any]) -> XMLBDRewardedVideoAdTrigger {
        let trigger = XMLBDRewardedVideoAdTrigger()
        trigger.item = item
        trigger.params = params
        return trigger
    }
    
    // 当前广告后台模型
    public var item: XMLADXLoaderItem? = nil {
        didSet {
            rewardedVideoAd = item?.v_bdAd
            rewardedVideoAd?.delegate = self
            busiScene = item?.item?.xml_busiScene ?? .normal
        }
    }
        
    private var rewardedVideoAd  : BaiduMobAdRewardVideo? = nil
    
    public func showAd(_ completion: @escaping (([String: Any]) -> Void)) {
        self.dismissCallback = completion
        if let topVC = UIApplication.topViewController(), NSStringFromClass(topVC.classForCoder).contains("XMPlayerViewController") {
            self.isTrigeFromPlayPage = true
        }
        if let adItem = rewardedVideoAd, adItem.isReady(), let viewController = XMLADXManager.viewController {
            bdVideoAdRenderSuccessHandle(adItem, from: viewController)
        } else {
            bdVideoAdDidCloseHandle()
        }
    }
    
    override func timerReachLimitHandle() {
        // 保证当前业务必须是付费解锁(并且不处于详情页)
        guard (self.busiScene == .freeUnLock || self.busiScene == .downloadLimit), !self.isClickedLoadVideo else { return }
        // 当前视图消失
        UIApplication.topViewController()?.dismiss(animated: false, completion: {
            if let topVC = UIApplication.topViewController(), !NSStringFromClass(topVC.classForCoder).contains("XM") {
                topVC.dismiss(animated: false, completion: nil)
            }
        })
        // 消失回调
        self.bdVideoAdDidCloseHandle()
    }
}

// MARK: - 百度百青藤激励视频代理 BaiduMobAdRewardVideoDelegate Method
extension XMLBDRewardedVideoAdTrigger: BaiduMobAdRewardVideoDelegate {
    
    func rewardedAdLoadSuccess(_ video: BaiduMobAdRewardVideo!) { }
    
    func rewardedAdLoadFailCode(_ errCode: String!, message: String!, rewardedAd video: BaiduMobAdRewardVideo!) { }
    
    func rewardedVideoAdLoaded(_ video: BaiduMobAdRewardVideo!) { }
    
    func rewardedVideoAdLoadFailed(_ video: BaiduMobAdRewardVideo!, withError reason: BaiduMobFailReason) { }
    
    func rewardedVideoAdDidStarted(_ video: BaiduMobAdRewardVideo!) {
        self.bdVideoAdWillShowHandle()
        if let exposureCallback = params["exposureCallback"] as? (() -> Void) {
            exposureCallback()
        }
    }
    
    func rewardedVideoAdShowFailed(_ video: BaiduMobAdRewardVideo!, withError reason: BaiduMobFailReason) {
        if busiScene != .normal, failRetry == false, let completion = self.dismissCallback { // 付费解锁场景加载失败二次重试机制
            failRetry = true
            showAd(completion)
            adLoaderStatus?.nativeVideoAdDidVisible()
        } else {
            if let obj = self.item { RouterBridge(nil).uploadAdFailureInfo(adObj: obj, status: 1001) }
            bdVideoAdDidCloseHandle()
            adLoaderStatus?.nativeVideoAdDidClose()
        }
    }
    
    func rewardedVideoAdDidPlayFinish(_ video: BaiduMobAdRewardVideo!) {
        if !self.isVerifyVideoResult {
            self.isVerifyVideoResult = true
        }
        adLoaderStatus?.nativeVideoAdFinishVerify(verify: true)
        adLoaderStatus?.nativeVideoAdDidPlayFinish(withError: nil)
    }
    
    func rewardedVideoAdDidClick(_ video: BaiduMobAdRewardVideo!, withPlayingProgress progress: CGFloat) {
        self.isClickedLoadVideo = true
        bdVideoAdClickHandle()
        adLoaderStatus?.nativeVideoAdClick()
    }
    
    func rewardedVideoAdDidClose(_ video: BaiduMobAdRewardVideo!, withPlayingProgress progress: CGFloat) {
        bdVideoAdDidCloseHandle()
        adLoaderStatus?.nativeVideoAdDidClose()
    }
}

// MARK: - 全屏视频广告辅助函数
extension XMLBDRewardedVideoAdTrigger {
    
    func bdVideoAdRenderSuccessHandle(_ obj: Any, from viewController: UIViewController) {
        if let rewardedVideoAd = obj as? BaiduMobAdRewardVideo, rewardedVideoAd.isReady() {
            rewardedVideoAd.show(from: viewController)
        } else {
            bdVideoAdDidCloseHandle()
        }
    }
    
    func bdVideoAdWillShowHandle() {
        // 开启激励视频亮屏统计
        self.beginStatistic()
        videoStartTime = Date().timeIntervalSince1970
        isRemovedNPWindow = false
        if RouterBridge(nil).isNPWindowShowing() {
            isRemovedNPWindow = true
            RouterBridge(nil).removeNPWindow(true)
        }
        
        if XMLADXConfig.isPauseAudioWhenADSound() && RouterBridge(nil).playerIsPlaying() {
            RouterBridge(nil).playerPause()
            self.isPausedXMAudio = true
        }
        
        XMLADXWidgetManager.hiddenBlackWidgetsIfNeed()
        let hasPlayVideo = self.item?.item?.hasVideoPlayTingShowed ?? false
        guard !hasPlayVideo else { return }
        
        self.item?.uploadVisibleStatisticInfo()
        XMLADXReportManager.sendShowReportInfo(self.item?.item, positionName: (params["positionName"] as? String) ?? "", vender: XMLAdType.bd.sdkType)
        self.item?.item?.hasVideoPlayTingShowed = true
        // 所有场景广告验证走SDK回调
        self.isVerifyVideoResult = false
        // 特殊业务处理
        if busiScene != .normal, let config = self.item?.item as? XMLPaidUnlockAdTypeItemModel {
            self.toolBar = XMLRewardedVideoToolBar.showIfNeed(config, isTrigeFromPlayPage: self.isTrigeFromPlayPage) { [weak self] (isCancel, isOpenVIP) in
                guard let wself = self else { return }
                if !isCancel { XMLADXReportManager.sendShowTimeReportInfo(wself.item?.item, playTime: TimeInterval(max(config.pul_videoTime, config.pul_videoCloseTime))) }
                if isOpenVIP && wself.busiScene == .paidUnLock {
                    wself.bdVideoAdDidCloseHandle(false, vipOpenStatus: true)
                } else {
                    isCancel ? wself.bdVideoAdDidCloseHandle(true) : wself.setupTimerAndStart(config.pul_endFrameTime)
                }
            }
        } else if busiScene == .adFree, let config = self.item?.item {
            XMLVideoADFreeTopMask.showIfNeed(config, totalTime: 0, isTrigeFromPlayPage: self.isTrigeFromPlayPage) { [weak self] isCancel, isOpenVIP  in
                guard let wself = self, isCancel else { return }
                wself.bdVideoAdDidCloseHandle(isCancel, vipOpenStatus: isOpenVIP)
            }
        } else if XMLADXConfig.enableVideoSuperviseControl() { // 是否开启监管模式
            XMLVideoADClaimTopMask.showIfNeed(XMLADXConfig.kVideoMantleCountDown(), isTrigeFromPlayPage: self.isTrigeFromPlayPage, callback: { [weak self] isCancel in
                guard let wself = self, isCancel else { return }
                wself.bdVideoAdDidCloseHandle(isCancel, vipOpenStatus: false)
            })
        }
    }
    
    func bdVideoAdClickHandle() {
        self.currentVC = UIApplication.topViewController()
        (UIApplication.shared.keyWindow?.viewWithTag(kXMLRewardedVideoToolBarTag) as? XMLRewardedVideoToolBar)?.currentVC = UIApplication.topViewController()
        (UIApplication.shared.keyWindow?.viewWithTag(kXMLRewardedVideoToolBarTag) as? XMLRewardedVideoToolBar)?.isClickedLoadVideo = true
        (UIApplication.shared.keyWindow?.viewWithTag(kXMLVideoADFreeTopMaskTag) as? XMLVideoADFreeTopMask)?.currentVC = UIApplication.topViewController()
        (UIApplication.shared.keyWindow?.viewWithTag(kXMLVideoADFreeTopMaskTag) as? XMLVideoADFreeTopMask)?.isClickedLoadVideo = true
        XMLADXReportManager.sendClickReportInfo(self.item?.item, positionName: (params["positionName"] as? String) ?? "", vender: XMLAdType.bd.sdkType)
    }
    
    func bdVideoAdDidCloseHandle(_ isForceClose: Bool = false, vipOpenStatus: Bool = false) {
        // 特殊业务处理
        if busiScene != .normal {
            XMLRewardedVideoToolBar.hideIfNeed()
            XMLVideoADFreeTopMask.hideIfNeed()
        }
        //强制关闭视频
        if isForceClose {
            adLoaderStatus?.nativeVideoAdDidClose()
        }
        XMLADXWidgetManager.recoverBlackWidgetsIfNeed()
        if isRemovedNPWindow {
            isRemovedNPWindow = false
            RouterBridge(nil).showOnMainTab(true)
        }
        XMLVideoADClaimTopMask.hideIfNeed()
        // 结束激励视频亮屏统计
        self.endStatistic()
        self.isSuccessLoadVideo = self.isSuccessLoadVideo && self.isVerifyVideoResult
        let params: [String: Any] = ["isVerifyVideoResult": self.isVerifyVideoResult,
                                     "isClickedLoadVideo" : self.isClickedLoadVideo,
                                     "isSuccessLoadVideo" : self.isSuccessLoadVideo,
                                     "rewardedAdCloseCode": isForceClose  ? 1 : 0,
                                     "isOpenVIPStatus"    : vipOpenStatus ? 1 : 0]
        self.dismissCallback?(params)
        self.dismissCallback = nil
        
        if XMLADXConfig.isPauseAudioWhenADSound() && self.isPausedXMAudio {
            self.isPausedXMAudio = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { // 延时处理，有得广告SDK AudioSession配置 放在此消失后面，故延时
                XMLADXManager.shared().recoverAudioControlIfNeed()
                RouterBridge(nil).playerResume()
            }
        }
        XMLADXManager.resetRewardVideoEnvironment()
    }
}
