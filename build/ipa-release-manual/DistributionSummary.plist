<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>tingLite.ipa</key>
	<array>
		<dict>
			<key>architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>buildNumber</key>
			<string>250321</string>
			<key>certificate</key>
			<dict>
				<key>SHA1</key>
				<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
				<key>dateExpires</key>
				<string>2026/6/20</string>
				<key>type</key>
				<string>Apple Development</string>
			</dict>
			<key>embeddedBinaries</key>
			<array>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>AFNetworking.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>4.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>CryptoSwift.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>FDFullscreenPopGesture.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.1.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>GDTMobSDK.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>MJRefresh.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>3.5.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>ObjcExceptionBridging.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>7.0.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>XCGLogger.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>7.0.0</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>XMLOCBridge.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>0.0.1</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>XMNetworkRequest.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.1.13</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>iCarousel.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.8.3</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1.0</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>name</key>
					<string>mpa_client.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.0.3</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>250321</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>FCD849BBC11C653D9B7C8CC60D0B6FCC517B00AA</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>type</key>
						<string>Apple Development</string>
					</dict>
					<key>entitlements</key>
					<dict>
						<key>application-identifier</key>
						<string>J32AD5KDR3.com.ximalaya.tingLite.tingLiteAPNSerEx</string>
						<key>com.apple.developer.team-identifier</key>
						<string>J32AD5KDR3</string>
						<key>get-task-allow</key>
						<true/>
					</dict>
					<key>name</key>
					<string>tingLiteAPNSerEx.appex</string>
					<key>profile</key>
					<dict>
						<key>UUID</key>
						<string>5244ac9d-28a7-41ac-9b15-30c8138322df</string>
						<key>dateExpires</key>
						<string>2026/6/20</string>
						<key>name</key>
						<string>iOS Team Provisioning Profile: com.ximalaya.tingLite.tingLiteAPNSerEx</string>
					</dict>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>J32AD5KDR3</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>3.0.36</string>
				</dict>
			</array>
			<key>entitlements</key>
			<dict>
				<key>application-identifier</key>
				<string>J32AD5KDR3.com.ximalaya.tingLite</string>
				<key>aps-environment</key>
				<string>development</string>
				<key>com.apple.developer.applesignin</key>
				<array>
					<string>Default</string>
				</array>
				<key>com.apple.developer.associated-domains</key>
				<array>
					<string>applinks:m.ximalaya.com</string>
					<string>applinks:www.ximalaya.com</string>
					<string>applinks:lite.ximalaya.com</string>
				</array>
				<key>com.apple.developer.team-identifier</key>
				<string>J32AD5KDR3</string>
				<key>get-task-allow</key>
				<true/>
			</dict>
			<key>name</key>
			<string>tingLite.app</string>
			<key>profile</key>
			<dict>
				<key>UUID</key>
				<string>5e972028-0514-4d6b-98e8-4c7a8b5c35a9</string>
				<key>dateExpires</key>
				<string>2026/6/20</string>
				<key>name</key>
				<string>iOS Team Provisioning Profile: com.ximalaya.tingLite</string>
			</dict>
			<key>team</key>
			<dict>
				<key>id</key>
				<string>J32AD5KDR3</string>
				<key>name</key>
				<string></string>
			</dict>
			<key>versionNumber</key>
			<string>3.0.36</string>
		</dict>
	</array>
</dict>
</plist>
