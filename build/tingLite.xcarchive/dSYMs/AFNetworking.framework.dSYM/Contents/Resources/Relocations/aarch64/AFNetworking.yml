---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/Lite_ting/tingLite/DerivedData/tingLite/Build/Intermediates.noindex/ArchiveIntermediates/tingLite/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/AFNetworking.framework/AFNetworking'
relocations:
  - { offset: 0x5CEC5, size: 0x8, addend: 0x0, symName: _AFNetworkingVersionString, symObjAddr: 0x0, symBinAddr: 0x25D00, symSize: 0x0 }
  - { offset: 0x5CEFA, size: 0x8, addend: 0x0, symName: _AFNetworkingVersionNumber, symObjAddr: 0x30, symBinAddr: 0x25D30, symSize: 0x0 }
  - { offset: 0x5CF37, size: 0x8, addend: 0x0, symName: '-[AFCachedImage initWithImage:identifier:]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0xF4 }
  - { offset: 0x5D093, size: 0x8, addend: 0x0, symName: '-[AFCachedImage initWithImage:identifier:]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0xF4 }
  - { offset: 0x5D129, size: 0x8, addend: 0x0, symName: '-[AFCachedImage accessImage]', symObjAddr: 0xF4, symBinAddr: 0x80F4, symSize: 0x4C }
  - { offset: 0x5D160, size: 0x8, addend: 0x0, symName: '-[AFCachedImage description]', symObjAddr: 0x140, symBinAddr: 0x8140, symSize: 0x8C }
  - { offset: 0x5D1A7, size: 0x8, addend: 0x0, symName: '-[AFCachedImage image]', symObjAddr: 0x1CC, symBinAddr: 0x81CC, symSize: 0x8 }
  - { offset: 0x5D1DE, size: 0x8, addend: 0x0, symName: '-[AFCachedImage setImage:]', symObjAddr: 0x1D4, symBinAddr: 0x81D4, symSize: 0xC }
  - { offset: 0x5D21F, size: 0x8, addend: 0x0, symName: '-[AFCachedImage identifier]', symObjAddr: 0x1E0, symBinAddr: 0x81E0, symSize: 0x8 }
  - { offset: 0x5D256, size: 0x8, addend: 0x0, symName: '-[AFCachedImage setIdentifier:]', symObjAddr: 0x1E8, symBinAddr: 0x81E8, symSize: 0x8 }
  - { offset: 0x5D295, size: 0x8, addend: 0x0, symName: '-[AFCachedImage totalBytes]', symObjAddr: 0x1F0, symBinAddr: 0x81F0, symSize: 0x8 }
  - { offset: 0x5D2CC, size: 0x8, addend: 0x0, symName: '-[AFCachedImage setTotalBytes:]', symObjAddr: 0x1F8, symBinAddr: 0x81F8, symSize: 0x8 }
  - { offset: 0x5D309, size: 0x8, addend: 0x0, symName: '-[AFCachedImage lastAccessDate]', symObjAddr: 0x200, symBinAddr: 0x8200, symSize: 0x8 }
  - { offset: 0x5D340, size: 0x8, addend: 0x0, symName: '-[AFCachedImage setLastAccessDate:]', symObjAddr: 0x208, symBinAddr: 0x8208, symSize: 0xC }
  - { offset: 0x5D381, size: 0x8, addend: 0x0, symName: '-[AFCachedImage currentMemoryUsage]', symObjAddr: 0x214, symBinAddr: 0x8214, symSize: 0x8 }
  - { offset: 0x5D3B8, size: 0x8, addend: 0x0, symName: '-[AFCachedImage setCurrentMemoryUsage:]', symObjAddr: 0x21C, symBinAddr: 0x821C, symSize: 0x8 }
  - { offset: 0x5D3F5, size: 0x8, addend: 0x0, symName: '-[AFCachedImage .cxx_destruct]', symObjAddr: 0x224, symBinAddr: 0x8224, symSize: 0x3C }
  - { offset: 0x5D428, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache init]', symObjAddr: 0x260, symBinAddr: 0x8260, symSize: 0xC }
  - { offset: 0x5D45D, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache initWithMemoryCapacity:preferredMemoryCapacity:]', symObjAddr: 0x26C, symBinAddr: 0x826C, symSize: 0x178 }
  - { offset: 0x5D50A, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache dealloc]', symObjAddr: 0x3E4, symBinAddr: 0x83E4, symSize: 0x68 }
  - { offset: 0x5D53D, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache memoryUsage]', symObjAddr: 0x44C, symBinAddr: 0x844C, symSize: 0xC4 }
  - { offset: 0x5D5C6, size: 0x8, addend: 0x0, symName: '___38-[AFAutoPurgingImageCache memoryUsage]_block_invoke', symObjAddr: 0x510, symBinAddr: 0x8510, symSize: 0x30 }
  - { offset: 0x5D615, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40r, symObjAddr: 0x540, symBinAddr: 0x8540, symSize: 0x34 }
  - { offset: 0x5D63E, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40r, symObjAddr: 0x574, symBinAddr: 0x8574, symSize: 0x2C }
  - { offset: 0x5D65D, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache addImage:withIdentifier:]', symObjAddr: 0x5A0, symBinAddr: 0x85A0, symSize: 0x130 }
  - { offset: 0x5D6F7, size: 0x8, addend: 0x0, symName: '___51-[AFAutoPurgingImageCache addImage:withIdentifier:]_block_invoke', symObjAddr: 0x6D0, symBinAddr: 0x86D0, symSize: 0xF4 }
  - { offset: 0x5D785, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s, symObjAddr: 0x7C4, symBinAddr: 0x87C4, symSize: 0x30 }
  - { offset: 0x5D7AE, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s, symObjAddr: 0x7F4, symBinAddr: 0x87F4, symSize: 0x30 }
  - { offset: 0x5D7CD, size: 0x8, addend: 0x0, symName: '___51-[AFAutoPurgingImageCache addImage:withIdentifier:]_block_invoke.51', symObjAddr: 0x824, symBinAddr: 0x8824, symSize: 0x26C }
  - { offset: 0x5D87A, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s, symObjAddr: 0xA90, symBinAddr: 0x8A90, symSize: 0x8 }
  - { offset: 0x5D8A1, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0xA98, symBinAddr: 0x8A98, symSize: 0x8 }
  - { offset: 0x5D8C0, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache removeImageWithIdentifier:]', symObjAddr: 0xAA0, symBinAddr: 0x8AA0, symSize: 0xF8 }
  - { offset: 0x5D94D, size: 0x8, addend: 0x0, symName: '___53-[AFAutoPurgingImageCache removeImageWithIdentifier:]_block_invoke', symObjAddr: 0xB98, symBinAddr: 0x8B98, symSize: 0xB4 }
  - { offset: 0x5D9CB, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48r, symObjAddr: 0xC4C, symBinAddr: 0x8C4C, symSize: 0x3C }
  - { offset: 0x5D9F4, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48r, symObjAddr: 0xC88, symBinAddr: 0x8C88, symSize: 0x34 }
  - { offset: 0x5DA13, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache removeAllImages]', symObjAddr: 0xCBC, symBinAddr: 0x8CBC, symSize: 0xC4 }
  - { offset: 0x5DA74, size: 0x8, addend: 0x0, symName: '___42-[AFAutoPurgingImageCache removeAllImages]_block_invoke', symObjAddr: 0xD80, symBinAddr: 0x8D80, symSize: 0x88 }
  - { offset: 0x5DAC3, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache imageWithIdentifier:]', symObjAddr: 0xE08, symBinAddr: 0x8E08, symSize: 0x11C }
  - { offset: 0x5DB3C, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0xF24, symBinAddr: 0x8F24, symSize: 0x10 }
  - { offset: 0x5DB61, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0xF34, symBinAddr: 0x8F34, symSize: 0x8 }
  - { offset: 0x5DB80, size: 0x8, addend: 0x0, symName: '___47-[AFAutoPurgingImageCache imageWithIdentifier:]_block_invoke', symObjAddr: 0xF3C, symBinAddr: 0x8F3C, symSize: 0x80 }
  - { offset: 0x5DBFE, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache addImage:forRequest:withAdditionalIdentifier:]', symObjAddr: 0xFBC, symBinAddr: 0x8FBC, symSize: 0x70 }
  - { offset: 0x5DC61, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache removeImageforRequest:withAdditionalIdentifier:]', symObjAddr: 0x102C, symBinAddr: 0x902C, symSize: 0x48 }
  - { offset: 0x5DCB8, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache imageforRequest:withAdditionalIdentifier:]', symObjAddr: 0x1074, symBinAddr: 0x9074, symSize: 0x50 }
  - { offset: 0x5DD0F, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache imageCacheKeyFromURLRequest:withAdditionalIdentifier:]', symObjAddr: 0x10C4, symBinAddr: 0x90C4, symSize: 0x90 }
  - { offset: 0x5DD72, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache shouldCacheImage:forRequest:withAdditionalIdentifier:]', symObjAddr: 0x1154, symBinAddr: 0x9154, symSize: 0x8 }
  - { offset: 0x5DDC9, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache memoryCapacity]', symObjAddr: 0x115C, symBinAddr: 0x915C, symSize: 0x8 }
  - { offset: 0x5DE00, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache setMemoryCapacity:]', symObjAddr: 0x1164, symBinAddr: 0x9164, symSize: 0x8 }
  - { offset: 0x5DE3D, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache preferredMemoryUsageAfterPurge]', symObjAddr: 0x116C, symBinAddr: 0x916C, symSize: 0x8 }
  - { offset: 0x5DE74, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache setPreferredMemoryUsageAfterPurge:]', symObjAddr: 0x1174, symBinAddr: 0x9174, symSize: 0x8 }
  - { offset: 0x5DEB1, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache cachedImages]', symObjAddr: 0x117C, symBinAddr: 0x917C, symSize: 0x8 }
  - { offset: 0x5DEE8, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache setCachedImages:]', symObjAddr: 0x1184, symBinAddr: 0x9184, symSize: 0xC }
  - { offset: 0x5DF29, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache currentMemoryUsage]', symObjAddr: 0x1190, symBinAddr: 0x9190, symSize: 0x8 }
  - { offset: 0x5DF60, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache setCurrentMemoryUsage:]', symObjAddr: 0x1198, symBinAddr: 0x9198, symSize: 0x8 }
  - { offset: 0x5DF9D, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache synchronizationQueue]', symObjAddr: 0x11A0, symBinAddr: 0x91A0, symSize: 0x8 }
  - { offset: 0x5DFD4, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache setSynchronizationQueue:]', symObjAddr: 0x11A8, symBinAddr: 0x91A8, symSize: 0xC }
  - { offset: 0x5E015, size: 0x8, addend: 0x0, symName: '-[AFAutoPurgingImageCache .cxx_destruct]', symObjAddr: 0x11B4, symBinAddr: 0x91B4, symSize: 0x30 }
  - { offset: 0x5E496, size: 0x8, addend: 0x0, symName: '+[AFHTTPSessionManager manager]', symObjAddr: 0x0, symBinAddr: 0x91E4, symSize: 0x20 }
  - { offset: 0x5E7BB, size: 0x8, addend: 0x0, symName: '+[AFHTTPSessionManager manager]', symObjAddr: 0x0, symBinAddr: 0x91E4, symSize: 0x20 }
  - { offset: 0x5E7F2, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager init]', symObjAddr: 0x20, symBinAddr: 0x9204, symSize: 0x8 }
  - { offset: 0x5E827, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager initWithBaseURL:]', symObjAddr: 0x28, symBinAddr: 0x920C, symSize: 0x8 }
  - { offset: 0x5E86A, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager initWithSessionConfiguration:]', symObjAddr: 0x30, symBinAddr: 0x9214, symSize: 0xC }
  - { offset: 0x5E8B1, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager initWithBaseURL:sessionConfiguration:]', symObjAddr: 0x3C, symBinAddr: 0x9220, symSize: 0x168 }
  - { offset: 0x5E908, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager setRequestSerializer:]', symObjAddr: 0x1A4, symBinAddr: 0x9388, symSize: 0x14 }
  - { offset: 0x5E94B, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager setResponseSerializer:]', symObjAddr: 0x1B8, symBinAddr: 0x939C, symSize: 0x34 }
  - { offset: 0x5E98E, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager setSecurityPolicy:]', symObjAddr: 0x1EC, symBinAddr: 0x93D0, symSize: 0x12C }
  - { offset: 0x5EA00, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager GET:parameters:headers:progress:success:failure:]', symObjAddr: 0x318, symBinAddr: 0x94FC, symSize: 0x5C }
  - { offset: 0x5EAA7, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager HEAD:parameters:headers:success:failure:]', symObjAddr: 0x374, symBinAddr: 0x9558, symSize: 0xD4 }
  - { offset: 0x5EB3E, size: 0x8, addend: 0x0, symName: '___64-[AFHTTPSessionManager HEAD:parameters:headers:success:failure:]_block_invoke', symObjAddr: 0x448, symBinAddr: 0x962C, symSize: 0x14 }
  - { offset: 0x5EB9F, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b, symObjAddr: 0x45C, symBinAddr: 0x9640, symSize: 0x10 }
  - { offset: 0x5EBC8, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager POST:parameters:headers:progress:success:failure:]', symObjAddr: 0x474, symBinAddr: 0x9650, symSize: 0x5C }
  - { offset: 0x5EC7D, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager POST:parameters:headers:constructingBodyWithBlock:progress:success:failure:]', symObjAddr: 0x4D0, symBinAddr: 0x96AC, symSize: 0x464 }
  - { offset: 0x5EDC4, size: 0x8, addend: 0x0, symName: '___99-[AFHTTPSessionManager POST:parameters:headers:constructingBodyWithBlock:progress:success:failure:]_block_invoke', symObjAddr: 0x934, symBinAddr: 0x9B10, symSize: 0x14 }
  - { offset: 0x5EE22, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40b, symObjAddr: 0x948, symBinAddr: 0x9B24, symSize: 0x34 }
  - { offset: 0x5EE4B, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s, symObjAddr: 0x97C, symBinAddr: 0x9B58, symSize: 0x28 }
  - { offset: 0x5EE6A, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x9A4, symBinAddr: 0x9B80, symSize: 0x10 }
  - { offset: 0x5EE8F, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x9B4, symBinAddr: 0x9B90, symSize: 0x8 }
  - { offset: 0x5EEAE, size: 0x8, addend: 0x0, symName: '___99-[AFHTTPSessionManager POST:parameters:headers:constructingBodyWithBlock:progress:success:failure:]_block_invoke.33', symObjAddr: 0x9BC, symBinAddr: 0x9B98, symSize: 0xA8 }
  - { offset: 0x5EF45, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b40b48r, symObjAddr: 0xA64, symBinAddr: 0x9C40, symSize: 0x4C }
  - { offset: 0x5EF6E, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager PUT:parameters:headers:success:failure:]', symObjAddr: 0xAE4, symBinAddr: 0x9C8C, symSize: 0x5C }
  - { offset: 0x5F005, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager PATCH:parameters:headers:success:failure:]', symObjAddr: 0xB40, symBinAddr: 0x9CE8, symSize: 0x5C }
  - { offset: 0x5F09C, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager DELETE:parameters:headers:success:failure:]', symObjAddr: 0xB9C, symBinAddr: 0x9D44, symSize: 0x5C }
  - { offset: 0x5F133, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager dataTaskWithHTTPMethod:URLString:parameters:headers:uploadProgress:downloadProgress:success:failure:]', symObjAddr: 0xBF8, symBinAddr: 0x9DA0, symSize: 0x464 }
  - { offset: 0x5F272, size: 0x8, addend: 0x0, symName: '___124-[AFHTTPSessionManager dataTaskWithHTTPMethod:URLString:parameters:headers:uploadProgress:downloadProgress:success:failure:]_block_invoke', symObjAddr: 0x105C, symBinAddr: 0xA204, symSize: 0x14 }
  - { offset: 0x5F2D4, size: 0x8, addend: 0x0, symName: '___124-[AFHTTPSessionManager dataTaskWithHTTPMethod:URLString:parameters:headers:uploadProgress:downloadProgress:success:failure:]_block_invoke_2', symObjAddr: 0x1070, symBinAddr: 0xA218, symSize: 0xA8 }
  - { offset: 0x5F373, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager description]', symObjAddr: 0x1118, symBinAddr: 0xA2C0, symSize: 0xF8 }
  - { offset: 0x5F3DA, size: 0x8, addend: 0x0, symName: '+[AFHTTPSessionManager supportsSecureCoding]', symObjAddr: 0x1210, symBinAddr: 0xA3B8, symSize: 0x8 }
  - { offset: 0x5F40E, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager initWithCoder:]', symObjAddr: 0x1218, symBinAddr: 0xA3C0, symSize: 0x274 }
  - { offset: 0x5F508, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager encodeWithCoder:]', symObjAddr: 0x148C, symBinAddr: 0xA634, symSize: 0x25C }
  - { offset: 0x5F585, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager copyWithZone:]', symObjAddr: 0x16E8, symBinAddr: 0xA890, symSize: 0x15C }
  - { offset: 0x5F5DF, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager baseURL]', symObjAddr: 0x1844, symBinAddr: 0xA9EC, symSize: 0x10 }
  - { offset: 0x5F616, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager setBaseURL:]', symObjAddr: 0x1854, symBinAddr: 0xA9FC, symSize: 0x14 }
  - { offset: 0x5F657, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager requestSerializer]', symObjAddr: 0x1868, symBinAddr: 0xAA10, symSize: 0x10 }
  - { offset: 0x5F68E, size: 0x8, addend: 0x0, symName: '-[AFHTTPSessionManager .cxx_destruct]', symObjAddr: 0x1878, symBinAddr: 0xAA20, symSize: 0x40 }
  - { offset: 0x5FC27, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler initWithUUID:success:failure:]', symObjAddr: 0x0, symBinAddr: 0xAA60, symSize: 0xA8 }
  - { offset: 0x5FC35, size: 0x8, addend: 0x0, symName: '+[AFImageDownloader defaultInstance]', symObjAddr: 0x950, symBinAddr: 0xB3B0, symSize: 0x74 }
  - { offset: 0x5FC5F, size: 0x8, addend: 0x0, symName: _defaultInstance.sharedInstance, symObjAddr: 0x11DA8, symBinAddr: 0x3BF78, symSize: 0x0 }
  - { offset: 0x5FC75, size: 0x8, addend: 0x0, symName: _defaultInstance.onceToken, symObjAddr: 0x11DB0, symBinAddr: 0x3BF80, symSize: 0x0 }
  - { offset: 0x60330, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler initWithUUID:success:failure:]', symObjAddr: 0x0, symBinAddr: 0xAA60, symSize: 0xA8 }
  - { offset: 0x60397, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler description]', symObjAddr: 0xA8, symBinAddr: 0xAB08, symSize: 0x84 }
  - { offset: 0x603CE, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler uuid]', symObjAddr: 0x12C, symBinAddr: 0xAB8C, symSize: 0x8 }
  - { offset: 0x60405, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler setUuid:]', symObjAddr: 0x134, symBinAddr: 0xAB94, symSize: 0xC }
  - { offset: 0x60446, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler successBlock]', symObjAddr: 0x140, symBinAddr: 0xABA0, symSize: 0x8 }
  - { offset: 0x6047D, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler setSuccessBlock:]', symObjAddr: 0x148, symBinAddr: 0xABA8, symSize: 0x8 }
  - { offset: 0x604BC, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler failureBlock]', symObjAddr: 0x150, symBinAddr: 0xABB0, symSize: 0x8 }
  - { offset: 0x604F3, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler setFailureBlock:]', symObjAddr: 0x158, symBinAddr: 0xABB8, symSize: 0x8 }
  - { offset: 0x60532, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderResponseHandler .cxx_destruct]', symObjAddr: 0x160, symBinAddr: 0xABC0, symSize: 0x3C }
  - { offset: 0x60565, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask initWithURLIdentifier:identifier:task:]', symObjAddr: 0x19C, symBinAddr: 0xABFC, symSize: 0xD0 }
  - { offset: 0x605CC, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask addResponseHandler:]', symObjAddr: 0x26C, symBinAddr: 0xACCC, symSize: 0x50 }
  - { offset: 0x6060F, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask removeResponseHandler:]', symObjAddr: 0x2BC, symBinAddr: 0xAD1C, symSize: 0x50 }
  - { offset: 0x60652, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask URLIdentifier]', symObjAddr: 0x30C, symBinAddr: 0xAD6C, symSize: 0x8 }
  - { offset: 0x60689, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask setURLIdentifier:]', symObjAddr: 0x314, symBinAddr: 0xAD74, symSize: 0xC }
  - { offset: 0x606CA, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask identifier]', symObjAddr: 0x320, symBinAddr: 0xAD80, symSize: 0x8 }
  - { offset: 0x60701, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask setIdentifier:]', symObjAddr: 0x328, symBinAddr: 0xAD88, symSize: 0xC }
  - { offset: 0x60742, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask task]', symObjAddr: 0x334, symBinAddr: 0xAD94, symSize: 0x8 }
  - { offset: 0x60779, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask setTask:]', symObjAddr: 0x33C, symBinAddr: 0xAD9C, symSize: 0xC }
  - { offset: 0x607BA, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask responseHandlers]', symObjAddr: 0x348, symBinAddr: 0xADA8, symSize: 0x8 }
  - { offset: 0x607F1, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask setResponseHandlers:]', symObjAddr: 0x350, symBinAddr: 0xADB0, symSize: 0xC }
  - { offset: 0x60832, size: 0x8, addend: 0x0, symName: '-[AFImageDownloaderMergedTask .cxx_destruct]', symObjAddr: 0x35C, symBinAddr: 0xADBC, symSize: 0x48 }
  - { offset: 0x60865, size: 0x8, addend: 0x0, symName: '-[AFImageDownloadReceipt initWithReceiptID:task:]', symObjAddr: 0x3A4, symBinAddr: 0xAE04, symSize: 0x7C }
  - { offset: 0x608BC, size: 0x8, addend: 0x0, symName: '-[AFImageDownloadReceipt task]', symObjAddr: 0x420, symBinAddr: 0xAE80, symSize: 0x8 }
  - { offset: 0x608F3, size: 0x8, addend: 0x0, symName: '-[AFImageDownloadReceipt setTask:]', symObjAddr: 0x428, symBinAddr: 0xAE88, symSize: 0xC }
  - { offset: 0x60934, size: 0x8, addend: 0x0, symName: '-[AFImageDownloadReceipt receiptID]', symObjAddr: 0x434, symBinAddr: 0xAE94, symSize: 0x8 }
  - { offset: 0x6096B, size: 0x8, addend: 0x0, symName: '-[AFImageDownloadReceipt setReceiptID:]', symObjAddr: 0x43C, symBinAddr: 0xAE9C, symSize: 0xC }
  - { offset: 0x609AC, size: 0x8, addend: 0x0, symName: '-[AFImageDownloadReceipt .cxx_destruct]', symObjAddr: 0x448, symBinAddr: 0xAEA8, symSize: 0x30 }
  - { offset: 0x609DF, size: 0x8, addend: 0x0, symName: '+[AFImageDownloader defaultURLCache]', symObjAddr: 0x478, symBinAddr: 0xAED8, symSize: 0xD4 }
  - { offset: 0x60A42, size: 0x8, addend: 0x0, symName: '+[AFImageDownloader defaultURLSessionConfiguration]', symObjAddr: 0x54C, symBinAddr: 0xAFAC, symSize: 0x9C }
  - { offset: 0x60A85, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader init]', symObjAddr: 0x5E8, symBinAddr: 0xB048, symSize: 0x4C }
  - { offset: 0x60ACC, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader initWithSessionConfiguration:]', symObjAddr: 0x634, symBinAddr: 0xB094, symSize: 0xC4 }
  - { offset: 0x60B23, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader initWithSessionManager:downloadPrioritization:maximumActiveDownloads:imageCache:]', symObjAddr: 0x6F8, symBinAddr: 0xB158, symSize: 0x258 }
  - { offset: 0x60C45, size: 0x8, addend: 0x0, symName: '___36+[AFImageDownloader defaultInstance]_block_invoke', symObjAddr: 0x9C4, symBinAddr: 0xB424, symSize: 0x2C }
  - { offset: 0x60C84, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader downloadImageForURLRequest:success:failure:]', symObjAddr: 0x9F0, symBinAddr: 0xB450, symSize: 0xBC }
  - { offset: 0x60CEB, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader downloadImageForURLRequest:withReceiptID:success:failure:]', symObjAddr: 0xAAC, symBinAddr: 0xB50C, symSize: 0x1E0 }
  - { offset: 0x60DA8, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0xC8C, symBinAddr: 0xB6EC, symSize: 0x10 }
  - { offset: 0x60DCD, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0xC9C, symBinAddr: 0xB6FC, symSize: 0x8 }
  - { offset: 0x60DEC, size: 0x8, addend: 0x0, symName: '___78-[AFImageDownloader downloadImageForURLRequest:withReceiptID:success:failure:]_block_invoke', symObjAddr: 0xCA4, symBinAddr: 0xB704, symSize: 0x494 }
  - { offset: 0x60F9A, size: 0x8, addend: 0x0, symName: '___78-[AFImageDownloader downloadImageForURLRequest:withReceiptID:success:failure:]_block_invoke_2', symObjAddr: 0x1138, symBinAddr: 0xBB98, symSize: 0x18 }
  - { offset: 0x61008, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48b, symObjAddr: 0x1150, symBinAddr: 0xBBB0, symSize: 0x3C }
  - { offset: 0x61031, size: 0x8, addend: 0x0, symName: '___78-[AFImageDownloader downloadImageForURLRequest:withReceiptID:success:failure:]_block_invoke.84', symObjAddr: 0x11BC, symBinAddr: 0xBBEC, symSize: 0x18 }
  - { offset: 0x6109F, size: 0x8, addend: 0x0, symName: '___78-[AFImageDownloader downloadImageForURLRequest:withReceiptID:success:failure:]_block_invoke_2.85', symObjAddr: 0x11D4, symBinAddr: 0xBC04, symSize: 0x164 }
  - { offset: 0x61171, size: 0x8, addend: 0x0, symName: '___78-[AFImageDownloader downloadImageForURLRequest:withReceiptID:success:failure:]_block_invoke_3', symObjAddr: 0x1338, symBinAddr: 0xBD68, symSize: 0x3D8 }
  - { offset: 0x612AD, size: 0x8, addend: 0x0, symName: '___78-[AFImageDownloader downloadImageForURLRequest:withReceiptID:success:failure:]_block_invoke_4', symObjAddr: 0x1710, symBinAddr: 0xC140, symSize: 0x44 }
  - { offset: 0x61329, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56s, symObjAddr: 0x1754, symBinAddr: 0xC184, symSize: 0x38 }
  - { offset: 0x61352, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56s, symObjAddr: 0x178C, symBinAddr: 0xC1BC, symSize: 0x38 }
  - { offset: 0x61371, size: 0x8, addend: 0x0, symName: '___78-[AFImageDownloader downloadImageForURLRequest:withReceiptID:success:failure:]_block_invoke.86', symObjAddr: 0x17C4, symBinAddr: 0xC1F4, symSize: 0x44 }
  - { offset: 0x613ED, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56s64s72s80w, symObjAddr: 0x1808, symBinAddr: 0xC238, symSize: 0x58 }
  - { offset: 0x61416, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56s64s72s80w, symObjAddr: 0x1860, symBinAddr: 0xC290, symSize: 0x50 }
  - { offset: 0x61435, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56s64w, symObjAddr: 0x18B0, symBinAddr: 0xC2E0, symSize: 0x48 }
  - { offset: 0x6145E, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56s64w, symObjAddr: 0x18F8, symBinAddr: 0xC328, symSize: 0x40 }
  - { offset: 0x6147D, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56b64b72r, symObjAddr: 0x1938, symBinAddr: 0xC368, symSize: 0x64 }
  - { offset: 0x614A6, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56s64s72r, symObjAddr: 0x199C, symBinAddr: 0xC3CC, symSize: 0x4C }
  - { offset: 0x614C5, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader cancelTaskForImageDownloadReceipt:]', symObjAddr: 0x19E8, symBinAddr: 0xC418, symSize: 0xA8 }
  - { offset: 0x61527, size: 0x8, addend: 0x0, symName: '___55-[AFImageDownloader cancelTaskForImageDownloadReceipt:]_block_invoke', symObjAddr: 0x1A90, symBinAddr: 0xC4C0, symSize: 0x3B4 }
  - { offset: 0x61625, size: 0x8, addend: 0x0, symName: '___55-[AFImageDownloader cancelTaskForImageDownloadReceipt:]_block_invoke_2', symObjAddr: 0x1E44, symBinAddr: 0xC874, symSize: 0x58 }
  - { offset: 0x61692, size: 0x8, addend: 0x0, symName: '___55-[AFImageDownloader cancelTaskForImageDownloadReceipt:]_block_invoke.94', symObjAddr: 0x1EAC, symBinAddr: 0xC8CC, symSize: 0x88 }
  - { offset: 0x61713, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s, symObjAddr: 0x1F64, symBinAddr: 0xC954, symSize: 0x28 }
  - { offset: 0x6173C, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader safelyRemoveMergedTaskWithURLIdentifier:]', symObjAddr: 0x1FB4, symBinAddr: 0xC97C, symSize: 0x11C }
  - { offset: 0x617B8, size: 0x8, addend: 0x0, symName: '___61-[AFImageDownloader safelyRemoveMergedTaskWithURLIdentifier:]_block_invoke', symObjAddr: 0x20D0, symBinAddr: 0xCA98, symSize: 0x44 }
  - { offset: 0x6181C, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader removeMergedTaskWithURLIdentifier:]', symObjAddr: 0x2184, symBinAddr: 0xCADC, symSize: 0x90 }
  - { offset: 0x61876, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader safelyDecrementActiveTaskCount]', symObjAddr: 0x2214, symBinAddr: 0xCB6C, symSize: 0x74 }
  - { offset: 0x618C0, size: 0x8, addend: 0x0, symName: '___51-[AFImageDownloader safelyDecrementActiveTaskCount]_block_invoke', symObjAddr: 0x2288, symBinAddr: 0xCBE0, symSize: 0x4C }
  - { offset: 0x61902, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader safelyStartNextTaskIfNecessary]', symObjAddr: 0x22D4, symBinAddr: 0xCC2C, symSize: 0x74 }
  - { offset: 0x6194C, size: 0x8, addend: 0x0, symName: '___51-[AFImageDownloader safelyStartNextTaskIfNecessary]_block_invoke', symObjAddr: 0x2348, symBinAddr: 0xCCA0, symSize: 0xC0 }
  - { offset: 0x619A6, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader startMergedTask:]', symObjAddr: 0x2408, symBinAddr: 0xCD60, symSize: 0x4C }
  - { offset: 0x619EB, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader enqueueMergedTask:]', symObjAddr: 0x2454, symBinAddr: 0xCDAC, symSize: 0x88 }
  - { offset: 0x61A30, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader dequeueMergedTask]', symObjAddr: 0x24DC, symBinAddr: 0xCE34, symSize: 0x74 }
  - { offset: 0x61A79, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader isActiveRequestCountBelowMaximumLimit]', symObjAddr: 0x2550, symBinAddr: 0xCEA8, symSize: 0x34 }
  - { offset: 0x61AB1, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader safelyGetMergedTask:]', symObjAddr: 0x2584, symBinAddr: 0xCEDC, symSize: 0x11C }
  - { offset: 0x61B2D, size: 0x8, addend: 0x0, symName: '___41-[AFImageDownloader safelyGetMergedTask:]_block_invoke', symObjAddr: 0x26A0, symBinAddr: 0xCFF8, symSize: 0x5C }
  - { offset: 0x61B91, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader imageCache]', symObjAddr: 0x26FC, symBinAddr: 0xD054, symSize: 0x8 }
  - { offset: 0x61BC8, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader setImageCache:]', symObjAddr: 0x2704, symBinAddr: 0xD05C, symSize: 0xC }
  - { offset: 0x61C09, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader sessionManager]', symObjAddr: 0x2710, symBinAddr: 0xD068, symSize: 0x8 }
  - { offset: 0x61C40, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader setSessionManager:]', symObjAddr: 0x2718, symBinAddr: 0xD070, symSize: 0xC }
  - { offset: 0x61C81, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader downloadPrioritization]', symObjAddr: 0x2724, symBinAddr: 0xD07C, symSize: 0x8 }
  - { offset: 0x61CB8, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader setDownloadPrioritization:]', symObjAddr: 0x272C, symBinAddr: 0xD084, symSize: 0x8 }
  - { offset: 0x61CF5, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader synchronizationQueue]', symObjAddr: 0x2734, symBinAddr: 0xD08C, symSize: 0x8 }
  - { offset: 0x61D2C, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader setSynchronizationQueue:]', symObjAddr: 0x273C, symBinAddr: 0xD094, symSize: 0xC }
  - { offset: 0x61D6D, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader responseQueue]', symObjAddr: 0x2748, symBinAddr: 0xD0A0, symSize: 0x8 }
  - { offset: 0x61DA4, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader setResponseQueue:]', symObjAddr: 0x2750, symBinAddr: 0xD0A8, symSize: 0xC }
  - { offset: 0x61DE5, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader maximumActiveDownloads]', symObjAddr: 0x275C, symBinAddr: 0xD0B4, symSize: 0x8 }
  - { offset: 0x61E1C, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader setMaximumActiveDownloads:]', symObjAddr: 0x2764, symBinAddr: 0xD0BC, symSize: 0x8 }
  - { offset: 0x61E59, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader activeRequestCount]', symObjAddr: 0x276C, symBinAddr: 0xD0C4, symSize: 0x8 }
  - { offset: 0x61E90, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader setActiveRequestCount:]', symObjAddr: 0x2774, symBinAddr: 0xD0CC, symSize: 0x8 }
  - { offset: 0x61ECD, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader queuedMergedTasks]', symObjAddr: 0x277C, symBinAddr: 0xD0D4, symSize: 0x8 }
  - { offset: 0x61F04, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader setQueuedMergedTasks:]', symObjAddr: 0x2784, symBinAddr: 0xD0DC, symSize: 0xC }
  - { offset: 0x61F45, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader mergedTasks]', symObjAddr: 0x2790, symBinAddr: 0xD0E8, symSize: 0x8 }
  - { offset: 0x61F7C, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader setMergedTasks:]', symObjAddr: 0x2798, symBinAddr: 0xD0F0, symSize: 0xC }
  - { offset: 0x61FBD, size: 0x8, addend: 0x0, symName: '-[AFImageDownloader .cxx_destruct]', symObjAddr: 0x27A4, symBinAddr: 0xD0FC, symSize: 0x60 }
  - { offset: 0x629CF, size: 0x8, addend: 0x0, symName: '+[AFNetworkActivityIndicatorManager sharedManager]', symObjAddr: 0x0, symBinAddr: 0xD15C, symSize: 0x74 }
  - { offset: 0x629DD, size: 0x8, addend: 0x0, symName: '+[AFNetworkActivityIndicatorManager sharedManager]', symObjAddr: 0x0, symBinAddr: 0xD15C, symSize: 0x74 }
  - { offset: 0x62A07, size: 0x8, addend: 0x0, symName: _sharedManager._sharedManager, symObjAddr: 0x7448, symBinAddr: 0x3BF88, symSize: 0x0 }
  - { offset: 0x62A1D, size: 0x8, addend: 0x0, symName: _sharedManager.oncePredicate, symObjAddr: 0x7450, symBinAddr: 0x3BF90, symSize: 0x0 }
  - { offset: 0x62C90, size: 0x8, addend: 0x0, symName: '___50+[AFNetworkActivityIndicatorManager sharedManager]_block_invoke', symObjAddr: 0x74, symBinAddr: 0xD1D0, symSize: 0x2C }
  - { offset: 0x62CCF, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager init]', symObjAddr: 0xA0, symBinAddr: 0xD1FC, symSize: 0x140 }
  - { offset: 0x62D06, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager dealloc]', symObjAddr: 0x1E0, symBinAddr: 0xD33C, symSize: 0x78 }
  - { offset: 0x62D39, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setEnabled:]', symObjAddr: 0x258, symBinAddr: 0xD3B4, symSize: 0x14 }
  - { offset: 0x62D76, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setNetworkingActivityActionWithBlock:]', symObjAddr: 0x26C, symBinAddr: 0xD3C8, symSize: 0x4 }
  - { offset: 0x62DB5, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager isNetworkActivityOccurring]', symObjAddr: 0x270, symBinAddr: 0xD3CC, symSize: 0x5C }
  - { offset: 0x62DEC, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setNetworkActivityIndicatorVisible:]', symObjAddr: 0x2CC, symBinAddr: 0xD428, symSize: 0xC8 }
  - { offset: 0x62E43, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager incrementActivityCount]', symObjAddr: 0x394, symBinAddr: 0xD4F0, symSize: 0xA4 }
  - { offset: 0x62E9F, size: 0x8, addend: 0x0, symName: '___59-[AFNetworkActivityIndicatorManager incrementActivityCount]_block_invoke', symObjAddr: 0x438, symBinAddr: 0xD594, symSize: 0x8 }
  - { offset: 0x62EDE, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager decrementActivityCount]', symObjAddr: 0x450, symBinAddr: 0xD59C, symSize: 0xA8 }
  - { offset: 0x62F56, size: 0x8, addend: 0x0, symName: '___59-[AFNetworkActivityIndicatorManager decrementActivityCount]_block_invoke', symObjAddr: 0x4F8, symBinAddr: 0xD644, symSize: 0x8 }
  - { offset: 0x62F95, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager networkRequestDidStart:]', symObjAddr: 0x500, symBinAddr: 0xD64C, symSize: 0x6C }
  - { offset: 0x62FEF, size: 0x8, addend: 0x0, symName: _AFNetworkRequestFromNotification, symObjAddr: 0x56C, symBinAddr: 0xD6B8, symSize: 0x94 }
  - { offset: 0x6301A, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager networkRequestDidFinish:]', symObjAddr: 0x600, symBinAddr: 0xD74C, symSize: 0x6C }
  - { offset: 0x63074, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setCurrentState:]', symObjAddr: 0x66C, symBinAddr: 0xD7B8, symSize: 0xBC }
  - { offset: 0x630B7, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager updateCurrentStateForNetworkActivityChange]', symObjAddr: 0x728, symBinAddr: 0xD874, symSize: 0x88 }
  - { offset: 0x630EA, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager startActivationDelayTimer]', symObjAddr: 0x7B0, symBinAddr: 0xD8FC, symSize: 0xB4 }
  - { offset: 0x6311D, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager activationDelayTimerFired]', symObjAddr: 0x864, symBinAddr: 0xD9B0, symSize: 0x30 }
  - { offset: 0x63150, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager startCompletionDelayTimer]', symObjAddr: 0x894, symBinAddr: 0xD9E0, symSize: 0xD4 }
  - { offset: 0x63183, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager completionDelayTimerFired]', symObjAddr: 0x968, symBinAddr: 0xDAB4, symSize: 0x8 }
  - { offset: 0x631B4, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager cancelActivationDelayTimer]', symObjAddr: 0x970, symBinAddr: 0xDABC, symSize: 0x30 }
  - { offset: 0x631E7, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager cancelCompletionDelayTimer]', symObjAddr: 0x9A0, symBinAddr: 0xDAEC, symSize: 0x30 }
  - { offset: 0x6321A, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager isEnabled]', symObjAddr: 0x9D0, symBinAddr: 0xDB1C, symSize: 0x8 }
  - { offset: 0x63251, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager isNetworkActivityIndicatorVisible]', symObjAddr: 0x9D8, symBinAddr: 0xDB24, symSize: 0x8 }
  - { offset: 0x63288, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager activationDelay]', symObjAddr: 0x9E0, symBinAddr: 0xDB2C, symSize: 0x8 }
  - { offset: 0x632BD, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setActivationDelay:]', symObjAddr: 0x9E8, symBinAddr: 0xDB34, symSize: 0x8 }
  - { offset: 0x632FB, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager completionDelay]', symObjAddr: 0x9F0, symBinAddr: 0xDB3C, symSize: 0x8 }
  - { offset: 0x63330, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setCompletionDelay:]', symObjAddr: 0x9F8, symBinAddr: 0xDB44, symSize: 0x8 }
  - { offset: 0x6336E, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager activityCount]', symObjAddr: 0xA00, symBinAddr: 0xDB4C, symSize: 0x8 }
  - { offset: 0x633A5, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setActivityCount:]', symObjAddr: 0xA08, symBinAddr: 0xDB54, symSize: 0x8 }
  - { offset: 0x633E2, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager activationDelayTimer]', symObjAddr: 0xA10, symBinAddr: 0xDB5C, symSize: 0x8 }
  - { offset: 0x63419, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setActivationDelayTimer:]', symObjAddr: 0xA18, symBinAddr: 0xDB64, symSize: 0xC }
  - { offset: 0x6345A, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager completionDelayTimer]', symObjAddr: 0xA24, symBinAddr: 0xDB70, symSize: 0x8 }
  - { offset: 0x63491, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setCompletionDelayTimer:]', symObjAddr: 0xA2C, symBinAddr: 0xDB78, symSize: 0xC }
  - { offset: 0x634D2, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager networkActivityActionBlock]', symObjAddr: 0xA38, symBinAddr: 0xDB84, symSize: 0x8 }
  - { offset: 0x63509, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager setNetworkActivityActionBlock:]', symObjAddr: 0xA40, symBinAddr: 0xDB8C, symSize: 0x8 }
  - { offset: 0x63548, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager currentState]', symObjAddr: 0xA48, symBinAddr: 0xDB94, symSize: 0x8 }
  - { offset: 0x6357F, size: 0x8, addend: 0x0, symName: '-[AFNetworkActivityIndicatorManager .cxx_destruct]', symObjAddr: 0xA50, symBinAddr: 0xDB9C, symSize: 0x3C }
  - { offset: 0x63815, size: 0x8, addend: 0x0, symName: _AFStringFromNetworkReachabilityStatus, symObjAddr: 0x0, symBinAddr: 0xDBD8, symSize: 0xD8 }
  - { offset: 0x6382F, size: 0x8, addend: 0x0, symName: _AFNetworkingReachabilityDidChangeNotification, symObjAddr: 0xD40, symBinAddr: 0x306F0, symSize: 0x0 }
  - { offset: 0x6384F, size: 0x8, addend: 0x0, symName: _AFNetworkingReachabilityNotificationStatusItem, symObjAddr: 0xD48, symBinAddr: 0x306F8, symSize: 0x0 }
  - { offset: 0x63859, size: 0x8, addend: 0x0, symName: '+[AFNetworkReachabilityManager sharedManager]', symObjAddr: 0xD8, symBinAddr: 0xDCB0, symSize: 0x74 }
  - { offset: 0x63883, size: 0x8, addend: 0x0, symName: _sharedManager._sharedManager, symObjAddr: 0x76B0, symBinAddr: 0x3BF98, symSize: 0x0 }
  - { offset: 0x63899, size: 0x8, addend: 0x0, symName: _sharedManager.onceToken, symObjAddr: 0x76B8, symBinAddr: 0x3BFA0, symSize: 0x0 }
  - { offset: 0x63AA5, size: 0x8, addend: 0x0, symName: _AFStringFromNetworkReachabilityStatus, symObjAddr: 0x0, symBinAddr: 0xDBD8, symSize: 0xD8 }
  - { offset: 0x63B10, size: 0x8, addend: 0x0, symName: '___45+[AFNetworkReachabilityManager sharedManager]_block_invoke', symObjAddr: 0x14C, symBinAddr: 0xDD24, symSize: 0x30 }
  - { offset: 0x63B4F, size: 0x8, addend: 0x0, symName: '+[AFNetworkReachabilityManager managerForDomain:]', symObjAddr: 0x17C, symBinAddr: 0xDD54, symSize: 0x64 }
  - { offset: 0x63C19, size: 0x8, addend: 0x0, symName: '+[AFNetworkReachabilityManager managerForAddress:]', symObjAddr: 0x1E0, symBinAddr: 0xDDB8, symSize: 0x54 }
  - { offset: 0x63CC4, size: 0x8, addend: 0x0, symName: '+[AFNetworkReachabilityManager manager]', symObjAddr: 0x234, symBinAddr: 0xDE0C, symSize: 0x3C }
  - { offset: 0x63D0A, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager initWithReachability:]', symObjAddr: 0x270, symBinAddr: 0xDE48, symSize: 0x74 }
  - { offset: 0x63D7A, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager init]', symObjAddr: 0x2E4, symBinAddr: 0xDEBC, symSize: 0x3C }
  - { offset: 0x63DAD, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager dealloc]', symObjAddr: 0x320, symBinAddr: 0xDEF8, symSize: 0x50 }
  - { offset: 0x63DEE, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager isReachable]', symObjAddr: 0x370, symBinAddr: 0xDF48, symSize: 0x38 }
  - { offset: 0x63E25, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager isReachableViaWWAN]', symObjAddr: 0x3A8, symBinAddr: 0xDF80, symSize: 0x1C }
  - { offset: 0x63E5C, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager isReachableViaWiFi]', symObjAddr: 0x3C4, symBinAddr: 0xDF9C, symSize: 0x1C }
  - { offset: 0x63E93, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager startMonitoring]', symObjAddr: 0x3E0, symBinAddr: 0xDFB8, symSize: 0x194 }
  - { offset: 0x63FDE, size: 0x8, addend: 0x0, symName: '___47-[AFNetworkReachabilityManager startMonitoring]_block_invoke', symObjAddr: 0x574, symBinAddr: 0xE14C, symSize: 0x80 }
  - { offset: 0x64060, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32w, symObjAddr: 0x5F4, symBinAddr: 0xE1CC, symSize: 0xC }
  - { offset: 0x64089, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32w, symObjAddr: 0x600, symBinAddr: 0xE1D8, symSize: 0x8 }
  - { offset: 0x640A8, size: 0x8, addend: 0x0, symName: _AFNetworkReachabilityRetainCallback, symObjAddr: 0x608, symBinAddr: 0xE1E0, symSize: 0x4 }
  - { offset: 0x640D4, size: 0x8, addend: 0x0, symName: _AFNetworkReachabilityRetainCallback, symObjAddr: 0x608, symBinAddr: 0xE1E0, symSize: 0x4 }
  - { offset: 0x640FA, size: 0x8, addend: 0x0, symName: _AFNetworkReachabilityReleaseCallback, symObjAddr: 0x60C, symBinAddr: 0xE1E4, symSize: 0xC }
  - { offset: 0x6413B, size: 0x8, addend: 0x0, symName: _AFNetworkReachabilityCallback, symObjAddr: 0x618, symBinAddr: 0xE1F0, symSize: 0xC }
  - { offset: 0x6419D, size: 0x8, addend: 0x0, symName: _AFPostReachabilityStatusChange, symObjAddr: 0x668, symBinAddr: 0xE240, symSize: 0xB8 }
  - { offset: 0x64240, size: 0x8, addend: 0x0, symName: '___47-[AFNetworkReachabilityManager startMonitoring]_block_invoke.22', symObjAddr: 0x624, symBinAddr: 0xE1FC, symSize: 0x44 }
  - { offset: 0x64351, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager stopMonitoring]', symObjAddr: 0x77C, symBinAddr: 0xE2F8, symSize: 0x54 }
  - { offset: 0x643BD, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager localizedNetworkReachabilityStatusString]', symObjAddr: 0x7D0, symBinAddr: 0xE34C, symSize: 0x14 }
  - { offset: 0x64402, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager setReachabilityStatusChangeBlock:]', symObjAddr: 0x7E4, symBinAddr: 0xE360, symSize: 0x4 }
  - { offset: 0x64441, size: 0x8, addend: 0x0, symName: '+[AFNetworkReachabilityManager keyPathsForValuesAffectingValueForKey:]', symObjAddr: 0x7E8, symBinAddr: 0xE364, symSize: 0xC0 }
  - { offset: 0x6448A, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager networkReachabilityStatus]', symObjAddr: 0x8A8, symBinAddr: 0xE424, symSize: 0x8 }
  - { offset: 0x644C1, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager setNetworkReachabilityStatus:]', symObjAddr: 0x8B0, symBinAddr: 0xE42C, symSize: 0x8 }
  - { offset: 0x644FE, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager networkReachability]', symObjAddr: 0x8B8, symBinAddr: 0xE434, symSize: 0x8 }
  - { offset: 0x64535, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager networkReachabilityStatusBlock]', symObjAddr: 0x8C0, symBinAddr: 0xE43C, symSize: 0x8 }
  - { offset: 0x6456C, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager setNetworkReachabilityStatusBlock:]', symObjAddr: 0x8C8, symBinAddr: 0xE444, symSize: 0x8 }
  - { offset: 0x645AB, size: 0x8, addend: 0x0, symName: '-[AFNetworkReachabilityManager .cxx_destruct]', symObjAddr: 0x8D0, symBinAddr: 0xE44C, symSize: 0xC }
  - { offset: 0x645DE, size: 0x8, addend: 0x0, symName: ___AFPostReachabilityStatusChange_block_invoke, symObjAddr: 0x8DC, symBinAddr: 0xE458, symSize: 0x120 }
  - { offset: 0x6492C, size: 0x8, addend: 0x0, symName: '+[AFSecurityPolicy certificatesInBundle:]', symObjAddr: 0x0, symBinAddr: 0xE578, symSize: 0x194 }
  - { offset: 0x64A43, size: 0x8, addend: 0x0, symName: '+[AFSecurityPolicy certificatesInBundle:]', symObjAddr: 0x0, symBinAddr: 0xE578, symSize: 0x194 }
  - { offset: 0x64ADC, size: 0x8, addend: 0x0, symName: '+[AFSecurityPolicy defaultPolicy]', symObjAddr: 0x194, symBinAddr: 0xE70C, symSize: 0x30 }
  - { offset: 0x64B23, size: 0x8, addend: 0x0, symName: '+[AFSecurityPolicy policyWithPinningMode:]', symObjAddr: 0x1C4, symBinAddr: 0xE73C, symSize: 0x88 }
  - { offset: 0x64B7A, size: 0x8, addend: 0x0, symName: '+[AFSecurityPolicy policyWithPinningMode:withPinnedCertificates:]', symObjAddr: 0x24C, symBinAddr: 0xE7C4, symSize: 0x64 }
  - { offset: 0x64BE1, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy init]', symObjAddr: 0x2B0, symBinAddr: 0xE828, symSize: 0x64 }
  - { offset: 0x64C76, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy setPinnedCertificates:]', symObjAddr: 0x314, symBinAddr: 0xE88C, symSize: 0x268 }
  - { offset: 0x64FF7, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy evaluateServerTrust:forDomain:]', symObjAddr: 0x57C, symBinAddr: 0xEAF4, symSize: 0x6A8 }
  - { offset: 0x655C9, size: 0x8, addend: 0x0, symName: '+[AFSecurityPolicy keyPathsForValuesAffectingPinnedPublicKeys]', symObjAddr: 0xC24, symBinAddr: 0xF19C, symSize: 0x14 }
  - { offset: 0x655FD, size: 0x8, addend: 0x0, symName: '+[AFSecurityPolicy supportsSecureCoding]', symObjAddr: 0xC38, symBinAddr: 0xF1B0, symSize: 0x8 }
  - { offset: 0x65631, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy initWithCoder:]', symObjAddr: 0xC40, symBinAddr: 0xF1B8, symSize: 0x190 }
  - { offset: 0x656D8, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy encodeWithCoder:]', symObjAddr: 0xDD0, symBinAddr: 0xF348, symSize: 0x158 }
  - { offset: 0x65755, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy copyWithZone:]', symObjAddr: 0xF28, symBinAddr: 0xF4A0, symSize: 0xB8 }
  - { offset: 0x657AF, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy SSLPinningMode]', symObjAddr: 0xFE0, symBinAddr: 0xF558, symSize: 0x8 }
  - { offset: 0x657E6, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy setSSLPinningMode:]', symObjAddr: 0xFE8, symBinAddr: 0xF560, symSize: 0x8 }
  - { offset: 0x65823, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy pinnedCertificates]', symObjAddr: 0xFF0, symBinAddr: 0xF568, symSize: 0x8 }
  - { offset: 0x6585A, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy allowInvalidCertificates]', symObjAddr: 0xFF8, symBinAddr: 0xF570, symSize: 0x8 }
  - { offset: 0x65891, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy setAllowInvalidCertificates:]', symObjAddr: 0x1000, symBinAddr: 0xF578, symSize: 0x8 }
  - { offset: 0x658CC, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy validatesDomainName]', symObjAddr: 0x1008, symBinAddr: 0xF580, symSize: 0x8 }
  - { offset: 0x65903, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy setValidatesDomainName:]', symObjAddr: 0x1010, symBinAddr: 0xF588, symSize: 0x8 }
  - { offset: 0x6593E, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy pinnedPublicKeys]', symObjAddr: 0x1018, symBinAddr: 0xF590, symSize: 0x8 }
  - { offset: 0x65975, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy setPinnedPublicKeys:]', symObjAddr: 0x1020, symBinAddr: 0xF598, symSize: 0xC }
  - { offset: 0x659B6, size: 0x8, addend: 0x0, symName: '-[AFSecurityPolicy .cxx_destruct]', symObjAddr: 0x102C, symBinAddr: 0xF5A4, symSize: 0x30 }
  - { offset: 0x65A70, size: 0x8, addend: 0x0, symName: _AFPercentEscapedStringFromString, symObjAddr: 0x0, symBinAddr: 0xF5D4, symSize: 0x150 }
  - { offset: 0x65A8A, size: 0x8, addend: 0x0, symName: _AFURLRequestSerializationErrorDomain, symObjAddr: 0x6D40, symBinAddr: 0x30760, symSize: 0x0 }
  - { offset: 0x65AAA, size: 0x8, addend: 0x0, symName: _AFNetworkingOperationFailingURLRequestErrorKey, symObjAddr: 0x6D48, symBinAddr: 0x30768, symSize: 0x0 }
  - { offset: 0x65AB4, size: 0x8, addend: 0x0, symName: _AFPercentEscapedStringFromString, symObjAddr: 0x0, symBinAddr: 0xF5D4, symSize: 0x150 }
  - { offset: 0x65BD8, size: 0x8, addend: 0x0, symName: _kAFUploadStream3GSuggestedPacketSize, symObjAddr: 0xB5F0, symBinAddr: 0x25D68, symSize: 0x0 }
  - { offset: 0x65BEF, size: 0x8, addend: 0x0, symName: _kAFUploadStream3GSuggestedDelay, symObjAddr: 0xB5F8, symBinAddr: 0x25D70, symSize: 0x0 }
  - { offset: 0x65C05, size: 0x8, addend: 0x0, symName: _AFHTTPRequestSerializerObservedKeyPaths, symObjAddr: 0x61EC, symBinAddr: 0x15630, symSize: 0x40 }
  - { offset: 0x65C2B, size: 0x8, addend: 0x0, symName: _AFHTTPRequestSerializerObservedKeyPaths._AFHTTPRequestSerializerObservedKeyPaths, symObjAddr: 0x22D10, symBinAddr: 0x3BFA8, symSize: 0x0 }
  - { offset: 0x65C41, size: 0x8, addend: 0x0, symName: _AFHTTPRequestSerializerObservedKeyPaths.onceToken, symObjAddr: 0x22D18, symBinAddr: 0x3BFB0, symSize: 0x0 }
  - { offset: 0x65C7F, size: 0x8, addend: 0x0, symName: _AFHTTPRequestSerializerObserverContext, symObjAddr: 0xB310, symBinAddr: 0x3BC60, symSize: 0x0 }
  - { offset: 0x66316, size: 0x8, addend: 0x0, symName: '-[AFQueryStringPair initWithField:value:]', symObjAddr: 0x150, symBinAddr: 0xF724, symSize: 0xA8 }
  - { offset: 0x6636D, size: 0x8, addend: 0x0, symName: '-[AFQueryStringPair URLEncodedStringValue]', symObjAddr: 0x1F8, symBinAddr: 0xF7CC, symSize: 0x1A4 }
  - { offset: 0x663CE, size: 0x8, addend: 0x0, symName: '-[AFQueryStringPair field]', symObjAddr: 0x39C, symBinAddr: 0xF970, symSize: 0x8 }
  - { offset: 0x66405, size: 0x8, addend: 0x0, symName: '-[AFQueryStringPair setField:]', symObjAddr: 0x3A4, symBinAddr: 0xF978, symSize: 0xC }
  - { offset: 0x66446, size: 0x8, addend: 0x0, symName: '-[AFQueryStringPair value]', symObjAddr: 0x3B0, symBinAddr: 0xF984, symSize: 0x8 }
  - { offset: 0x6647D, size: 0x8, addend: 0x0, symName: '-[AFQueryStringPair setValue:]', symObjAddr: 0x3B8, symBinAddr: 0xF98C, symSize: 0xC }
  - { offset: 0x664BE, size: 0x8, addend: 0x0, symName: '-[AFQueryStringPair .cxx_destruct]', symObjAddr: 0x3C4, symBinAddr: 0xF998, symSize: 0x30 }
  - { offset: 0x6650B, size: 0x8, addend: 0x0, symName: _AFQueryStringFromParameters, symObjAddr: 0x3F4, symBinAddr: 0xF9C8, symSize: 0x170 }
  - { offset: 0x6658E, size: 0x8, addend: 0x0, symName: _AFQueryStringPairsFromKeyAndValue, symObjAddr: 0x570, symBinAddr: 0xFB44, symSize: 0x4D8 }
  - { offset: 0x666E7, size: 0x8, addend: 0x0, symName: _AFQueryStringPairsFromDictionary, symObjAddr: 0x564, symBinAddr: 0xFB38, symSize: 0xC }
  - { offset: 0x66723, size: 0x8, addend: 0x0, symName: '+[AFHTTPRequestSerializer serializer]', symObjAddr: 0xA48, symBinAddr: 0x1001C, symSize: 0x18 }
  - { offset: 0x6675A, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer init]', symObjAddr: 0xA60, symBinAddr: 0x10034, symSize: 0x5C0 }
  - { offset: 0x668AA, size: 0x8, addend: 0x0, symName: '___31-[AFHTTPRequestSerializer init]_block_invoke', symObjAddr: 0x1020, symBinAddr: 0x105F4, symSize: 0x9C }
  - { offset: 0x66934, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer dealloc]', symObjAddr: 0x10CC, symBinAddr: 0x10690, symSize: 0x144 }
  - { offset: 0x669A2, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setAllowsCellularAccess:]', symObjAddr: 0x1210, symBinAddr: 0x107D4, symSize: 0x80 }
  - { offset: 0x66A22, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setCachePolicy:]', symObjAddr: 0x1290, symBinAddr: 0x10854, symSize: 0x80 }
  - { offset: 0x66A93, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setHTTPShouldHandleCookies:]', symObjAddr: 0x1310, symBinAddr: 0x108D4, symSize: 0x80 }
  - { offset: 0x66B00, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setHTTPShouldUsePipelining:]', symObjAddr: 0x1390, symBinAddr: 0x10954, symSize: 0x80 }
  - { offset: 0x66B6D, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setNetworkServiceType:]', symObjAddr: 0x1410, symBinAddr: 0x109D4, symSize: 0x80 }
  - { offset: 0x66BDE, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setTimeoutInterval:]', symObjAddr: 0x1490, symBinAddr: 0x10A54, symSize: 0x88 }
  - { offset: 0x66C4F, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer HTTPRequestHeaders]', symObjAddr: 0x1518, symBinAddr: 0x10ADC, symSize: 0xE8 }
  - { offset: 0x66CCC, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x1600, symBinAddr: 0x10BC4, symSize: 0x10 }
  - { offset: 0x66CF1, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x1610, symBinAddr: 0x10BD4, symSize: 0x8 }
  - { offset: 0x66D10, size: 0x8, addend: 0x0, symName: '___45-[AFHTTPRequestSerializer HTTPRequestHeaders]_block_invoke', symObjAddr: 0x1618, symBinAddr: 0x10BDC, symSize: 0x70 }
  - { offset: 0x66D63, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setValue:forHTTPHeaderField:]', symObjAddr: 0x16E8, symBinAddr: 0x10C4C, symSize: 0xD8 }
  - { offset: 0x66DEA, size: 0x8, addend: 0x0, symName: '___55-[AFHTTPRequestSerializer setValue:forHTTPHeaderField:]_block_invoke', symObjAddr: 0x17C0, symBinAddr: 0x10D24, symSize: 0x3C }
  - { offset: 0x66E4E, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer valueForHTTPHeaderField:]', symObjAddr: 0x185C, symBinAddr: 0x10D60, symSize: 0x11C }
  - { offset: 0x66ECA, size: 0x8, addend: 0x0, symName: '___51-[AFHTTPRequestSerializer valueForHTTPHeaderField:]_block_invoke', symObjAddr: 0x1978, symBinAddr: 0x10E7C, symSize: 0x5C }
  - { offset: 0x66F2E, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setAuthorizationHeaderFieldWithUsername:password:]', symObjAddr: 0x1A44, symBinAddr: 0x10ED8, symSize: 0xCC }
  - { offset: 0x66FA6, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer clearAuthorizationHeader]', symObjAddr: 0x1B10, symBinAddr: 0x10FA4, symSize: 0x74 }
  - { offset: 0x66FF0, size: 0x8, addend: 0x0, symName: '___51-[AFHTTPRequestSerializer clearAuthorizationHeader]_block_invoke', symObjAddr: 0x1B84, symBinAddr: 0x11018, symSize: 0x3C }
  - { offset: 0x67032, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setQueryStringSerializationWithStyle:]', symObjAddr: 0x1BC0, symBinAddr: 0x11054, symSize: 0x28 }
  - { offset: 0x67077, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setQueryStringSerializationWithBlock:]', symObjAddr: 0x1BE8, symBinAddr: 0x1107C, symSize: 0x4 }
  - { offset: 0x670B8, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer requestWithMethod:URLString:parameters:error:]', symObjAddr: 0x1BEC, symBinAddr: 0x11080, symSize: 0x214 }
  - { offset: 0x6717C, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer multipartFormRequestWithMethod:URLString:parameters:constructingBodyWithBlock:error:]', symObjAddr: 0x1E00, symBinAddr: 0x11294, symSize: 0x324 }
  - { offset: 0x672A8, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer requestWithMultipartFormRequest:writingStreamContentsToFile:completionHandler:]', symObjAddr: 0x2124, symBinAddr: 0x115B8, symSize: 0x1D0 }
  - { offset: 0x673BB, size: 0x8, addend: 0x0, symName: '___105-[AFHTTPRequestSerializer requestWithMultipartFormRequest:writingStreamContentsToFile:completionHandler:]_block_invoke', symObjAddr: 0x22F4, symBinAddr: 0x11788, symSize: 0x214 }
  - { offset: 0x6747B, size: 0x8, addend: 0x0, symName: '___105-[AFHTTPRequestSerializer requestWithMultipartFormRequest:writingStreamContentsToFile:completionHandler:]_block_invoke_2', symObjAddr: 0x2508, symBinAddr: 0x1199C, symSize: 0x18 }
  - { offset: 0x674D6, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32b40r, symObjAddr: 0x2520, symBinAddr: 0x119B4, symSize: 0x3C }
  - { offset: 0x674FF, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48b56r, symObjAddr: 0x255C, symBinAddr: 0x119F0, symSize: 0x4C }
  - { offset: 0x67528, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56r, symObjAddr: 0x25A8, symBinAddr: 0x11A3C, symSize: 0x3C }
  - { offset: 0x67547, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer requestBySerializingRequest:withParameters:error:]', symObjAddr: 0x25E4, symBinAddr: 0x11A78, symSize: 0x39C }
  - { offset: 0x67628, size: 0x8, addend: 0x0, symName: '___76-[AFHTTPRequestSerializer requestBySerializingRequest:withParameters:error:]_block_invoke', symObjAddr: 0x2980, symBinAddr: 0x11E14, symSize: 0x7C }
  - { offset: 0x676A6, size: 0x8, addend: 0x0, symName: '+[AFHTTPRequestSerializer automaticallyNotifiesObserversForKey:]', symObjAddr: 0x2A4C, symBinAddr: 0x11E90, symSize: 0x98 }
  - { offset: 0x676FD, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer observeValueForKeyPath:ofObject:change:context:]', symObjAddr: 0x2AE4, symBinAddr: 0x11F28, symSize: 0xDC }
  - { offset: 0x67771, size: 0x8, addend: 0x0, symName: '+[AFHTTPRequestSerializer supportsSecureCoding]', symObjAddr: 0x2BC0, symBinAddr: 0x12004, symSize: 0x8 }
  - { offset: 0x677A5, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer initWithCoder:]', symObjAddr: 0x2BC8, symBinAddr: 0x1200C, symSize: 0x138 }
  - { offset: 0x6780A, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer encodeWithCoder:]', symObjAddr: 0x2D00, symBinAddr: 0x12144, symSize: 0x108 }
  - { offset: 0x6787A, size: 0x8, addend: 0x0, symName: '___43-[AFHTTPRequestSerializer encodeWithCoder:]_block_invoke', symObjAddr: 0x2E08, symBinAddr: 0x1224C, symSize: 0x68 }
  - { offset: 0x678DB, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer copyWithZone:]', symObjAddr: 0x2E70, symBinAddr: 0x122B4, symSize: 0x108 }
  - { offset: 0x67952, size: 0x8, addend: 0x0, symName: '___40-[AFHTTPRequestSerializer copyWithZone:]_block_invoke', symObjAddr: 0x2F78, symBinAddr: 0x123BC, symSize: 0x5C }
  - { offset: 0x679B6, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer stringEncoding]', symObjAddr: 0x2FD4, symBinAddr: 0x12418, symSize: 0x8 }
  - { offset: 0x679ED, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setStringEncoding:]', symObjAddr: 0x2FDC, symBinAddr: 0x12420, symSize: 0x8 }
  - { offset: 0x67A2A, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer allowsCellularAccess]', symObjAddr: 0x2FE4, symBinAddr: 0x12428, symSize: 0x8 }
  - { offset: 0x67A61, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer cachePolicy]', symObjAddr: 0x2FEC, symBinAddr: 0x12430, symSize: 0x8 }
  - { offset: 0x67A98, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer HTTPShouldHandleCookies]', symObjAddr: 0x2FF4, symBinAddr: 0x12438, symSize: 0x8 }
  - { offset: 0x67ACF, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer HTTPShouldUsePipelining]', symObjAddr: 0x2FFC, symBinAddr: 0x12440, symSize: 0x8 }
  - { offset: 0x67B06, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer networkServiceType]', symObjAddr: 0x3004, symBinAddr: 0x12448, symSize: 0x8 }
  - { offset: 0x67B3D, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer timeoutInterval]', symObjAddr: 0x300C, symBinAddr: 0x12450, symSize: 0x8 }
  - { offset: 0x67B72, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer HTTPMethodsEncodingParametersInURI]', symObjAddr: 0x3014, symBinAddr: 0x12458, symSize: 0x8 }
  - { offset: 0x67BA9, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setHTTPMethodsEncodingParametersInURI:]', symObjAddr: 0x301C, symBinAddr: 0x12460, symSize: 0xC }
  - { offset: 0x67BEA, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer mutableObservedChangedKeyPaths]', symObjAddr: 0x3028, symBinAddr: 0x1246C, symSize: 0x8 }
  - { offset: 0x67C21, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setMutableObservedChangedKeyPaths:]', symObjAddr: 0x3030, symBinAddr: 0x12474, symSize: 0xC }
  - { offset: 0x67C62, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer mutableHTTPRequestHeaders]', symObjAddr: 0x303C, symBinAddr: 0x12480, symSize: 0x8 }
  - { offset: 0x67C99, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setMutableHTTPRequestHeaders:]', symObjAddr: 0x3044, symBinAddr: 0x12488, symSize: 0xC }
  - { offset: 0x67CDA, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer requestHeaderModificationQueue]', symObjAddr: 0x3050, symBinAddr: 0x12494, symSize: 0x8 }
  - { offset: 0x67D11, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setRequestHeaderModificationQueue:]', symObjAddr: 0x3058, symBinAddr: 0x1249C, symSize: 0xC }
  - { offset: 0x67D52, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer queryStringSerializationStyle]', symObjAddr: 0x3064, symBinAddr: 0x124A8, symSize: 0x8 }
  - { offset: 0x67D89, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setQueryStringSerializationStyle:]', symObjAddr: 0x306C, symBinAddr: 0x124B0, symSize: 0x8 }
  - { offset: 0x67DC6, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer queryStringSerialization]', symObjAddr: 0x3074, symBinAddr: 0x124B8, symSize: 0x8 }
  - { offset: 0x67DFD, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer setQueryStringSerialization:]', symObjAddr: 0x307C, symBinAddr: 0x124C0, symSize: 0x8 }
  - { offset: 0x67E3C, size: 0x8, addend: 0x0, symName: '-[AFHTTPRequestSerializer .cxx_destruct]', symObjAddr: 0x3084, symBinAddr: 0x124C8, symSize: 0x54 }
  - { offset: 0x67E7D, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData initWithURLRequest:stringEncoding:]', symObjAddr: 0x30D8, symBinAddr: 0x1251C, symSize: 0x110 }
  - { offset: 0x67F16, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData setRequest:]', symObjAddr: 0x31E8, symBinAddr: 0x1262C, symSize: 0x30 }
  - { offset: 0x67F91, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData appendPartWithFileURL:name:error:]', symObjAddr: 0x3218, symBinAddr: 0x1265C, symSize: 0x12C }
  - { offset: 0x680B0, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData appendPartWithFileURL:name:fileName:mimeType:error:]', symObjAddr: 0x3344, symBinAddr: 0x12788, symSize: 0x3C8 }
  - { offset: 0x681A8, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData appendPartWithInputStream:name:fileName:length:mimeType:]', symObjAddr: 0x370C, symBinAddr: 0x12B50, symSize: 0x1B8 }
  - { offset: 0x68253, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData appendPartWithFileData:name:fileName:mimeType:]', symObjAddr: 0x38C4, symBinAddr: 0x12D08, symSize: 0x11C }
  - { offset: 0x682DC, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData appendPartWithFormData:name:]', symObjAddr: 0x39E0, symBinAddr: 0x12E24, symSize: 0xD0 }
  - { offset: 0x68343, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData appendPartWithHeaders:body:]', symObjAddr: 0x3AB0, symBinAddr: 0x12EF4, symSize: 0x100 }
  - { offset: 0x683AA, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData throttleBandwidthWithPacketSize:delay:]', symObjAddr: 0x3BB0, symBinAddr: 0x12FF4, symSize: 0x74 }
  - { offset: 0x68400, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData requestByFinalizingMultipartFormData]', symObjAddr: 0x3C24, symBinAddr: 0x13068, symSize: 0x1C4 }
  - { offset: 0x68438, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData request]', symObjAddr: 0x3DE8, symBinAddr: 0x1322C, symSize: 0x8 }
  - { offset: 0x68470, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData stringEncoding]', symObjAddr: 0x3DF0, symBinAddr: 0x13234, symSize: 0x8 }
  - { offset: 0x684A8, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData setStringEncoding:]', symObjAddr: 0x3DF8, symBinAddr: 0x1323C, symSize: 0x8 }
  - { offset: 0x684E6, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData boundary]', symObjAddr: 0x3E00, symBinAddr: 0x13244, symSize: 0x8 }
  - { offset: 0x6851E, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData setBoundary:]', symObjAddr: 0x3E08, symBinAddr: 0x1324C, symSize: 0x8 }
  - { offset: 0x6855E, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData bodyStream]', symObjAddr: 0x3E10, symBinAddr: 0x13254, symSize: 0x8 }
  - { offset: 0x68596, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData setBodyStream:]', symObjAddr: 0x3E18, symBinAddr: 0x1325C, symSize: 0xC }
  - { offset: 0x685D8, size: 0x8, addend: 0x0, symName: '-[AFStreamingMultipartFormData .cxx_destruct]', symObjAddr: 0x3E24, symBinAddr: 0x13268, symSize: 0x3C }
  - { offset: 0x6860C, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream initWithStringEncoding:]', symObjAddr: 0x3E60, symBinAddr: 0x132A4, symSize: 0xA0 }
  - { offset: 0x68655, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setInitialAndFinalBoundaries]', symObjAddr: 0x3F00, symBinAddr: 0x13344, symSize: 0x1B0 }
  - { offset: 0x686A1, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream appendHTTPBodyPart:]', symObjAddr: 0x40B0, symBinAddr: 0x134F4, symSize: 0x50 }
  - { offset: 0x686E6, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream isEmpty]', symObjAddr: 0x4100, symBinAddr: 0x13544, symSize: 0x40 }
  - { offset: 0x6871E, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream read:maxLength:]', symObjAddr: 0x4140, symBinAddr: 0x13584, symSize: 0x1E4 }
  - { offset: 0x6880A, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream getBuffer:length:]', symObjAddr: 0x4324, symBinAddr: 0x13768, symSize: 0x8 }
  - { offset: 0x68858, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream hasBytesAvailable]', symObjAddr: 0x432C, symBinAddr: 0x13770, symSize: 0x1C }
  - { offset: 0x68890, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream open]', symObjAddr: 0x4348, symBinAddr: 0x1378C, symSize: 0x90 }
  - { offset: 0x688C4, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream close]', symObjAddr: 0x43D8, symBinAddr: 0x1381C, symSize: 0x8 }
  - { offset: 0x688F6, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream propertyForKey:]', symObjAddr: 0x43E0, symBinAddr: 0x13824, symSize: 0x8 }
  - { offset: 0x68937, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setProperty:forKey:]', symObjAddr: 0x43E8, symBinAddr: 0x1382C, symSize: 0x8 }
  - { offset: 0x68985, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream scheduleInRunLoop:forMode:]', symObjAddr: 0x43F0, symBinAddr: 0x13834, symSize: 0x4 }
  - { offset: 0x689CF, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream removeFromRunLoop:forMode:]', symObjAddr: 0x43F4, symBinAddr: 0x13838, symSize: 0x4 }
  - { offset: 0x68A19, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream contentLength]', symObjAddr: 0x43F8, symBinAddr: 0x1383C, symSize: 0x104 }
  - { offset: 0x68A7A, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream _scheduleInCFRunLoop:forMode:]', symObjAddr: 0x44FC, symBinAddr: 0x13940, symSize: 0x4 }
  - { offset: 0x68AC4, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream _unscheduleFromCFRunLoop:forMode:]', symObjAddr: 0x4500, symBinAddr: 0x13944, symSize: 0x4 }
  - { offset: 0x68B0E, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream _setCFClientFlags:callback:context:]', symObjAddr: 0x4504, symBinAddr: 0x13948, symSize: 0x8 }
  - { offset: 0x68B69, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream copyWithZone:]', symObjAddr: 0x450C, symBinAddr: 0x13950, symSize: 0x150 }
  - { offset: 0x68BDB, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream delegate]', symObjAddr: 0x465C, symBinAddr: 0x13AA0, symSize: 0x10 }
  - { offset: 0x68C13, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setDelegate:]', symObjAddr: 0x466C, symBinAddr: 0x13AB0, symSize: 0x10 }
  - { offset: 0x68C51, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream streamStatus]', symObjAddr: 0x467C, symBinAddr: 0x13AC0, symSize: 0x10 }
  - { offset: 0x68C89, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setStreamStatus:]', symObjAddr: 0x468C, symBinAddr: 0x13AD0, symSize: 0x10 }
  - { offset: 0x68CC7, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream streamError]', symObjAddr: 0x469C, symBinAddr: 0x13AE0, symSize: 0x10 }
  - { offset: 0x68CFF, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setStreamError:]', symObjAddr: 0x46AC, symBinAddr: 0x13AF0, symSize: 0xC }
  - { offset: 0x68D3F, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream numberOfBytesInPacket]', symObjAddr: 0x46B8, symBinAddr: 0x13AFC, symSize: 0x10 }
  - { offset: 0x68D77, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setNumberOfBytesInPacket:]', symObjAddr: 0x46C8, symBinAddr: 0x13B0C, symSize: 0x10 }
  - { offset: 0x68DB5, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream delay]', symObjAddr: 0x46D8, symBinAddr: 0x13B1C, symSize: 0x10 }
  - { offset: 0x68DEB, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setDelay:]', symObjAddr: 0x46E8, symBinAddr: 0x13B2C, symSize: 0x10 }
  - { offset: 0x68E2A, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream inputStream]', symObjAddr: 0x46F8, symBinAddr: 0x13B3C, symSize: 0x10 }
  - { offset: 0x68E62, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setInputStream:]', symObjAddr: 0x4708, symBinAddr: 0x13B4C, symSize: 0x14 }
  - { offset: 0x68EA4, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream stringEncoding]', symObjAddr: 0x471C, symBinAddr: 0x13B60, symSize: 0x10 }
  - { offset: 0x68EDC, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setStringEncoding:]', symObjAddr: 0x472C, symBinAddr: 0x13B70, symSize: 0x10 }
  - { offset: 0x68F1A, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream HTTPBodyParts]', symObjAddr: 0x473C, symBinAddr: 0x13B80, symSize: 0x10 }
  - { offset: 0x68F52, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setHTTPBodyParts:]', symObjAddr: 0x474C, symBinAddr: 0x13B90, symSize: 0x14 }
  - { offset: 0x68F94, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream HTTPBodyPartEnumerator]', symObjAddr: 0x4760, symBinAddr: 0x13BA4, symSize: 0x10 }
  - { offset: 0x68FCC, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setHTTPBodyPartEnumerator:]', symObjAddr: 0x4770, symBinAddr: 0x13BB4, symSize: 0x14 }
  - { offset: 0x6900E, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream currentHTTPBodyPart]', symObjAddr: 0x4784, symBinAddr: 0x13BC8, symSize: 0x10 }
  - { offset: 0x69046, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setCurrentHTTPBodyPart:]', symObjAddr: 0x4794, symBinAddr: 0x13BD8, symSize: 0x14 }
  - { offset: 0x69088, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream outputStream]', symObjAddr: 0x47A8, symBinAddr: 0x13BEC, symSize: 0x10 }
  - { offset: 0x690C0, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setOutputStream:]', symObjAddr: 0x47B8, symBinAddr: 0x13BFC, symSize: 0x14 }
  - { offset: 0x69102, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream buffer]', symObjAddr: 0x47CC, symBinAddr: 0x13C10, symSize: 0x10 }
  - { offset: 0x6913A, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream setBuffer:]', symObjAddr: 0x47DC, symBinAddr: 0x13C20, symSize: 0x14 }
  - { offset: 0x6917C, size: 0x8, addend: 0x0, symName: '-[AFMultipartBodyStream .cxx_destruct]', symObjAddr: 0x47F0, symBinAddr: 0x13C34, symSize: 0xA4 }
  - { offset: 0x691B0, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart init]', symObjAddr: 0x4894, symBinAddr: 0x13CD8, symSize: 0x60 }
  - { offset: 0x691E8, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart dealloc]', symObjAddr: 0x48F4, symBinAddr: 0x13D38, symSize: 0x58 }
  - { offset: 0x6921C, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart inputStream]', symObjAddr: 0x494C, symBinAddr: 0x13D90, symSize: 0x19C }
  - { offset: 0x69254, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart stringForHeaders]', symObjAddr: 0x4AE8, symBinAddr: 0x13F2C, symSize: 0x1F8 }
  - { offset: 0x69309, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart contentLength]', symObjAddr: 0x4CE0, symBinAddr: 0x14124, symSize: 0x1EC }
  - { offset: 0x693E2, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart hasBytesAvailable]', symObjAddr: 0x4ECC, symBinAddr: 0x14310, symSize: 0x54 }
  - { offset: 0x6941A, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart read:maxLength:]', symObjAddr: 0x4F20, symBinAddr: 0x14364, symSize: 0x2C0 }
  - { offset: 0x69553, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart readData:intoBuffer:maxLength:]', symObjAddr: 0x51E0, symBinAddr: 0x14624, symSize: 0xA0 }
  - { offset: 0x69600, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart transitionToNextPhase]', symObjAddr: 0x5280, symBinAddr: 0x146C4, symSize: 0x174 }
  - { offset: 0x6964E, size: 0x8, addend: 0x0, symName: '___39-[AFHTTPBodyPart transitionToNextPhase]_block_invoke', symObjAddr: 0x53F4, symBinAddr: 0x14838, symSize: 0x8 }
  - { offset: 0x69690, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart copyWithZone:]', symObjAddr: 0x53FC, symBinAddr: 0x14840, symSize: 0xE0 }
  - { offset: 0x696EA, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart stringEncoding]', symObjAddr: 0x54DC, symBinAddr: 0x14920, symSize: 0x8 }
  - { offset: 0x69722, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart setStringEncoding:]', symObjAddr: 0x54E4, symBinAddr: 0x14928, symSize: 0x8 }
  - { offset: 0x69760, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart headers]', symObjAddr: 0x54EC, symBinAddr: 0x14930, symSize: 0x8 }
  - { offset: 0x69798, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart setHeaders:]', symObjAddr: 0x54F4, symBinAddr: 0x14938, symSize: 0xC }
  - { offset: 0x697DA, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart boundary]', symObjAddr: 0x5500, symBinAddr: 0x14944, symSize: 0x8 }
  - { offset: 0x69812, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart setBoundary:]', symObjAddr: 0x5508, symBinAddr: 0x1494C, symSize: 0x8 }
  - { offset: 0x69852, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart body]', symObjAddr: 0x5510, symBinAddr: 0x14954, symSize: 0x8 }
  - { offset: 0x6988A, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart setBody:]', symObjAddr: 0x5518, symBinAddr: 0x1495C, symSize: 0xC }
  - { offset: 0x698CC, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart bodyContentLength]', symObjAddr: 0x5524, symBinAddr: 0x14968, symSize: 0x8 }
  - { offset: 0x69904, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart setBodyContentLength:]', symObjAddr: 0x552C, symBinAddr: 0x14970, symSize: 0x8 }
  - { offset: 0x69942, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart setInputStream:]', symObjAddr: 0x5534, symBinAddr: 0x14978, symSize: 0xC }
  - { offset: 0x69984, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart hasInitialBoundary]', symObjAddr: 0x5540, symBinAddr: 0x14984, symSize: 0x8 }
  - { offset: 0x699BC, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart setHasInitialBoundary:]', symObjAddr: 0x5548, symBinAddr: 0x1498C, symSize: 0x8 }
  - { offset: 0x699F8, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart hasFinalBoundary]', symObjAddr: 0x5550, symBinAddr: 0x14994, symSize: 0x8 }
  - { offset: 0x69A30, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart setHasFinalBoundary:]', symObjAddr: 0x5558, symBinAddr: 0x1499C, symSize: 0x8 }
  - { offset: 0x69A6C, size: 0x8, addend: 0x0, symName: '-[AFHTTPBodyPart .cxx_destruct]', symObjAddr: 0x5560, symBinAddr: 0x149A4, symSize: 0x48 }
  - { offset: 0x69AA0, size: 0x8, addend: 0x0, symName: '+[AFJSONRequestSerializer serializer]', symObjAddr: 0x55A8, symBinAddr: 0x149EC, symSize: 0x8 }
  - { offset: 0x69AD6, size: 0x8, addend: 0x0, symName: '+[AFJSONRequestSerializer serializerWithWritingOptions:]', symObjAddr: 0x55B0, symBinAddr: 0x149F4, symSize: 0x34 }
  - { offset: 0x69B30, size: 0x8, addend: 0x0, symName: '-[AFJSONRequestSerializer requestBySerializingRequest:withParameters:error:]', symObjAddr: 0x55E4, symBinAddr: 0x14A28, symSize: 0x334 }
  - { offset: 0x69BE4, size: 0x8, addend: 0x0, symName: '___76-[AFJSONRequestSerializer requestBySerializingRequest:withParameters:error:]_block_invoke', symObjAddr: 0x5918, symBinAddr: 0x14D5C, symSize: 0x7C }
  - { offset: 0x69C62, size: 0x8, addend: 0x0, symName: '-[AFJSONRequestSerializer initWithCoder:]', symObjAddr: 0x5994, symBinAddr: 0x14DD8, symSize: 0xE0 }
  - { offset: 0x69CB9, size: 0x8, addend: 0x0, symName: '-[AFJSONRequestSerializer encodeWithCoder:]', symObjAddr: 0x5A74, symBinAddr: 0x14EB8, symSize: 0xC4 }
  - { offset: 0x69D0C, size: 0x8, addend: 0x0, symName: '-[AFJSONRequestSerializer copyWithZone:]', symObjAddr: 0x5B38, symBinAddr: 0x14F7C, symSize: 0x5C }
  - { offset: 0x69D66, size: 0x8, addend: 0x0, symName: '-[AFJSONRequestSerializer writingOptions]', symObjAddr: 0x5B94, symBinAddr: 0x14FD8, symSize: 0x10 }
  - { offset: 0x69D9E, size: 0x8, addend: 0x0, symName: '-[AFJSONRequestSerializer setWritingOptions:]', symObjAddr: 0x5BA4, symBinAddr: 0x14FE8, symSize: 0x10 }
  - { offset: 0x69DDC, size: 0x8, addend: 0x0, symName: '+[AFPropertyListRequestSerializer serializer]', symObjAddr: 0x5BB4, symBinAddr: 0x14FF8, symSize: 0xC }
  - { offset: 0x69E12, size: 0x8, addend: 0x0, symName: '+[AFPropertyListRequestSerializer serializerWithFormat:writeOptions:]', symObjAddr: 0x5BC0, symBinAddr: 0x15004, symSize: 0x4C }
  - { offset: 0x69E7D, size: 0x8, addend: 0x0, symName: '-[AFPropertyListRequestSerializer requestBySerializingRequest:withParameters:error:]', symObjAddr: 0x5C0C, symBinAddr: 0x15050, symSize: 0x244 }
  - { offset: 0x69F19, size: 0x8, addend: 0x0, symName: '___84-[AFPropertyListRequestSerializer requestBySerializingRequest:withParameters:error:]_block_invoke', symObjAddr: 0x5E50, symBinAddr: 0x15294, symSize: 0x7C }
  - { offset: 0x69F97, size: 0x8, addend: 0x0, symName: '-[AFPropertyListRequestSerializer initWithCoder:]', symObjAddr: 0x5ECC, symBinAddr: 0x15310, symSize: 0x148 }
  - { offset: 0x69FFC, size: 0x8, addend: 0x0, symName: '-[AFPropertyListRequestSerializer encodeWithCoder:]', symObjAddr: 0x6014, symBinAddr: 0x15458, symSize: 0x128 }
  - { offset: 0x6A05D, size: 0x8, addend: 0x0, symName: '-[AFPropertyListRequestSerializer copyWithZone:]', symObjAddr: 0x613C, symBinAddr: 0x15580, symSize: 0x70 }
  - { offset: 0x6A0B7, size: 0x8, addend: 0x0, symName: '-[AFPropertyListRequestSerializer format]', symObjAddr: 0x61AC, symBinAddr: 0x155F0, symSize: 0x10 }
  - { offset: 0x6A0EF, size: 0x8, addend: 0x0, symName: '-[AFPropertyListRequestSerializer setFormat:]', symObjAddr: 0x61BC, symBinAddr: 0x15600, symSize: 0x10 }
  - { offset: 0x6A12D, size: 0x8, addend: 0x0, symName: '-[AFPropertyListRequestSerializer writeOptions]', symObjAddr: 0x61CC, symBinAddr: 0x15610, symSize: 0x10 }
  - { offset: 0x6A165, size: 0x8, addend: 0x0, symName: '-[AFPropertyListRequestSerializer setWriteOptions:]', symObjAddr: 0x61DC, symBinAddr: 0x15620, symSize: 0x10 }
  - { offset: 0x6A1DD, size: 0x8, addend: 0x0, symName: ___AFHTTPRequestSerializerObservedKeyPaths_block_invoke, symObjAddr: 0x622C, symBinAddr: 0x15670, symSize: 0x164 }
  - { offset: 0x6AB85, size: 0x8, addend: 0x0, symName: _AFJSONObjectByRemovingKeysWithNullValues, symObjAddr: 0x0, symBinAddr: 0x157D4, symSize: 0x3D4 }
  - { offset: 0x6AB9F, size: 0x8, addend: 0x0, symName: _AFURLResponseSerializationErrorDomain, symObjAddr: 0x2E50, symBinAddr: 0x308A0, symSize: 0x0 }
  - { offset: 0x6ABBF, size: 0x8, addend: 0x0, symName: _AFNetworkingOperationFailingURLResponseErrorKey, symObjAddr: 0x2E58, symBinAddr: 0x308A8, symSize: 0x0 }
  - { offset: 0x6ABD5, size: 0x8, addend: 0x0, symName: _AFNetworkingOperationFailingURLResponseDataErrorKey, symObjAddr: 0x2E60, symBinAddr: 0x308B0, symSize: 0x0 }
  - { offset: 0x6ABFA, size: 0x8, addend: 0x0, symName: _imageLock, symObjAddr: 0xEDE8, symBinAddr: 0x3BFB8, symSize: 0x0 }
  - { offset: 0x6AC09, size: 0x8, addend: 0x0, symName: '+[UIImage(AFNetworkingSafeImageLoading) af_safeImageWithData:]', symObjAddr: 0x1BA0, symBinAddr: 0x17374, symSize: 0x90 }
  - { offset: 0x6AC35, size: 0x8, addend: 0x0, symName: '_af_safeImageWithData:.onceToken', symObjAddr: 0xEDF0, symBinAddr: 0x3BFC0, symSize: 0x0 }
  - { offset: 0x6AE15, size: 0x8, addend: 0x0, symName: _AFJSONObjectByRemovingKeysWithNullValues, symObjAddr: 0x0, symBinAddr: 0x157D4, symSize: 0x3D4 }
  - { offset: 0x6AF05, size: 0x8, addend: 0x0, symName: '+[AFHTTPResponseSerializer serializer]', symObjAddr: 0x3D4, symBinAddr: 0x15BA8, symSize: 0x18 }
  - { offset: 0x6AF3C, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer init]', symObjAddr: 0x3EC, symBinAddr: 0x15BC0, symSize: 0x98 }
  - { offset: 0x6AF73, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer validateResponse:data:error:]', symObjAddr: 0x484, symBinAddr: 0x15C58, symSize: 0x594 }
  - { offset: 0x6B063, size: 0x8, addend: 0x0, symName: _AFErrorWithUnderlyingError, symObjAddr: 0xA18, symBinAddr: 0x161EC, symSize: 0x140 }
  - { offset: 0x6B0AE, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer responseObjectForResponse:data:error:]', symObjAddr: 0xB58, symBinAddr: 0x1632C, symSize: 0x50 }
  - { offset: 0x6B115, size: 0x8, addend: 0x0, symName: '+[AFHTTPResponseSerializer supportsSecureCoding]', symObjAddr: 0xBA8, symBinAddr: 0x1637C, symSize: 0x8 }
  - { offset: 0x6B148, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer initWithCoder:]', symObjAddr: 0xBB0, symBinAddr: 0x16384, symSize: 0x11C }
  - { offset: 0x6B1D1, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer encodeWithCoder:]', symObjAddr: 0xCCC, symBinAddr: 0x164A0, symSize: 0xCC }
  - { offset: 0x6B230, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer copyWithZone:]', symObjAddr: 0xD98, symBinAddr: 0x1656C, symSize: 0xC0 }
  - { offset: 0x6B287, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer acceptableStatusCodes]', symObjAddr: 0xE58, symBinAddr: 0x1662C, symSize: 0x8 }
  - { offset: 0x6B2BE, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer setAcceptableStatusCodes:]', symObjAddr: 0xE60, symBinAddr: 0x16634, symSize: 0x8 }
  - { offset: 0x6B2FD, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer acceptableContentTypes]', symObjAddr: 0xE68, symBinAddr: 0x1663C, symSize: 0x8 }
  - { offset: 0x6B334, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer setAcceptableContentTypes:]', symObjAddr: 0xE70, symBinAddr: 0x16644, symSize: 0x8 }
  - { offset: 0x6B373, size: 0x8, addend: 0x0, symName: '-[AFHTTPResponseSerializer .cxx_destruct]', symObjAddr: 0xE78, symBinAddr: 0x1664C, symSize: 0x30 }
  - { offset: 0x6B3A6, size: 0x8, addend: 0x0, symName: '+[AFJSONResponseSerializer serializer]', symObjAddr: 0xEA8, symBinAddr: 0x1667C, symSize: 0x8 }
  - { offset: 0x6B3DB, size: 0x8, addend: 0x0, symName: '+[AFJSONResponseSerializer serializerWithReadingOptions:]', symObjAddr: 0xEB0, symBinAddr: 0x16684, symSize: 0x34 }
  - { offset: 0x6B432, size: 0x8, addend: 0x0, symName: '-[AFJSONResponseSerializer init]', symObjAddr: 0xEE4, symBinAddr: 0x166B8, symSize: 0xA4 }
  - { offset: 0x6B469, size: 0x8, addend: 0x0, symName: '-[AFJSONResponseSerializer responseObjectForResponse:data:error:]', symObjAddr: 0xF88, symBinAddr: 0x1675C, symSize: 0x190 }
  - { offset: 0x6B53A, size: 0x8, addend: 0x0, symName: _AFErrorOrUnderlyingErrorHasCodeInDomain, symObjAddr: 0x1118, symBinAddr: 0x168EC, symSize: 0x10C }
  - { offset: 0x6B592, size: 0x8, addend: 0x0, symName: '+[AFJSONResponseSerializer supportsSecureCoding]', symObjAddr: 0x1224, symBinAddr: 0x169F8, symSize: 0x8 }
  - { offset: 0x6B5C6, size: 0x8, addend: 0x0, symName: '-[AFJSONResponseSerializer initWithCoder:]', symObjAddr: 0x122C, symBinAddr: 0x16A00, symSize: 0x148 }
  - { offset: 0x6B62B, size: 0x8, addend: 0x0, symName: '-[AFJSONResponseSerializer encodeWithCoder:]', symObjAddr: 0x1374, symBinAddr: 0x16B48, symSize: 0x128 }
  - { offset: 0x6B68C, size: 0x8, addend: 0x0, symName: '-[AFJSONResponseSerializer copyWithZone:]', symObjAddr: 0x149C, symBinAddr: 0x16C70, symSize: 0x70 }
  - { offset: 0x6B6E6, size: 0x8, addend: 0x0, symName: '-[AFJSONResponseSerializer readingOptions]', symObjAddr: 0x150C, symBinAddr: 0x16CE0, symSize: 0x10 }
  - { offset: 0x6B71D, size: 0x8, addend: 0x0, symName: '-[AFJSONResponseSerializer setReadingOptions:]', symObjAddr: 0x151C, symBinAddr: 0x16CF0, symSize: 0x10 }
  - { offset: 0x6B75A, size: 0x8, addend: 0x0, symName: '-[AFJSONResponseSerializer removesKeysWithNullValues]', symObjAddr: 0x152C, symBinAddr: 0x16D00, symSize: 0x10 }
  - { offset: 0x6B791, size: 0x8, addend: 0x0, symName: '-[AFJSONResponseSerializer setRemovesKeysWithNullValues:]', symObjAddr: 0x153C, symBinAddr: 0x16D10, symSize: 0x10 }
  - { offset: 0x6B7CC, size: 0x8, addend: 0x0, symName: '+[AFXMLParserResponseSerializer serializer]', symObjAddr: 0x154C, symBinAddr: 0x16D20, symSize: 0x18 }
  - { offset: 0x6B815, size: 0x8, addend: 0x0, symName: '-[AFXMLParserResponseSerializer init]', symObjAddr: 0x1564, symBinAddr: 0x16D38, symSize: 0x94 }
  - { offset: 0x6B84D, size: 0x8, addend: 0x0, symName: '-[AFXMLParserResponseSerializer responseObjectForResponse:data:error:]', symObjAddr: 0x15F8, symBinAddr: 0x16DCC, symSize: 0x8C }
  - { offset: 0x6B8C6, size: 0x8, addend: 0x0, symName: '+[AFPropertyListResponseSerializer serializer]', symObjAddr: 0x1684, symBinAddr: 0x16E58, symSize: 0xC }
  - { offset: 0x6B8FC, size: 0x8, addend: 0x0, symName: '+[AFPropertyListResponseSerializer serializerWithFormat:readOptions:]', symObjAddr: 0x1690, symBinAddr: 0x16E64, symSize: 0x4C }
  - { offset: 0x6B967, size: 0x8, addend: 0x0, symName: '-[AFPropertyListResponseSerializer init]', symObjAddr: 0x16DC, symBinAddr: 0x16EB0, symSize: 0x8C }
  - { offset: 0x6B99F, size: 0x8, addend: 0x0, symName: '-[AFPropertyListResponseSerializer responseObjectForResponse:data:error:]', symObjAddr: 0x1768, symBinAddr: 0x16F3C, symSize: 0x110 }
  - { offset: 0x6BA50, size: 0x8, addend: 0x0, symName: '+[AFPropertyListResponseSerializer supportsSecureCoding]', symObjAddr: 0x1878, symBinAddr: 0x1704C, symSize: 0x8 }
  - { offset: 0x6BA84, size: 0x8, addend: 0x0, symName: '-[AFPropertyListResponseSerializer initWithCoder:]', symObjAddr: 0x1880, symBinAddr: 0x17054, symSize: 0x148 }
  - { offset: 0x6BAE9, size: 0x8, addend: 0x0, symName: '-[AFPropertyListResponseSerializer encodeWithCoder:]', symObjAddr: 0x19C8, symBinAddr: 0x1719C, symSize: 0x128 }
  - { offset: 0x6BB4A, size: 0x8, addend: 0x0, symName: '-[AFPropertyListResponseSerializer copyWithZone:]', symObjAddr: 0x1AF0, symBinAddr: 0x172C4, symSize: 0x70 }
  - { offset: 0x6BBA4, size: 0x8, addend: 0x0, symName: '-[AFPropertyListResponseSerializer format]', symObjAddr: 0x1B60, symBinAddr: 0x17334, symSize: 0x10 }
  - { offset: 0x6BBDB, size: 0x8, addend: 0x0, symName: '-[AFPropertyListResponseSerializer setFormat:]', symObjAddr: 0x1B70, symBinAddr: 0x17344, symSize: 0x10 }
  - { offset: 0x6BC18, size: 0x8, addend: 0x0, symName: '-[AFPropertyListResponseSerializer readOptions]', symObjAddr: 0x1B80, symBinAddr: 0x17354, symSize: 0x10 }
  - { offset: 0x6BC4F, size: 0x8, addend: 0x0, symName: '-[AFPropertyListResponseSerializer setReadOptions:]', symObjAddr: 0x1B90, symBinAddr: 0x17364, symSize: 0x10 }
  - { offset: 0x6BCD3, size: 0x8, addend: 0x0, symName: '___62+[UIImage(AFNetworkingSafeImageLoading) af_safeImageWithData:]_block_invoke', symObjAddr: 0x1C30, symBinAddr: 0x17404, symSize: 0x30 }
  - { offset: 0x6BCFC, size: 0x8, addend: 0x0, symName: '-[AFImageResponseSerializer init]', symObjAddr: 0x1C60, symBinAddr: 0x17434, symSize: 0x11C }
  - { offset: 0x6BE51, size: 0x8, addend: 0x0, symName: '-[AFImageResponseSerializer responseObjectForResponse:data:error:]', symObjAddr: 0x1D7C, symBinAddr: 0x17550, symSize: 0x3A8 }
  - { offset: 0x6C228, size: 0x8, addend: 0x0, symName: _AFImageWithDataAtScale, symObjAddr: 0x2124, symBinAddr: 0x178F8, symSize: 0xB8 }
  - { offset: 0x6C3BE, size: 0x8, addend: 0x0, symName: '+[AFImageResponseSerializer supportsSecureCoding]', symObjAddr: 0x21DC, symBinAddr: 0x179B0, symSize: 0x8 }
  - { offset: 0x6C3F2, size: 0x8, addend: 0x0, symName: '-[AFImageResponseSerializer initWithCoder:]', symObjAddr: 0x21E4, symBinAddr: 0x179B8, symSize: 0x118 }
  - { offset: 0x6C468, size: 0x8, addend: 0x0, symName: '-[AFImageResponseSerializer encodeWithCoder:]', symObjAddr: 0x22FC, symBinAddr: 0x17AD0, symSize: 0xFC }
  - { offset: 0x6C4C9, size: 0x8, addend: 0x0, symName: '-[AFImageResponseSerializer copyWithZone:]', symObjAddr: 0x23F8, symBinAddr: 0x17BCC, symSize: 0x6C }
  - { offset: 0x6C523, size: 0x8, addend: 0x0, symName: '-[AFImageResponseSerializer imageScale]', symObjAddr: 0x2464, symBinAddr: 0x17C38, symSize: 0x10 }
  - { offset: 0x6C558, size: 0x8, addend: 0x0, symName: '-[AFImageResponseSerializer setImageScale:]', symObjAddr: 0x2474, symBinAddr: 0x17C48, symSize: 0x10 }
  - { offset: 0x6C596, size: 0x8, addend: 0x0, symName: '-[AFImageResponseSerializer automaticallyInflatesResponseImage]', symObjAddr: 0x2484, symBinAddr: 0x17C58, symSize: 0x10 }
  - { offset: 0x6C5CD, size: 0x8, addend: 0x0, symName: '-[AFImageResponseSerializer setAutomaticallyInflatesResponseImage:]', symObjAddr: 0x2494, symBinAddr: 0x17C68, symSize: 0x10 }
  - { offset: 0x6C608, size: 0x8, addend: 0x0, symName: '+[AFCompoundResponseSerializer compoundSerializerWithResponseSerializers:]', symObjAddr: 0x24A4, symBinAddr: 0x17C78, symSize: 0x4C }
  - { offset: 0x6C662, size: 0x8, addend: 0x0, symName: '-[AFCompoundResponseSerializer responseObjectForResponse:data:error:]', symObjAddr: 0x24F0, symBinAddr: 0x17CC4, symSize: 0x1FC }
  - { offset: 0x6C724, size: 0x8, addend: 0x0, symName: '+[AFCompoundResponseSerializer supportsSecureCoding]', symObjAddr: 0x26EC, symBinAddr: 0x17EC0, symSize: 0x8 }
  - { offset: 0x6C758, size: 0x8, addend: 0x0, symName: '-[AFCompoundResponseSerializer initWithCoder:]', symObjAddr: 0x26F4, symBinAddr: 0x17EC8, symSize: 0x16C }
  - { offset: 0x6C7C0, size: 0x8, addend: 0x0, symName: '-[AFCompoundResponseSerializer encodeWithCoder:]', symObjAddr: 0x2860, symBinAddr: 0x18034, symSize: 0xB0 }
  - { offset: 0x6C813, size: 0x8, addend: 0x0, symName: '-[AFCompoundResponseSerializer copyWithZone:]', symObjAddr: 0x2910, symBinAddr: 0x180E4, symSize: 0x70 }
  - { offset: 0x6C86D, size: 0x8, addend: 0x0, symName: '-[AFCompoundResponseSerializer responseSerializers]', symObjAddr: 0x2980, symBinAddr: 0x18154, symSize: 0x10 }
  - { offset: 0x6C8A5, size: 0x8, addend: 0x0, symName: '-[AFCompoundResponseSerializer setResponseSerializers:]', symObjAddr: 0x2990, symBinAddr: 0x18164, symSize: 0xC }
  - { offset: 0x6C8E5, size: 0x8, addend: 0x0, symName: '-[AFCompoundResponseSerializer .cxx_destruct]', symObjAddr: 0x299C, symBinAddr: 0x18170, symSize: 0x14 }
  - { offset: 0x6CA80, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate initWithTask:]', symObjAddr: 0x0, symBinAddr: 0x18184, symSize: 0x3CC }
  - { offset: 0x6CA9A, size: 0x8, addend: 0x0, symName: _AFNetworkingTaskDidResumeNotification, symObjAddr: 0x5DE0, symBinAddr: 0x308D8, symSize: 0x0 }
  - { offset: 0x6CABA, size: 0x8, addend: 0x0, symName: _AFNetworkingTaskDidCompleteNotification, symObjAddr: 0x5DE8, symBinAddr: 0x308E0, symSize: 0x0 }
  - { offset: 0x6CAD0, size: 0x8, addend: 0x0, symName: _AFNetworkingTaskDidSuspendNotification, symObjAddr: 0x5DF0, symBinAddr: 0x308E8, symSize: 0x0 }
  - { offset: 0x6CAE6, size: 0x8, addend: 0x0, symName: _AFURLSessionDidInvalidateNotification, symObjAddr: 0x5DF8, symBinAddr: 0x308F0, symSize: 0x0 }
  - { offset: 0x6CAFC, size: 0x8, addend: 0x0, symName: _AFURLSessionDownloadTaskDidMoveFileSuccessfullyNotification, symObjAddr: 0x5E00, symBinAddr: 0x308F8, symSize: 0x0 }
  - { offset: 0x6CB12, size: 0x8, addend: 0x0, symName: _AFURLSessionDownloadTaskDidFailToMoveFileNotification, symObjAddr: 0x5E08, symBinAddr: 0x30900, symSize: 0x0 }
  - { offset: 0x6CB28, size: 0x8, addend: 0x0, symName: _AFNetworkingTaskDidCompleteSerializedResponseKey, symObjAddr: 0x5E10, symBinAddr: 0x30908, symSize: 0x0 }
  - { offset: 0x6CB3E, size: 0x8, addend: 0x0, symName: _AFNetworkingTaskDidCompleteResponseSerializerKey, symObjAddr: 0x5E18, symBinAddr: 0x30910, symSize: 0x0 }
  - { offset: 0x6CB54, size: 0x8, addend: 0x0, symName: _AFNetworkingTaskDidCompleteResponseDataKey, symObjAddr: 0x5E20, symBinAddr: 0x30918, symSize: 0x0 }
  - { offset: 0x6CB6A, size: 0x8, addend: 0x0, symName: _AFNetworkingTaskDidCompleteErrorKey, symObjAddr: 0x5E28, symBinAddr: 0x30920, symSize: 0x0 }
  - { offset: 0x6CB80, size: 0x8, addend: 0x0, symName: _AFNetworkingTaskDidCompleteAssetPathKey, symObjAddr: 0x5E30, symBinAddr: 0x30928, symSize: 0x0 }
  - { offset: 0x6CB96, size: 0x8, addend: 0x0, symName: _AFNetworkingTaskDidCompleteSessionTaskMetrics, symObjAddr: 0x5E38, symBinAddr: 0x30930, symSize: 0x0 }
  - { offset: 0x6CBAC, size: 0x8, addend: 0x0, symName: _AuthenticationChallengeErrorKey, symObjAddr: 0x5E70, symBinAddr: 0x30968, symSize: 0x0 }
  - { offset: 0x6CBC1, size: 0x8, addend: 0x0, symName: _url_session_manager_completion_group, symObjAddr: 0x53A0, symBinAddr: 0x1D3E4, symSize: 0x40 }
  - { offset: 0x6CBE7, size: 0x8, addend: 0x0, symName: _url_session_manager_completion_group.af_url_session_manager_completion_group, symObjAddr: 0x243A0, symBinAddr: 0x3BFC8, symSize: 0x0 }
  - { offset: 0x6CBFD, size: 0x8, addend: 0x0, symName: _url_session_manager_completion_group.onceToken, symObjAddr: 0x243A8, symBinAddr: 0x3BFD0, symSize: 0x0 }
  - { offset: 0x6D925, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate initWithTask:]', symObjAddr: 0x0, symBinAddr: 0x18184, symSize: 0x3CC }
  - { offset: 0x6D9CF, size: 0x8, addend: 0x0, symName: '___48-[AFURLSessionManagerTaskDelegate initWithTask:]_block_invoke', symObjAddr: 0x3CC, symBinAddr: 0x18550, symSize: 0x2C }
  - { offset: 0x6DA0A, size: 0x8, addend: 0x0, symName: '___48-[AFURLSessionManagerTaskDelegate initWithTask:]_block_invoke.28', symObjAddr: 0x40C, symBinAddr: 0x1857C, symSize: 0x2C }
  - { offset: 0x6DA45, size: 0x8, addend: 0x0, symName: '___48-[AFURLSessionManagerTaskDelegate initWithTask:]_block_invoke_2', symObjAddr: 0x438, symBinAddr: 0x185A8, symSize: 0x2C }
  - { offset: 0x6DA80, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate dealloc]', symObjAddr: 0x464, symBinAddr: 0x185D4, symSize: 0xDC }
  - { offset: 0x6DADF, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate observeValueForKeyPath:ofObject:change:context:]', symObjAddr: 0x540, symBinAddr: 0x186B0, symSize: 0x104 }
  - { offset: 0x6DBA2, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate URLSession:task:didCompleteWithError:]', symObjAddr: 0x644, symBinAddr: 0x187B4, symSize: 0x484 }
  - { offset: 0x6DD44, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0xAC8, symBinAddr: 0x18C38, symSize: 0x10 }
  - { offset: 0x6DD69, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0xAD8, symBinAddr: 0x18C48, symSize: 0x8 }
  - { offset: 0x6DD88, size: 0x8, addend: 0x0, symName: '___72-[AFURLSessionManagerTaskDelegate URLSession:task:didCompleteWithError:]_block_invoke', symObjAddr: 0xAE0, symBinAddr: 0x18C50, symSize: 0x104 }
  - { offset: 0x6DE38, size: 0x8, addend: 0x0, symName: '___72-[AFURLSessionManagerTaskDelegate URLSession:task:didCompleteWithError:]_block_invoke_2', symObjAddr: 0xBE4, symBinAddr: 0x18D54, symSize: 0x48 }
  - { offset: 0x6DE83, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56s64r, symObjAddr: 0xC7C, symBinAddr: 0x18D9C, symSize: 0x4C }
  - { offset: 0x6DEAC, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56s64r, symObjAddr: 0xCC8, symBinAddr: 0x18DE8, symSize: 0x44 }
  - { offset: 0x6DECB, size: 0x8, addend: 0x0, symName: '___72-[AFURLSessionManagerTaskDelegate URLSession:task:didCompleteWithError:]_block_invoke.35', symObjAddr: 0xD0C, symBinAddr: 0x18E2C, symSize: 0x244 }
  - { offset: 0x6DFC0, size: 0x8, addend: 0x0, symName: '___72-[AFURLSessionManagerTaskDelegate URLSession:task:didCompleteWithError:]_block_invoke_2.36', symObjAddr: 0xF50, symBinAddr: 0x19070, symSize: 0x104 }
  - { offset: 0x6E070, size: 0x8, addend: 0x0, symName: '___72-[AFURLSessionManagerTaskDelegate URLSession:task:didCompleteWithError:]_block_invoke_3', symObjAddr: 0x1054, symBinAddr: 0x19174, symSize: 0x48 }
  - { offset: 0x6E0BB, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56s64s72r, symObjAddr: 0x109C, symBinAddr: 0x191BC, symSize: 0x54 }
  - { offset: 0x6E0E4, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate URLSession:task:didFinishCollectingMetrics:]', symObjAddr: 0x113C, symBinAddr: 0x19210, symSize: 0x8 }
  - { offset: 0x6E13D, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate URLSession:dataTask:didReceiveData:]', symObjAddr: 0x1144, symBinAddr: 0x19218, symSize: 0xD4 }
  - { offset: 0x6E1A0, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate URLSession:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:]', symObjAddr: 0x1218, symBinAddr: 0x192EC, symSize: 0x90 }
  - { offset: 0x6E219, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate URLSession:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:]', symObjAddr: 0x12A8, symBinAddr: 0x1937C, symSize: 0x6C }
  - { offset: 0x6E296, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate URLSession:downloadTask:didResumeAtOffset:expectedTotalBytes:]', symObjAddr: 0x1314, symBinAddr: 0x193E8, symSize: 0x6C }
  - { offset: 0x6E306, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate URLSession:downloadTask:didFinishDownloadingToURL:]', symObjAddr: 0x1380, symBinAddr: 0x19454, symSize: 0x1F4 }
  - { offset: 0x6E3AF, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate manager]', symObjAddr: 0x1574, symBinAddr: 0x19648, symSize: 0x18 }
  - { offset: 0x6E3E6, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setManager:]', symObjAddr: 0x158C, symBinAddr: 0x19660, symSize: 0xC }
  - { offset: 0x6E427, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate mutableData]', symObjAddr: 0x1598, symBinAddr: 0x1966C, symSize: 0x8 }
  - { offset: 0x6E45E, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setMutableData:]', symObjAddr: 0x15A0, symBinAddr: 0x19674, symSize: 0xC }
  - { offset: 0x6E49F, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate uploadProgress]', symObjAddr: 0x15AC, symBinAddr: 0x19680, symSize: 0x8 }
  - { offset: 0x6E4D6, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setUploadProgress:]', symObjAddr: 0x15B4, symBinAddr: 0x19688, symSize: 0xC }
  - { offset: 0x6E517, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate downloadProgress]', symObjAddr: 0x15C0, symBinAddr: 0x19694, symSize: 0x8 }
  - { offset: 0x6E54E, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setDownloadProgress:]', symObjAddr: 0x15C8, symBinAddr: 0x1969C, symSize: 0xC }
  - { offset: 0x6E58F, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate downloadFileURL]', symObjAddr: 0x15D4, symBinAddr: 0x196A8, symSize: 0x8 }
  - { offset: 0x6E5C6, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setDownloadFileURL:]', symObjAddr: 0x15DC, symBinAddr: 0x196B0, symSize: 0x8 }
  - { offset: 0x6E605, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate sessionTaskMetrics]', symObjAddr: 0x15E4, symBinAddr: 0x196B8, symSize: 0x8 }
  - { offset: 0x6E63C, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setSessionTaskMetrics:]', symObjAddr: 0x15EC, symBinAddr: 0x196C0, symSize: 0xC }
  - { offset: 0x6E67D, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate downloadTaskDidFinishDownloading]', symObjAddr: 0x15F8, symBinAddr: 0x196CC, symSize: 0x8 }
  - { offset: 0x6E6B4, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setDownloadTaskDidFinishDownloading:]', symObjAddr: 0x1600, symBinAddr: 0x196D4, symSize: 0x8 }
  - { offset: 0x6E6F3, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate uploadProgressBlock]', symObjAddr: 0x1608, symBinAddr: 0x196DC, symSize: 0x8 }
  - { offset: 0x6E72A, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setUploadProgressBlock:]', symObjAddr: 0x1610, symBinAddr: 0x196E4, symSize: 0x8 }
  - { offset: 0x6E769, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate downloadProgressBlock]', symObjAddr: 0x1618, symBinAddr: 0x196EC, symSize: 0x8 }
  - { offset: 0x6E7A0, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setDownloadProgressBlock:]', symObjAddr: 0x1620, symBinAddr: 0x196F4, symSize: 0x8 }
  - { offset: 0x6E7DF, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate completionHandler]', symObjAddr: 0x1628, symBinAddr: 0x196FC, symSize: 0x8 }
  - { offset: 0x6E816, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate setCompletionHandler:]', symObjAddr: 0x1630, symBinAddr: 0x19704, symSize: 0x8 }
  - { offset: 0x6E855, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManagerTaskDelegate .cxx_destruct]', symObjAddr: 0x1638, symBinAddr: 0x1970C, symSize: 0x8C }
  - { offset: 0x6E888, size: 0x8, addend: 0x0, symName: '+[_AFURLSessionTaskSwizzling load]', symObjAddr: 0x16C4, symBinAddr: 0x19798, symSize: 0x16C }
  - { offset: 0x6EAC1, size: 0x8, addend: 0x0, symName: '+[_AFURLSessionTaskSwizzling swizzleResumeAndSuspendMethodForClass:]', symObjAddr: 0x1830, symBinAddr: 0x19904, symSize: 0x11C }
  - { offset: 0x6EDCC, size: 0x8, addend: 0x0, symName: '-[_AFURLSessionTaskSwizzling state]', symObjAddr: 0x194C, symBinAddr: 0x19A20, symSize: 0x8 }
  - { offset: 0x6EE00, size: 0x8, addend: 0x0, symName: '-[_AFURLSessionTaskSwizzling af_resume]', symObjAddr: 0x1954, symBinAddr: 0x19A28, symSize: 0x68 }
  - { offset: 0x6EE45, size: 0x8, addend: 0x0, symName: '-[_AFURLSessionTaskSwizzling af_suspend]', symObjAddr: 0x19BC, symBinAddr: 0x19A90, symSize: 0x6C }
  - { offset: 0x6EE8A, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager init]', symObjAddr: 0x1A28, symBinAddr: 0x19AFC, symSize: 0x8 }
  - { offset: 0x6EEC0, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager initWithSessionConfiguration:]', symObjAddr: 0x1A30, symBinAddr: 0x19B04, symSize: 0x254 }
  - { offset: 0x6EF09, size: 0x8, addend: 0x0, symName: '___52-[AFURLSessionManager initWithSessionConfiguration:]_block_invoke', symObjAddr: 0x1C84, symBinAddr: 0x19D58, symSize: 0x278 }
  - { offset: 0x6EFDE, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager dealloc]', symObjAddr: 0x1F0C, symBinAddr: 0x19FD0, symSize: 0x68 }
  - { offset: 0x6F012, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager session]', symObjAddr: 0x1F74, symBinAddr: 0x1A038, symSize: 0xC8 }
  - { offset: 0x6F04A, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager taskDescriptionForSessionTasks]', symObjAddr: 0x203C, symBinAddr: 0x1A100, symSize: 0x34 }
  - { offset: 0x6F082, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager taskDidResume:]', symObjAddr: 0x2070, symBinAddr: 0x1A134, symSize: 0xF4 }
  - { offset: 0x6F0EE, size: 0x8, addend: 0x0, symName: '___37-[AFURLSessionManager taskDidResume:]_block_invoke', symObjAddr: 0x2164, symBinAddr: 0x1A228, symSize: 0x48 }
  - { offset: 0x6F12C, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager taskDidSuspend:]', symObjAddr: 0x21AC, symBinAddr: 0x1A270, symSize: 0xF4 }
  - { offset: 0x6F198, size: 0x8, addend: 0x0, symName: '___38-[AFURLSessionManager taskDidSuspend:]_block_invoke', symObjAddr: 0x22A0, symBinAddr: 0x1A364, symSize: 0x48 }
  - { offset: 0x6F1D6, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager delegateForTask:]', symObjAddr: 0x22E8, symBinAddr: 0x1A3AC, symSize: 0xEC }
  - { offset: 0x6F230, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDelegate:forTask:]', symObjAddr: 0x23D4, symBinAddr: 0x1A498, symSize: 0xFC }
  - { offset: 0x6F286, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager addDelegateForDataTask:uploadProgress:downloadProgress:completionHandler:]', symObjAddr: 0x24D0, symBinAddr: 0x1A594, symSize: 0x10C }
  - { offset: 0x6F30F, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager addDelegateForUploadTask:progress:completionHandler:]', symObjAddr: 0x25DC, symBinAddr: 0x1A6A0, symSize: 0xE8 }
  - { offset: 0x6F387, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager addDelegateForDownloadTask:progress:destination:completionHandler:]', symObjAddr: 0x26C4, symBinAddr: 0x1A788, symSize: 0x164 }
  - { offset: 0x6F410, size: 0x8, addend: 0x0, symName: '___89-[AFURLSessionManager addDelegateForDownloadTask:progress:destination:completionHandler:]_block_invoke', symObjAddr: 0x2828, symBinAddr: 0x1A8EC, symSize: 0x7C }
  - { offset: 0x6F4A3, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager removeDelegateForTask:]', symObjAddr: 0x28B4, symBinAddr: 0x1A968, symSize: 0xE4 }
  - { offset: 0x6F4E8, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager tasksForKeyPath:]', symObjAddr: 0x2998, symBinAddr: 0x1AA4C, symSize: 0x154 }
  - { offset: 0x6F5B3, size: 0x8, addend: 0x0, symName: '___39-[AFURLSessionManager tasksForKeyPath:]_block_invoke', symObjAddr: 0x2AEC, symBinAddr: 0x1ABA0, symSize: 0x23C }
  - { offset: 0x6F69E, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager tasks]', symObjAddr: 0x2D98, symBinAddr: 0x1ADDC, symSize: 0x54 }
  - { offset: 0x6F6F1, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager dataTasks]', symObjAddr: 0x2DEC, symBinAddr: 0x1AE30, symSize: 0x54 }
  - { offset: 0x6F744, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager uploadTasks]', symObjAddr: 0x2E40, symBinAddr: 0x1AE84, symSize: 0x54 }
  - { offset: 0x6F797, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager downloadTasks]', symObjAddr: 0x2E94, symBinAddr: 0x1AED8, symSize: 0x54 }
  - { offset: 0x6F7EA, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager invalidateSessionCancelingTasks:resetSession:]', symObjAddr: 0x2EE8, symBinAddr: 0x1AF2C, symSize: 0x70 }
  - { offset: 0x6F840, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setResponseSerializer:]', symObjAddr: 0x2F58, symBinAddr: 0x1AF9C, symSize: 0xC }
  - { offset: 0x6F885, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager addNotificationObserverForTask:]', symObjAddr: 0x2F64, symBinAddr: 0x1AFA8, symSize: 0xAC }
  - { offset: 0x6F8CA, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager removeNotificationObserverForTask:]', symObjAddr: 0x3010, symBinAddr: 0x1B054, symSize: 0x9C }
  - { offset: 0x6F90F, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager dataTaskWithRequest:uploadProgress:downloadProgress:completionHandler:]', symObjAddr: 0x30AC, symBinAddr: 0x1B0F0, symSize: 0xDC }
  - { offset: 0x6F99C, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager uploadTaskWithRequest:fromFile:progress:completionHandler:]', symObjAddr: 0x3188, symBinAddr: 0x1B1CC, symSize: 0xE0 }
  - { offset: 0x6FA29, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager uploadTaskWithRequest:fromData:progress:completionHandler:]', symObjAddr: 0x3268, symBinAddr: 0x1B2AC, symSize: 0xDC }
  - { offset: 0x6FAB6, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager uploadTaskWithStreamedRequest:progress:completionHandler:]', symObjAddr: 0x3344, symBinAddr: 0x1B388, symSize: 0xB8 }
  - { offset: 0x6FB32, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager downloadTaskWithRequest:progress:destination:completionHandler:]', symObjAddr: 0x33FC, symBinAddr: 0x1B440, symSize: 0xDC }
  - { offset: 0x6FBBF, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager downloadTaskWithResumeData:progress:destination:completionHandler:]', symObjAddr: 0x34D8, symBinAddr: 0x1B51C, symSize: 0xDC }
  - { offset: 0x6FC4C, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager uploadProgressForTask:]', symObjAddr: 0x35B4, symBinAddr: 0x1B5F8, symSize: 0x44 }
  - { offset: 0x6FC95, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager downloadProgressForTask:]', symObjAddr: 0x35F8, symBinAddr: 0x1B63C, symSize: 0x44 }
  - { offset: 0x6FCDE, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setSessionDidBecomeInvalidBlock:]', symObjAddr: 0x363C, symBinAddr: 0x1B680, symSize: 0x4 }
  - { offset: 0x6FD1F, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setSessionDidReceiveAuthenticationChallengeBlock:]', symObjAddr: 0x3640, symBinAddr: 0x1B684, symSize: 0x4 }
  - { offset: 0x6FD60, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDidFinishEventsForBackgroundURLSessionBlock:]', symObjAddr: 0x3644, symBinAddr: 0x1B688, symSize: 0x4 }
  - { offset: 0x6FDA1, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskNeedNewBodyStreamBlock:]', symObjAddr: 0x3648, symBinAddr: 0x1B68C, symSize: 0x4 }
  - { offset: 0x6FDE2, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskWillPerformHTTPRedirectionBlock:]', symObjAddr: 0x364C, symBinAddr: 0x1B690, symSize: 0x4 }
  - { offset: 0x6FE23, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskDidSendBodyDataBlock:]', symObjAddr: 0x3650, symBinAddr: 0x1B694, symSize: 0x4 }
  - { offset: 0x6FE64, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskDidCompleteBlock:]', symObjAddr: 0x3654, symBinAddr: 0x1B698, symSize: 0x4 }
  - { offset: 0x6FEA5, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskDidFinishCollectingMetricsBlock:]', symObjAddr: 0x3658, symBinAddr: 0x1B69C, symSize: 0x4 }
  - { offset: 0x6FEE6, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDataTaskDidReceiveResponseBlock:]', symObjAddr: 0x365C, symBinAddr: 0x1B6A0, symSize: 0x4 }
  - { offset: 0x6FF27, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDataTaskDidBecomeDownloadTaskBlock:]', symObjAddr: 0x3660, symBinAddr: 0x1B6A4, symSize: 0x4 }
  - { offset: 0x6FF68, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDataTaskDidReceiveDataBlock:]', symObjAddr: 0x3664, symBinAddr: 0x1B6A8, symSize: 0x4 }
  - { offset: 0x6FFA9, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDataTaskWillCacheResponseBlock:]', symObjAddr: 0x3668, symBinAddr: 0x1B6AC, symSize: 0x4 }
  - { offset: 0x6FFEA, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDownloadTaskDidFinishDownloadingBlock:]', symObjAddr: 0x366C, symBinAddr: 0x1B6B0, symSize: 0x4 }
  - { offset: 0x7002B, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDownloadTaskDidWriteDataBlock:]', symObjAddr: 0x3670, symBinAddr: 0x1B6B4, symSize: 0x4 }
  - { offset: 0x7006C, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDownloadTaskDidResumeBlock:]', symObjAddr: 0x3674, symBinAddr: 0x1B6B8, symSize: 0x4 }
  - { offset: 0x700AD, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager description]', symObjAddr: 0x3678, symBinAddr: 0x1B6BC, symSize: 0xB8 }
  - { offset: 0x70106, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager respondsToSelector:]', symObjAddr: 0x3730, symBinAddr: 0x1B774, symSize: 0xDC }
  - { offset: 0x7014F, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:didBecomeInvalidWithError:]', symObjAddr: 0x380C, symBinAddr: 0x1B850, symSize: 0xC4 }
  - { offset: 0x701C0, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:didReceiveChallenge:completionHandler:]', symObjAddr: 0x38D0, symBinAddr: 0x1B914, symSize: 0xDC }
  - { offset: 0x7028D, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:task:willPerformHTTPRedirection:newRequest:completionHandler:]', symObjAddr: 0x39AC, symBinAddr: 0x1B9F0, symSize: 0x134 }
  - { offset: 0x7036B, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:task:didReceiveChallenge:completionHandler:]', symObjAddr: 0x3AE0, symBinAddr: 0x1BB24, symSize: 0x3FC }
  - { offset: 0x704E1, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager serverTrustErrorForServerTrust:url:]', symObjAddr: 0x3EDC, symBinAddr: 0x1BF20, symSize: 0x278 }
  - { offset: 0x7058C, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:task:needNewBodyStream:]', symObjAddr: 0x4154, symBinAddr: 0x1C198, symSize: 0x1A8 }
  - { offset: 0x7063A, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:]', symObjAddr: 0x42FC, symBinAddr: 0x1C340, symSize: 0x164 }
  - { offset: 0x70735, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:task:didCompleteWithError:]', symObjAddr: 0x4460, symBinAddr: 0x1C4A4, symSize: 0xFC }
  - { offset: 0x707CF, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:task:didFinishCollectingMetrics:]', symObjAddr: 0x455C, symBinAddr: 0x1C5A0, symSize: 0xF0 }
  - { offset: 0x70869, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:dataTask:didReceiveResponse:completionHandler:]', symObjAddr: 0x464C, symBinAddr: 0x1C690, symSize: 0xF4 }
  - { offset: 0x7092F, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:dataTask:didBecomeDownloadTask:]', symObjAddr: 0x4740, symBinAddr: 0x1C784, symSize: 0xF8 }
  - { offset: 0x709C9, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:dataTask:didReceiveData:]', symObjAddr: 0x4838, symBinAddr: 0x1C87C, symSize: 0xE8 }
  - { offset: 0x70A63, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:dataTask:willCacheResponse:completionHandler:]', symObjAddr: 0x4920, symBinAddr: 0x1C964, symSize: 0x118 }
  - { offset: 0x70B29, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSessionDidFinishEventsForBackgroundURLSession:]', symObjAddr: 0x4A38, symBinAddr: 0x1CA7C, symSize: 0xB0 }
  - { offset: 0x70B84, size: 0x8, addend: 0x0, symName: '___72-[AFURLSessionManager URLSessionDidFinishEventsForBackgroundURLSession:]_block_invoke', symObjAddr: 0x4AE8, symBinAddr: 0x1CB2C, symSize: 0x40 }
  - { offset: 0x70BE3, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:downloadTask:didFinishDownloadingToURL:]', symObjAddr: 0x4B28, symBinAddr: 0x1CB6C, symSize: 0x1EC }
  - { offset: 0x70CAD, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:]', symObjAddr: 0x4D14, symBinAddr: 0x1CD58, symSize: 0xFC }
  - { offset: 0x70D77, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager URLSession:downloadTask:didResumeAtOffset:expectedTotalBytes:]', symObjAddr: 0x4E10, symBinAddr: 0x1CE54, symSize: 0xF0 }
  - { offset: 0x70E29, size: 0x8, addend: 0x0, symName: '+[AFURLSessionManager supportsSecureCoding]', symObjAddr: 0x4F00, symBinAddr: 0x1CF44, symSize: 0x8 }
  - { offset: 0x70E5D, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager initWithCoder:]', symObjAddr: 0x4F08, symBinAddr: 0x1CF4C, symSize: 0x98 }
  - { offset: 0x70EB7, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager encodeWithCoder:]', symObjAddr: 0x4FA0, symBinAddr: 0x1CFE4, symSize: 0x7C }
  - { offset: 0x70EFC, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager copyWithZone:]', symObjAddr: 0x501C, symBinAddr: 0x1D060, symSize: 0x80 }
  - { offset: 0x70F45, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setSession:]', symObjAddr: 0x509C, symBinAddr: 0x1D0E0, symSize: 0xC }
  - { offset: 0x70F87, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager operationQueue]', symObjAddr: 0x50A8, symBinAddr: 0x1D0EC, symSize: 0x8 }
  - { offset: 0x70FBE, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setOperationQueue:]', symObjAddr: 0x50B0, symBinAddr: 0x1D0F4, symSize: 0xC }
  - { offset: 0x71000, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager responseSerializer]', symObjAddr: 0x50BC, symBinAddr: 0x1D100, symSize: 0x8 }
  - { offset: 0x71037, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager securityPolicy]', symObjAddr: 0x50C4, symBinAddr: 0x1D108, symSize: 0x8 }
  - { offset: 0x7106E, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setSecurityPolicy:]', symObjAddr: 0x50CC, symBinAddr: 0x1D110, symSize: 0xC }
  - { offset: 0x710AF, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager reachabilityManager]', symObjAddr: 0x50D8, symBinAddr: 0x1D11C, symSize: 0x8 }
  - { offset: 0x710E6, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setReachabilityManager:]', symObjAddr: 0x50E0, symBinAddr: 0x1D124, symSize: 0xC }
  - { offset: 0x71127, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager completionQueue]', symObjAddr: 0x50EC, symBinAddr: 0x1D130, symSize: 0x8 }
  - { offset: 0x7115E, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setCompletionQueue:]', symObjAddr: 0x50F4, symBinAddr: 0x1D138, symSize: 0xC }
  - { offset: 0x7119F, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager completionGroup]', symObjAddr: 0x5100, symBinAddr: 0x1D144, symSize: 0x8 }
  - { offset: 0x711D6, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setCompletionGroup:]', symObjAddr: 0x5108, symBinAddr: 0x1D14C, symSize: 0xC }
  - { offset: 0x71217, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager sessionConfiguration]', symObjAddr: 0x5114, symBinAddr: 0x1D158, symSize: 0x8 }
  - { offset: 0x7124F, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setSessionConfiguration:]', symObjAddr: 0x511C, symBinAddr: 0x1D160, symSize: 0xC }
  - { offset: 0x71291, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager mutableTaskDelegatesKeyedByTaskIdentifier]', symObjAddr: 0x5128, symBinAddr: 0x1D16C, symSize: 0x8 }
  - { offset: 0x712C9, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setMutableTaskDelegatesKeyedByTaskIdentifier:]', symObjAddr: 0x5130, symBinAddr: 0x1D174, symSize: 0xC }
  - { offset: 0x7130B, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager lock]', symObjAddr: 0x513C, symBinAddr: 0x1D180, symSize: 0x8 }
  - { offset: 0x71343, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setLock:]', symObjAddr: 0x5144, symBinAddr: 0x1D188, symSize: 0xC }
  - { offset: 0x71385, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager sessionDidBecomeInvalid]', symObjAddr: 0x5150, symBinAddr: 0x1D194, symSize: 0x8 }
  - { offset: 0x713BD, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setSessionDidBecomeInvalid:]', symObjAddr: 0x5158, symBinAddr: 0x1D19C, symSize: 0x8 }
  - { offset: 0x713FD, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager sessionDidReceiveAuthenticationChallenge]', symObjAddr: 0x5160, symBinAddr: 0x1D1A4, symSize: 0x8 }
  - { offset: 0x71435, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setSessionDidReceiveAuthenticationChallenge:]', symObjAddr: 0x5168, symBinAddr: 0x1D1AC, symSize: 0x8 }
  - { offset: 0x71475, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager didFinishEventsForBackgroundURLSession]', symObjAddr: 0x5170, symBinAddr: 0x1D1B4, symSize: 0x8 }
  - { offset: 0x714AD, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDidFinishEventsForBackgroundURLSession:]', symObjAddr: 0x5178, symBinAddr: 0x1D1BC, symSize: 0x8 }
  - { offset: 0x714ED, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager taskWillPerformHTTPRedirection]', symObjAddr: 0x5180, symBinAddr: 0x1D1C4, symSize: 0x8 }
  - { offset: 0x71525, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskWillPerformHTTPRedirection:]', symObjAddr: 0x5188, symBinAddr: 0x1D1CC, symSize: 0x8 }
  - { offset: 0x71565, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager authenticationChallengeHandler]', symObjAddr: 0x5190, symBinAddr: 0x1D1D4, symSize: 0x8 }
  - { offset: 0x7159D, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setAuthenticationChallengeHandler:]', symObjAddr: 0x5198, symBinAddr: 0x1D1DC, symSize: 0x8 }
  - { offset: 0x715E0, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager taskNeedNewBodyStream]', symObjAddr: 0x51A0, symBinAddr: 0x1D1E4, symSize: 0x8 }
  - { offset: 0x71618, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskNeedNewBodyStream:]', symObjAddr: 0x51A8, symBinAddr: 0x1D1EC, symSize: 0x8 }
  - { offset: 0x71658, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager taskDidSendBodyData]', symObjAddr: 0x51B0, symBinAddr: 0x1D1F4, symSize: 0x8 }
  - { offset: 0x71690, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskDidSendBodyData:]', symObjAddr: 0x51B8, symBinAddr: 0x1D1FC, symSize: 0x8 }
  - { offset: 0x716D0, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager taskDidComplete]', symObjAddr: 0x51C0, symBinAddr: 0x1D204, symSize: 0x8 }
  - { offset: 0x71708, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskDidComplete:]', symObjAddr: 0x51C8, symBinAddr: 0x1D20C, symSize: 0x8 }
  - { offset: 0x71748, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager taskDidFinishCollectingMetrics]', symObjAddr: 0x51D0, symBinAddr: 0x1D214, symSize: 0x8 }
  - { offset: 0x71780, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setTaskDidFinishCollectingMetrics:]', symObjAddr: 0x51D8, symBinAddr: 0x1D21C, symSize: 0x8 }
  - { offset: 0x717C0, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager dataTaskDidReceiveResponse]', symObjAddr: 0x51E0, symBinAddr: 0x1D224, symSize: 0x8 }
  - { offset: 0x717F8, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDataTaskDidReceiveResponse:]', symObjAddr: 0x51E8, symBinAddr: 0x1D22C, symSize: 0x8 }
  - { offset: 0x71838, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager dataTaskDidBecomeDownloadTask]', symObjAddr: 0x51F0, symBinAddr: 0x1D234, symSize: 0x8 }
  - { offset: 0x71870, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDataTaskDidBecomeDownloadTask:]', symObjAddr: 0x51F8, symBinAddr: 0x1D23C, symSize: 0x8 }
  - { offset: 0x718B0, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager dataTaskDidReceiveData]', symObjAddr: 0x5200, symBinAddr: 0x1D244, symSize: 0x8 }
  - { offset: 0x718E8, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDataTaskDidReceiveData:]', symObjAddr: 0x5208, symBinAddr: 0x1D24C, symSize: 0x8 }
  - { offset: 0x71928, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager dataTaskWillCacheResponse]', symObjAddr: 0x5210, symBinAddr: 0x1D254, symSize: 0x8 }
  - { offset: 0x71960, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDataTaskWillCacheResponse:]', symObjAddr: 0x5218, symBinAddr: 0x1D25C, symSize: 0x8 }
  - { offset: 0x719A0, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager downloadTaskDidFinishDownloading]', symObjAddr: 0x5220, symBinAddr: 0x1D264, symSize: 0x8 }
  - { offset: 0x719D8, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDownloadTaskDidFinishDownloading:]', symObjAddr: 0x5228, symBinAddr: 0x1D26C, symSize: 0x8 }
  - { offset: 0x71A18, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager downloadTaskDidWriteData]', symObjAddr: 0x5230, symBinAddr: 0x1D274, symSize: 0x8 }
  - { offset: 0x71A50, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDownloadTaskDidWriteData:]', symObjAddr: 0x5238, symBinAddr: 0x1D27C, symSize: 0x8 }
  - { offset: 0x71A90, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager downloadTaskDidResume]', symObjAddr: 0x5240, symBinAddr: 0x1D284, symSize: 0x8 }
  - { offset: 0x71AC8, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager setDownloadTaskDidResume:]', symObjAddr: 0x5248, symBinAddr: 0x1D28C, symSize: 0x8 }
  - { offset: 0x71B08, size: 0x8, addend: 0x0, symName: '-[AFURLSessionManager .cxx_destruct]', symObjAddr: 0x5250, symBinAddr: 0x1D294, symSize: 0x150 }
  - { offset: 0x71B3C, size: 0x8, addend: 0x0, symName: ___url_session_manager_completion_group_block_invoke, symObjAddr: 0x53E0, symBinAddr: 0x1D424, symSize: 0x24 }
  - { offset: 0x71B7D, size: 0x8, addend: 0x0, symName: ___url_session_manager_processing_queue_block_invoke, symObjAddr: 0x5404, symBinAddr: 0x1D448, symSize: 0x34 }
  - { offset: 0x726D5, size: 0x8, addend: 0x0, symName: '-[UIActivityIndicatorView(AFNetworking) af_notificationObserver]', symObjAddr: 0x0, symBinAddr: 0x1D47C, symSize: 0x74 }
  - { offset: 0x72714, size: 0x8, addend: 0x0, symName: '-[UIActivityIndicatorView(AFNetworking) af_notificationObserver]', symObjAddr: 0x0, symBinAddr: 0x1D47C, symSize: 0x74 }
  - { offset: 0x72808, size: 0x8, addend: 0x0, symName: '-[UIActivityIndicatorView(AFNetworking) setAnimatingWithStateOfTask:]', symObjAddr: 0x74, symBinAddr: 0x1D4F0, symSize: 0x50 }
  - { offset: 0x7284B, size: 0x8, addend: 0x0, symName: '-[AFActivityIndicatorViewNotificationObserver initWithActivityIndicatorView:]', symObjAddr: 0xC4, symBinAddr: 0x1D540, symSize: 0x6C }
  - { offset: 0x72892, size: 0x8, addend: 0x0, symName: '-[AFActivityIndicatorViewNotificationObserver setAnimatingWithStateOfTask:]', symObjAddr: 0x130, symBinAddr: 0x1D5AC, symSize: 0x164 }
  - { offset: 0x72904, size: 0x8, addend: 0x0, symName: '-[AFActivityIndicatorViewNotificationObserver af_startAnimating]', symObjAddr: 0x294, symBinAddr: 0x1D710, symSize: 0x58 }
  - { offset: 0x72974, size: 0x8, addend: 0x0, symName: '___64-[AFActivityIndicatorViewNotificationObserver af_startAnimating]_block_invoke', symObjAddr: 0x2EC, symBinAddr: 0x1D768, symSize: 0x34 }
  - { offset: 0x729B3, size: 0x8, addend: 0x0, symName: '-[AFActivityIndicatorViewNotificationObserver af_stopAnimating]', symObjAddr: 0x330, symBinAddr: 0x1D79C, symSize: 0x58 }
  - { offset: 0x729FC, size: 0x8, addend: 0x0, symName: '___63-[AFActivityIndicatorViewNotificationObserver af_stopAnimating]_block_invoke', symObjAddr: 0x388, symBinAddr: 0x1D7F4, symSize: 0x34 }
  - { offset: 0x72A3B, size: 0x8, addend: 0x0, symName: '-[AFActivityIndicatorViewNotificationObserver dealloc]', symObjAddr: 0x3BC, symBinAddr: 0x1D828, symSize: 0xB0 }
  - { offset: 0x72A7E, size: 0x8, addend: 0x0, symName: '-[AFActivityIndicatorViewNotificationObserver activityIndicatorView]', symObjAddr: 0x46C, symBinAddr: 0x1D8D8, symSize: 0x18 }
  - { offset: 0x72AB5, size: 0x8, addend: 0x0, symName: '-[AFActivityIndicatorViewNotificationObserver .cxx_destruct]', symObjAddr: 0x484, symBinAddr: 0x1D8F0, symSize: 0x8 }
  - { offset: 0x72C97, size: 0x8, addend: 0x0, symName: '-[UIButton(_AFNetworking) af_imageDownloadReceiptForState:]', symObjAddr: 0x0, symBinAddr: 0x1D8F8, symSize: 0x28 }
  - { offset: 0x72CB1, size: 0x8, addend: 0x0, symName: _AFImageDownloadReceiptHighlighted, symObjAddr: 0x8275, symBinAddr: 0x3BFE8, symSize: 0x0 }
  - { offset: 0x72CCE, size: 0x8, addend: 0x0, symName: _AFImageDownloadReceiptSelected, symObjAddr: 0x8276, symBinAddr: 0x3BFE9, symSize: 0x0 }
  - { offset: 0x72CE4, size: 0x8, addend: 0x0, symName: _AFImageDownloadReceiptDisabled, symObjAddr: 0x8277, symBinAddr: 0x3BFEA, symSize: 0x0 }
  - { offset: 0x72CFA, size: 0x8, addend: 0x0, symName: _AFImageDownloadReceiptNormal, symObjAddr: 0x8278, symBinAddr: 0x3BFEB, symSize: 0x0 }
  - { offset: 0x72D10, size: 0x8, addend: 0x0, symName: _AFBackgroundImageDownloadReceiptHighlighted, symObjAddr: 0x8279, symBinAddr: 0x3BFEC, symSize: 0x0 }
  - { offset: 0x72D26, size: 0x8, addend: 0x0, symName: _AFBackgroundImageDownloadReceiptSelected, symObjAddr: 0x827A, symBinAddr: 0x3BFED, symSize: 0x0 }
  - { offset: 0x72D3C, size: 0x8, addend: 0x0, symName: _AFBackgroundImageDownloadReceiptDisabled, symObjAddr: 0x827B, symBinAddr: 0x3BFEE, symSize: 0x0 }
  - { offset: 0x72D52, size: 0x8, addend: 0x0, symName: _AFBackgroundImageDownloadReceiptNormal, symObjAddr: 0x827C, symBinAddr: 0x3BFEF, symSize: 0x0 }
  - { offset: 0x72DEC, size: 0x8, addend: 0x0, symName: '-[UIButton(_AFNetworking) af_imageDownloadReceiptForState:]', symObjAddr: 0x0, symBinAddr: 0x1D8F8, symSize: 0x28 }
  - { offset: 0x72E32, size: 0x8, addend: 0x0, symName: '-[UIButton(_AFNetworking) af_imageDownloadReceiptForState:]', symObjAddr: 0x0, symBinAddr: 0x1D8F8, symSize: 0x28 }
  - { offset: 0x72EB0, size: 0x8, addend: 0x0, symName: '-[UIButton(_AFNetworking) af_setImageDownloadReceipt:forState:]', symObjAddr: 0x28, symBinAddr: 0x1D920, symSize: 0x2C }
  - { offset: 0x72F02, size: 0x8, addend: 0x0, symName: '-[UIButton(_AFNetworking) af_setImageDownloadReceipt:forState:]', symObjAddr: 0x28, symBinAddr: 0x1D920, symSize: 0x2C }
  - { offset: 0x72F6A, size: 0x8, addend: 0x0, symName: '-[UIButton(_AFNetworking) af_backgroundImageDownloadReceiptForState:]', symObjAddr: 0x54, symBinAddr: 0x1D94C, symSize: 0x28 }
  - { offset: 0x72FB0, size: 0x8, addend: 0x0, symName: '-[UIButton(_AFNetworking) af_backgroundImageDownloadReceiptForState:]', symObjAddr: 0x54, symBinAddr: 0x1D94C, symSize: 0x28 }
  - { offset: 0x72FE7, size: 0x8, addend: 0x0, symName: '-[UIButton(_AFNetworking) af_setBackgroundImageDownloadReceipt:forState:]', symObjAddr: 0x7C, symBinAddr: 0x1D974, symSize: 0x2C }
  - { offset: 0x73039, size: 0x8, addend: 0x0, symName: '-[UIButton(_AFNetworking) af_setBackgroundImageDownloadReceipt:forState:]', symObjAddr: 0x7C, symBinAddr: 0x1D974, symSize: 0x2C }
  - { offset: 0x73069, size: 0x8, addend: 0x0, symName: '+[UIButton(AFNetworking) sharedImageDownloader]', symObjAddr: 0xA8, symBinAddr: 0x1D9A0, symSize: 0x70 }
  - { offset: 0x730AA, size: 0x8, addend: 0x0, symName: '+[UIButton(AFNetworking) setSharedImageDownloader:]', symObjAddr: 0x118, symBinAddr: 0x1DA10, symSize: 0x4C }
  - { offset: 0x73105, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) setImageForState:withURL:]', symObjAddr: 0x164, symBinAddr: 0x1DA5C, symSize: 0x8 }
  - { offset: 0x73152, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) setImageForState:withURL:placeholderImage:]', symObjAddr: 0x16C, symBinAddr: 0x1DA64, symSize: 0xA0 }
  - { offset: 0x731C5, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) setImageForState:withURLRequest:placeholderImage:success:failure:]', symObjAddr: 0x20C, symBinAddr: 0x1DB04, symSize: 0x308 }
  - { offset: 0x732DF, size: 0x8, addend: 0x0, symName: '___91-[UIButton(AFNetworking) setImageForState:withURLRequest:placeholderImage:success:failure:]_block_invoke', symObjAddr: 0x514, symBinAddr: 0x1DE0C, symSize: 0x110 }
  - { offset: 0x733BB, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40b48w, symObjAddr: 0x624, symBinAddr: 0x1DF1C, symSize: 0x40 }
  - { offset: 0x733E4, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48w, symObjAddr: 0x664, symBinAddr: 0x1DF5C, symSize: 0x30 }
  - { offset: 0x73403, size: 0x8, addend: 0x0, symName: '___91-[UIButton(AFNetworking) setImageForState:withURLRequest:placeholderImage:success:failure:]_block_invoke.12', symObjAddr: 0x694, symBinAddr: 0x1DF8C, symSize: 0xF8 }
  - { offset: 0x734DF, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) setBackgroundImageForState:withURL:]', symObjAddr: 0x78C, symBinAddr: 0x1E084, symSize: 0x8 }
  - { offset: 0x7352C, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) setBackgroundImageForState:withURL:placeholderImage:]', symObjAddr: 0x794, symBinAddr: 0x1E08C, symSize: 0xA0 }
  - { offset: 0x7359F, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) setBackgroundImageForState:withURLRequest:placeholderImage:success:failure:]', symObjAddr: 0x834, symBinAddr: 0x1E12C, symSize: 0x308 }
  - { offset: 0x736B9, size: 0x8, addend: 0x0, symName: '___101-[UIButton(AFNetworking) setBackgroundImageForState:withURLRequest:placeholderImage:success:failure:]_block_invoke', symObjAddr: 0xB3C, symBinAddr: 0x1E434, symSize: 0x110 }
  - { offset: 0x73795, size: 0x8, addend: 0x0, symName: '___101-[UIButton(AFNetworking) setBackgroundImageForState:withURLRequest:placeholderImage:success:failure:]_block_invoke_2', symObjAddr: 0xC4C, symBinAddr: 0x1E544, symSize: 0xF8 }
  - { offset: 0x73877, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) cancelImageDownloadTaskForState:]', symObjAddr: 0xD44, symBinAddr: 0x1E63C, symSize: 0x78 }
  - { offset: 0x738CD, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) cancelBackgroundImageDownloadTaskForState:]', symObjAddr: 0xDBC, symBinAddr: 0x1E6B4, symSize: 0x78 }
  - { offset: 0x73923, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) isActiveTaskURLEqualToURLRequest:forState:]', symObjAddr: 0xE34, symBinAddr: 0x1E72C, symSize: 0x118 }
  - { offset: 0x7398E, size: 0x8, addend: 0x0, symName: '-[UIButton(AFNetworking) isActiveBackgroundTaskURLEqualToURLRequest:forState:]', symObjAddr: 0xF4C, symBinAddr: 0x1E844, symSize: 0x118 }
  - { offset: 0x740F0, size: 0x8, addend: 0x0, symName: '-[UIImageView(_AFNetworking) af_activeImageDownloadReceipt]', symObjAddr: 0x0, symBinAddr: 0x1E95C, symSize: 0xC }
  - { offset: 0x7416A, size: 0x8, addend: 0x0, symName: '-[UIImageView(_AFNetworking) af_activeImageDownloadReceipt]', symObjAddr: 0x0, symBinAddr: 0x1E95C, symSize: 0xC }
  - { offset: 0x741FD, size: 0x8, addend: 0x0, symName: '-[UIImageView(_AFNetworking) af_setActiveImageDownloadReceipt:]', symObjAddr: 0xC, symBinAddr: 0x1E968, symSize: 0x10 }
  - { offset: 0x7427F, size: 0x8, addend: 0x0, symName: '+[UIImageView(AFNetworking) sharedImageDownloader]', symObjAddr: 0x1C, symBinAddr: 0x1E978, symSize: 0x70 }
  - { offset: 0x742C0, size: 0x8, addend: 0x0, symName: '+[UIImageView(AFNetworking) setSharedImageDownloader:]', symObjAddr: 0x8C, symBinAddr: 0x1E9E8, symSize: 0x4C }
  - { offset: 0x7431B, size: 0x8, addend: 0x0, symName: '-[UIImageView(AFNetworking) setImageWithURL:]', symObjAddr: 0xD8, symBinAddr: 0x1EA34, symSize: 0x8 }
  - { offset: 0x7435A, size: 0x8, addend: 0x0, symName: '-[UIImageView(AFNetworking) setImageWithURL:placeholderImage:]', symObjAddr: 0xE0, symBinAddr: 0x1EA3C, symSize: 0x8C }
  - { offset: 0x743BD, size: 0x8, addend: 0x0, symName: '-[UIImageView(AFNetworking) setImageWithURLRequest:placeholderImage:success:failure:]', symObjAddr: 0x16C, symBinAddr: 0x1EAC8, symSize: 0x34C }
  - { offset: 0x7450E, size: 0x8, addend: 0x0, symName: '___85-[UIImageView(AFNetworking) setImageWithURLRequest:placeholderImage:success:failure:]_block_invoke', symObjAddr: 0x4B8, symBinAddr: 0x1EE14, symSize: 0x100 }
  - { offset: 0x745DA, size: 0x8, addend: 0x0, symName: '___85-[UIImageView(AFNetworking) setImageWithURLRequest:placeholderImage:success:failure:]_block_invoke.13', symObjAddr: 0x628, symBinAddr: 0x1EF14, symSize: 0xEC }
  - { offset: 0x746A6, size: 0x8, addend: 0x0, symName: '-[UIImageView(AFNetworking) cancelImageDownloadTask]', symObjAddr: 0x714, symBinAddr: 0x1F000, symSize: 0x98 }
  - { offset: 0x746D9, size: 0x8, addend: 0x0, symName: '-[UIImageView(AFNetworking) clearActiveDownloadInformation]', symObjAddr: 0x7AC, symBinAddr: 0x1F098, symSize: 0x8 }
  - { offset: 0x7470A, size: 0x8, addend: 0x0, symName: '-[UIImageView(AFNetworking) isActiveTaskURLEqualToURLRequest:]', symObjAddr: 0x7B4, symBinAddr: 0x1F0A0, symSize: 0x110 }
  - { offset: 0x74D1E, size: 0x8, addend: 0x0, symName: '-[UIProgressView(AFNetworking) af_uploadProgressAnimated]', symObjAddr: 0x0, symBinAddr: 0x1F1B0, symSize: 0x44 }
  - { offset: 0x74D38, size: 0x8, addend: 0x0, symName: _AFTaskCountOfBytesSentContext, symObjAddr: 0x860, symBinAddr: 0x3BF68, symSize: 0x0 }
  - { offset: 0x74D4F, size: 0x8, addend: 0x0, symName: _AFTaskCountOfBytesReceivedContext, symObjAddr: 0x868, symBinAddr: 0x3BF70, symSize: 0x0 }
  - { offset: 0x74D59, size: 0x8, addend: 0x0, symName: '-[UIProgressView(AFNetworking) af_uploadProgressAnimated]', symObjAddr: 0x0, symBinAddr: 0x1F1B0, symSize: 0x44 }
  - { offset: 0x74DEE, size: 0x8, addend: 0x0, symName: '-[UIProgressView(AFNetworking) af_setUploadProgressAnimated:]', symObjAddr: 0x44, symBinAddr: 0x1F1F4, symSize: 0x5C }
  - { offset: 0x74E79, size: 0x8, addend: 0x0, symName: '-[UIProgressView(AFNetworking) af_downloadProgressAnimated]', symObjAddr: 0xA0, symBinAddr: 0x1F250, symSize: 0x44 }
  - { offset: 0x74EC7, size: 0x8, addend: 0x0, symName: '-[UIProgressView(AFNetworking) af_setDownloadProgressAnimated:]', symObjAddr: 0xE4, symBinAddr: 0x1F294, symSize: 0x5C }
  - { offset: 0x74F34, size: 0x8, addend: 0x0, symName: '-[UIProgressView(AFNetworking) setProgressWithUploadProgressOfTask:animated:]', symObjAddr: 0x140, symBinAddr: 0x1F2F0, symSize: 0x8C }
  - { offset: 0x74F87, size: 0x8, addend: 0x0, symName: '-[UIProgressView(AFNetworking) setProgressWithDownloadProgressOfTask:animated:]', symObjAddr: 0x1CC, symBinAddr: 0x1F37C, symSize: 0x8C }
  - { offset: 0x74FDA, size: 0x8, addend: 0x0, symName: '-[UIProgressView(AFNetworking) observeValueForKeyPath:ofObject:change:context:]', symObjAddr: 0x258, symBinAddr: 0x1F408, symSize: 0x2D8 }
  - { offset: 0x7516A, size: 0x8, addend: 0x0, symName: '___79-[UIProgressView(AFNetworking) observeValueForKeyPath:ofObject:change:context:]_block_invoke', symObjAddr: 0x530, symBinAddr: 0x1F6E0, symSize: 0x54 }
  - { offset: 0x751B9, size: 0x8, addend: 0x0, symName: '___79-[UIProgressView(AFNetworking) observeValueForKeyPath:ofObject:change:context:]_block_invoke.12', symObjAddr: 0x5D4, symBinAddr: 0x1F734, symSize: 0x54 }
  - { offset: 0x753CA, size: 0x8, addend: 0x0, symName: '-[UIRefreshControl(AFNetworking) af_notificationObserver]', symObjAddr: 0x0, symBinAddr: 0x1F788, symSize: 0x74 }
  - { offset: 0x75409, size: 0x8, addend: 0x0, symName: '-[UIRefreshControl(AFNetworking) af_notificationObserver]', symObjAddr: 0x0, symBinAddr: 0x1F788, symSize: 0x74 }
  - { offset: 0x754FD, size: 0x8, addend: 0x0, symName: '-[UIRefreshControl(AFNetworking) setRefreshingWithStateOfTask:]', symObjAddr: 0x74, symBinAddr: 0x1F7FC, symSize: 0x50 }
  - { offset: 0x75540, size: 0x8, addend: 0x0, symName: '-[AFRefreshControlNotificationObserver initWithActivityRefreshControl:]', symObjAddr: 0xC4, symBinAddr: 0x1F84C, symSize: 0x6C }
  - { offset: 0x75587, size: 0x8, addend: 0x0, symName: '-[AFRefreshControlNotificationObserver setRefreshingWithStateOfTask:]', symObjAddr: 0x130, symBinAddr: 0x1F8B8, symSize: 0x154 }
  - { offset: 0x755F9, size: 0x8, addend: 0x0, symName: '-[AFRefreshControlNotificationObserver af_beginRefreshing]', symObjAddr: 0x284, symBinAddr: 0x1FA0C, symSize: 0x58 }
  - { offset: 0x75669, size: 0x8, addend: 0x0, symName: '___58-[AFRefreshControlNotificationObserver af_beginRefreshing]_block_invoke', symObjAddr: 0x2DC, symBinAddr: 0x1FA64, symSize: 0x34 }
  - { offset: 0x756A8, size: 0x8, addend: 0x0, symName: '-[AFRefreshControlNotificationObserver af_endRefreshing]', symObjAddr: 0x320, symBinAddr: 0x1FA98, symSize: 0x58 }
  - { offset: 0x756F1, size: 0x8, addend: 0x0, symName: '___56-[AFRefreshControlNotificationObserver af_endRefreshing]_block_invoke', symObjAddr: 0x378, symBinAddr: 0x1FAF0, symSize: 0x34 }
  - { offset: 0x75730, size: 0x8, addend: 0x0, symName: '-[AFRefreshControlNotificationObserver dealloc]', symObjAddr: 0x3AC, symBinAddr: 0x1FB24, symSize: 0xB0 }
  - { offset: 0x75773, size: 0x8, addend: 0x0, symName: '-[AFRefreshControlNotificationObserver refreshControl]', symObjAddr: 0x45C, symBinAddr: 0x1FBD4, symSize: 0x18 }
  - { offset: 0x757AA, size: 0x8, addend: 0x0, symName: '-[AFRefreshControlNotificationObserver .cxx_destruct]', symObjAddr: 0x474, symBinAddr: 0x1FBEC, symSize: 0x8 }
  - { offset: 0x7598C, size: 0x8, addend: 0x0, symName: '-[WKWebView(_AFNetworking) af_URLSessionTask]', symObjAddr: 0x0, symBinAddr: 0x1FBF4, symSize: 0xC }
  - { offset: 0x7599A, size: 0x8, addend: 0x0, symName: '-[WKWebView(AFNetworking) sessionManager]', symObjAddr: 0x1C, symBinAddr: 0x1FC10, symSize: 0x84 }
  - { offset: 0x759C4, size: 0x8, addend: 0x0, symName: _sessionManager._af_defaultHTTPSessionManager, symObjAddr: 0x6968, symBinAddr: 0x3BFF0, symSize: 0x0 }
  - { offset: 0x759DA, size: 0x8, addend: 0x0, symName: _sessionManager.onceToken, symObjAddr: 0x6970, symBinAddr: 0x3BFF8, symSize: 0x0 }
  - { offset: 0x75D2F, size: 0x8, addend: 0x0, symName: '-[WKWebView(AFNetworking) responseSerializer]', symObjAddr: 0x174, symBinAddr: 0x1FD68, symSize: 0x84 }
  - { offset: 0x75D59, size: 0x8, addend: 0x0, symName: _responseSerializer._af_defaultResponseSerializer, symObjAddr: 0x6978, symBinAddr: 0x3C000, symSize: 0x0 }
  - { offset: 0x75D6F, size: 0x8, addend: 0x0, symName: _responseSerializer.onceToken, symObjAddr: 0x6980, symBinAddr: 0x3C008, symSize: 0x0 }
  - { offset: 0x75DC9, size: 0x8, addend: 0x0, symName: '-[WKWebView(_AFNetworking) af_URLSessionTask]', symObjAddr: 0x0, symBinAddr: 0x1FBF4, symSize: 0xC }
  - { offset: 0x75E33, size: 0x8, addend: 0x0, symName: '-[WKWebView(_AFNetworking) af_setURLSessionTask:]', symObjAddr: 0xC, symBinAddr: 0x1FC00, symSize: 0x10 }
  - { offset: 0x75EFC, size: 0x8, addend: 0x0, symName: '___41-[WKWebView(AFNetworking) sessionManager]_block_invoke', symObjAddr: 0xA0, symBinAddr: 0x1FC94, symSize: 0xC4 }
  - { offset: 0x75F23, size: 0x8, addend: 0x0, symName: '-[WKWebView(AFNetworking) setSessionManager:]', symObjAddr: 0x164, symBinAddr: 0x1FD58, symSize: 0x10 }
  - { offset: 0x75F87, size: 0x8, addend: 0x0, symName: '___45-[WKWebView(AFNetworking) responseSerializer]_block_invoke', symObjAddr: 0x1F8, symBinAddr: 0x1FDEC, symSize: 0x34 }
  - { offset: 0x75FAE, size: 0x8, addend: 0x0, symName: '-[WKWebView(AFNetworking) setResponseSerializer:]', symObjAddr: 0x22C, symBinAddr: 0x1FE20, symSize: 0x10 }
  - { offset: 0x76012, size: 0x8, addend: 0x0, symName: '-[WKWebView(AFNetworking) loadRequest:navigation:progress:success:failure:]', symObjAddr: 0x23C, symBinAddr: 0x1FE30, symSize: 0xB8 }
  - { offset: 0x76095, size: 0x8, addend: 0x0, symName: '___75-[WKWebView(AFNetworking) loadRequest:navigation:progress:success:failure:]_block_invoke', symObjAddr: 0x2F4, symBinAddr: 0x1FEE8, symSize: 0x124 }
  - { offset: 0x761AE, size: 0x8, addend: 0x0, symName: '-[WKWebView(AFNetworking) loadRequest:navigation:MIMEType:textEncodingName:progress:success:failure:]', symObjAddr: 0x430, symBinAddr: 0x2000C, symSize: 0x3A8 }
  - { offset: 0x76296, size: 0x8, addend: 0x0, symName: ___Block_byref_object_copy_, symObjAddr: 0x7D8, symBinAddr: 0x203B4, symSize: 0x10 }
  - { offset: 0x762BB, size: 0x8, addend: 0x0, symName: ___Block_byref_object_dispose_, symObjAddr: 0x7E8, symBinAddr: 0x203C4, symSize: 0x8 }
  - { offset: 0x762DA, size: 0x8, addend: 0x0, symName: '___101-[WKWebView(AFNetworking) loadRequest:navigation:MIMEType:textEncodingName:progress:success:failure:]_block_invoke', symObjAddr: 0x7F0, symBinAddr: 0x203CC, symSize: 0x13C }
  - { offset: 0x763E4, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s48s56s64s72b80b88r, symObjAddr: 0x92C, symBinAddr: 0x20508, symSize: 0x74 }
  - { offset: 0x7640D, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s48s56s64s72s80s88r, symObjAddr: 0x9A0, symBinAddr: 0x2057C, symSize: 0x5C }
...
