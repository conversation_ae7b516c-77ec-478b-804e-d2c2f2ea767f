// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 5.5 effective-4.1.50 (swiftlang-1300.0.31.1 clang-1300.0.29.1)
// swift-module-flags: -target arm64-apple-ios8.0 -enable-objc-interop -enable-library-evolution -swift-version 4 -enforce-exclusivity=checked -O -module-name SwiftyUserDefaults
import Foundation
import Swift
@_exported import SwiftyUserDefaults
import _Concurrency
@_hasMissingDesignatedInitializers open class DefaultsKeys {
  @objc deinit
}
@_hasMissingDesignatedInitializers open class DefaultsKey<ValueType> : SwiftyUserDefaults.DefaultsKeys where ValueType : SwiftyUserDefaults.DefaultsSerializable {
  final public let _key: Swift.String
  final public let defaultValue: ValueType.T?
  public init(_ key: Swift.String, defaultValue: ValueType.T)
  @available(*, unavailable, message: "This key needs a `defaultValue` parameter. If this type does not have a default value, consider using an optional key.")
  public init(_ key: Swift.String)
  @objc deinit
}
extension SwiftyUserDefaults.DefaultsKey where ValueType : SwiftyUserDefaults.OptionalType, ValueType.Wrapped : SwiftyUserDefaults.DefaultsSerializable {
  convenience public init(_ key: Swift.String)
}
open class DefaultsBridge<T> {
  public init()
  open func save(key: Swift.String, value: T?, userDefaults: Foundation.UserDefaults)
  open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> T?
  open func isSerialized() -> Swift.Bool
  open func deserialize(_ object: Any) -> T?
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsObjectBridge<T> : SwiftyUserDefaults.DefaultsBridge<T> {
  override open func save(key: Swift.String, value: T?, userDefaults: Foundation.UserDefaults)
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> T?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsArrayBridge<T> : SwiftyUserDefaults.DefaultsBridge<T> where T : Swift.Collection {
  override open func save(key: Swift.String, value: T?, userDefaults: Foundation.UserDefaults)
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> T?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsStringBridge : SwiftyUserDefaults.DefaultsBridge<Swift.String> {
  override open func save(key: Swift.String, value: Swift.String?, userDefaults: Foundation.UserDefaults)
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> Swift.String?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsIntBridge : SwiftyUserDefaults.DefaultsBridge<Swift.Int> {
  override open func save(key: Swift.String, value: Swift.Int?, userDefaults: Foundation.UserDefaults)
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> Swift.Int?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsDoubleBridge : SwiftyUserDefaults.DefaultsBridge<Swift.Double> {
  override open func save(key: Swift.String, value: Swift.Double?, userDefaults: Foundation.UserDefaults)
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> Swift.Double?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsBoolBridge : SwiftyUserDefaults.DefaultsBridge<Swift.Bool> {
  override open func save(key: Swift.String, value: Swift.Bool?, userDefaults: Foundation.UserDefaults)
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> Swift.Bool?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsDataBridge : SwiftyUserDefaults.DefaultsBridge<Foundation.Data> {
  override open func save(key: Swift.String, value: Foundation.Data?, userDefaults: Foundation.UserDefaults)
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> Foundation.Data?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsUrlBridge : SwiftyUserDefaults.DefaultsBridge<Foundation.URL> {
  override open func save(key: Swift.String, value: Foundation.URL?, userDefaults: Foundation.UserDefaults)
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> Foundation.URL?
  override open func isSerialized() -> Swift.Bool
  override open func deserialize(_ object: Any) -> Foundation.URL?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsCodableBridge<T> : SwiftyUserDefaults.DefaultsBridge<T> where T : Swift.Decodable, T : Swift.Encodable {
  override open func save(key: Swift.String, value: T?, userDefaults: Foundation.UserDefaults)
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> T?
  override open func isSerialized() -> Swift.Bool
  override open func deserialize(_ object: Any) -> T?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsKeyedArchiverBridge<T> : SwiftyUserDefaults.DefaultsBridge<T> {
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> T?
  override open func save(key: Swift.String, value: T?, userDefaults: Foundation.UserDefaults)
  override open func isSerialized() -> Swift.Bool
  override open func deserialize(_ object: Any) -> T?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsRawRepresentableBridge<T> : SwiftyUserDefaults.DefaultsBridge<T> where T : Swift.RawRepresentable {
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> T?
  override open func save(key: Swift.String, value: T?, userDefaults: Foundation.UserDefaults)
  override open func isSerialized() -> Swift.Bool
  override open func deserialize(_ object: Any) -> T?
  override public init()
  @objc deinit
}
@_inheritsConvenienceInitializers open class DefaultsRawRepresentableArrayBridge<T> : SwiftyUserDefaults.DefaultsBridge<T> where T : Swift.Collection, T.Element : Swift.RawRepresentable {
  override open func get(key: Swift.String, userDefaults: Foundation.UserDefaults) -> T?
  override open func save(key: Swift.String, value: T?, userDefaults: Foundation.UserDefaults)
  override open func isSerialized() -> Swift.Bool
  override open func deserialize(_ object: Any) -> T?
  override public init()
  @objc deinit
}
extension Foundation.UserDefaults {
  public subscript<T>(key: SwiftyUserDefaults.DefaultsKey<T?>) -> T.T? where T : SwiftyUserDefaults.DefaultsSerializable {
    get
    set
  }
  public subscript<T>(key: SwiftyUserDefaults.DefaultsKey<T>) -> T.T where T : SwiftyUserDefaults.DefaultsSerializable, T == T.T {
    get
    set
  }
}
extension Foundation.UserDefaults {
  public func observe<T>(key: SwiftyUserDefaults.DefaultsKey<T>, options: Foundation.NSKeyValueObservingOptions = [.old, .new], handler: @escaping (SwiftyUserDefaults.DefaultsObserver<T>.Update) -> Swift.Void) -> SwiftyUserDefaults.DefaultsDisposable where T : SwiftyUserDefaults.DefaultsSerializable
}
public protocol DefaultsDisposable {
  func dispose()
}
@_hasMissingDesignatedInitializers final public class DefaultsObserver<T> : ObjectiveC.NSObject, SwiftyUserDefaults.DefaultsDisposable where T : SwiftyUserDefaults.DefaultsSerializable {
  public struct Update {
    public let kind: Foundation.NSKeyValueChange
    public let indexes: Foundation.IndexSet?
    public let isPrior: Swift.Bool
    public let newValue: T.T?
    public let oldValue: T.T?
  }
  @objc deinit
  @objc override final public func observeValue(forKeyPath keyPath: Swift.String?, of object: Any?, change: [Foundation.NSKeyValueChangeKey : Any]?, context: Swift.UnsafeMutableRawPointer?)
  final public func dispose()
}
extension Foundation.Date : SwiftyUserDefaults.DefaultsSerializable {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Foundation.Date> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Foundation.Date]> {
    get
  }
  public typealias T = Foundation.Date
}
extension Swift.String : SwiftyUserDefaults.DefaultsSerializable {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Swift.String> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Swift.String]> {
    get
  }
  public typealias T = Swift.String
}
extension Swift.Int : SwiftyUserDefaults.DefaultsSerializable {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Swift.Int> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Swift.Int]> {
    get
  }
  public typealias T = Swift.Int
}
extension Swift.Double : SwiftyUserDefaults.DefaultsSerializable {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Swift.Double> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Swift.Double]> {
    get
  }
  public typealias T = Swift.Double
}
extension Swift.Bool : SwiftyUserDefaults.DefaultsSerializable {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Swift.Bool> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Swift.Bool]> {
    get
  }
  public typealias T = Swift.Bool
}
extension Foundation.Data : SwiftyUserDefaults.DefaultsSerializable {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Foundation.Data> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Foundation.Data]> {
    get
  }
  public typealias T = Foundation.Data
}
extension Foundation.URL : SwiftyUserDefaults.DefaultsSerializable {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Foundation.URL> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Foundation.URL]> {
    get
  }
  public typealias T = Foundation.URL
}
extension SwiftyUserDefaults.DefaultsSerializable where Self : Swift.Decodable, Self : Swift.Encodable {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Self> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Self]> {
    get
  }
}
extension SwiftyUserDefaults.DefaultsSerializable where Self : Swift.RawRepresentable {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Self> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Self]> {
    get
  }
}
extension SwiftyUserDefaults.DefaultsSerializable where Self : Foundation.NSCoding {
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Self> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Self]> {
    get
  }
}
extension Swift.Dictionary : SwiftyUserDefaults.DefaultsSerializable where Key == Swift.String {
  public typealias T = [Key : Value]
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<[Swift.String : Value]> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[[Swift.String : Value]]> {
    get
  }
}
extension Swift.Array : SwiftyUserDefaults.DefaultsSerializable where Element : SwiftyUserDefaults.DefaultsSerializable {
  public typealias T = [Element]
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<[Element]> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[[Element]]> {
    get
  }
}
extension Swift.Optional : SwiftyUserDefaults.DefaultsSerializable where Wrapped : SwiftyUserDefaults.DefaultsSerializable {
  public typealias T = Wrapped
  public static var _defaults: SwiftyUserDefaults.DefaultsBridge<Wrapped> {
    get
  }
  public static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Wrapped]> {
    get
  }
}
public protocol OptionalType {
  associatedtype Wrapped
  var wrapped: Self.Wrapped? { get }
}
extension Swift.Optional : SwiftyUserDefaults.OptionalType {
  public var wrapped: Wrapped? {
    get
  }
}
public protocol DefaultsSerializable {
  associatedtype T
  static var _defaults: SwiftyUserDefaults.DefaultsBridge<Self.T> { get }
  static var _defaultsArray: SwiftyUserDefaults.DefaultsBridge<[Self.T]> { get }
}
public let Defaults: Foundation.UserDefaults
extension Foundation.UserDefaults {
  public func hasKey<T>(_ key: SwiftyUserDefaults.DefaultsKey<T>) -> Swift.Bool where T : SwiftyUserDefaults.DefaultsSerializable
  public func remove<T>(_ key: SwiftyUserDefaults.DefaultsKey<T>) where T : SwiftyUserDefaults.DefaultsSerializable
  public func removeAll()
}
