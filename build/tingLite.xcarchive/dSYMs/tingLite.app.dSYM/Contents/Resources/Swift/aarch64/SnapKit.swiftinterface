// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 5.5 effective-4.2 (swiftlang-1300.0.31.1 clang-1300.0.29.1)
// swift-module-flags: -target arm64-apple-ios8.0 -enable-objc-interop -enable-library-evolution -swift-version 4.2 -enforce-exclusivity=checked -O -module-name SnapKit
import Foundation
@_exported import SnapKit
import Swift
import UIKit
import _Concurrency
public typealias ConstraintInsets = UIKit.UIEdgeInsets
@_hasMissingDesignatedInitializers public class ConstraintMakerFinalizable {
  @discardableResult
  public func labeled(_ label: Swift.String) -> SnapKit.ConstraintMakerFinalizable
  public var constraint: SnapKit.Constraint {
    get
  }
  @objc deinit
}
public protocol ConstraintConstantTarget {
}
extension CoreGraphics.CGPoint : SnapKit.ConstraintConstantTarget {
}
extension CoreGraphics.CGSize : SnapKit.ConstraintConstantTarget {
}
extension UIKit.UIEdgeInsets : SnapKit.ConstraintConstantTarget {
}
extension UIKit.UIView {
  @_Concurrency.MainActor(unsafe) public var snp_left: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_top: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_right: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_bottom: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_leading: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_trailing: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_width: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_height: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_centerX: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_centerY: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_baseline: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, macOS 10.11, *)
  @_Concurrency.MainActor(unsafe) public var snp_lastBaseline: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, macOS 10.11, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_firstBaseline: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_leftMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_topMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_rightMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_bottomMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_leadingMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_trailingMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_centerXWithinMargins: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_centerYWithinMargins: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_edges: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_size: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public var snp_center: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_margins: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  @available(iOS, deprecated: 3.0, message: "Use newer snp.* syntax.")
  @_Concurrency.MainActor(unsafe) public var snp_centerWithinMargins: SnapKit.ConstraintItem {
    get
  }
  @_Concurrency.MainActor(unsafe) public func snp_prepareConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void) -> [SnapKit.Constraint]
  @_Concurrency.MainActor(unsafe) public func snp_makeConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void)
  @_Concurrency.MainActor(unsafe) public func snp_remakeConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void)
  @_Concurrency.MainActor(unsafe) public func snp_updateConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void)
  @_Concurrency.MainActor(unsafe) public func snp_removeConstraints()
  @_Concurrency.MainActor(unsafe) public var snp: SnapKit.ConstraintViewDSL {
    get
  }
}
@_hasMissingDesignatedInitializers public class ConstraintMakerRelatable {
  @discardableResult
  public func equalTo(_ other: SnapKit.ConstraintRelatableTarget, _ file: Swift.String = #file, _ line: Swift.UInt = #line) -> SnapKit.ConstraintMakerEditable
  @discardableResult
  public func equalToSuperview(_ file: Swift.String = #file, _ line: Swift.UInt = #line) -> SnapKit.ConstraintMakerEditable
  @discardableResult
  public func lessThanOrEqualTo(_ other: SnapKit.ConstraintRelatableTarget, _ file: Swift.String = #file, _ line: Swift.UInt = #line) -> SnapKit.ConstraintMakerEditable
  @discardableResult
  public func lessThanOrEqualToSuperview(_ file: Swift.String = #file, _ line: Swift.UInt = #line) -> SnapKit.ConstraintMakerEditable
  @discardableResult
  public func greaterThanOrEqualTo(_ other: SnapKit.ConstraintRelatableTarget, _ file: Swift.String = #file, line: Swift.UInt = #line) -> SnapKit.ConstraintMakerEditable
  @discardableResult
  public func greaterThanOrEqualToSuperview(_ file: Swift.String = #file, line: Swift.UInt = #line) -> SnapKit.ConstraintMakerEditable
  @objc deinit
}
public protocol ConstraintOffsetTarget : SnapKit.ConstraintConstantTarget {
}
extension Swift.Int : SnapKit.ConstraintOffsetTarget {
}
extension Swift.UInt : SnapKit.ConstraintOffsetTarget {
}
extension Swift.Float : SnapKit.ConstraintOffsetTarget {
}
extension Swift.Double : SnapKit.ConstraintOffsetTarget {
}
extension CoreGraphics.CGFloat : SnapKit.ConstraintOffsetTarget {
}
@available(iOS 9.0, macOS 10.11, *)
public struct ConstraintLayoutGuideDSL : SnapKit.ConstraintAttributesDSL {
  @discardableResult
  public func prepareConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void) -> [SnapKit.Constraint]
  public func makeConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void)
  public func remakeConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void)
  public func updateConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void)
  public func removeConstraints()
  public var target: Swift.AnyObject? {
    get
  }
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers public class ConstraintMakerEditable : SnapKit.ConstraintMakerPriortizable {
  @discardableResult
  public func multipliedBy(_ amount: SnapKit.ConstraintMultiplierTarget) -> SnapKit.ConstraintMakerEditable
  @discardableResult
  public func dividedBy(_ amount: SnapKit.ConstraintMultiplierTarget) -> SnapKit.ConstraintMakerEditable
  @discardableResult
  public func offset(_ amount: SnapKit.ConstraintOffsetTarget) -> SnapKit.ConstraintMakerEditable
  @discardableResult
  public func inset(_ amount: SnapKit.ConstraintInsetTarget) -> SnapKit.ConstraintMakerEditable
  @objc deinit
}
@available(iOS 9.0, *)
public typealias ConstraintLayoutGuide = UIKit.UILayoutGuide
@_hasMissingDesignatedInitializers public class ConstraintMaker {
  public var left: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var top: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var bottom: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var right: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var leading: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var trailing: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var width: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var height: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var centerX: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var centerY: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var baseline: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var lastBaseline: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, macOS 10.11, *)
  public var firstBaseline: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var leftMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var rightMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var topMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var bottomMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var leadingMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var trailingMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var centerXWithinMargins: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var centerYWithinMargins: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var edges: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var size: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var center: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var margins: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var centerWithinMargins: SnapKit.ConstraintMakerExtendable {
    get
  }
  @objc deinit
}
public struct ConstraintPriority : Swift.ExpressibleByFloatLiteral, Swift.Equatable, Swift.Strideable {
  public typealias FloatLiteralType = Swift.Float
  public let value: Swift.Float
  public init(floatLiteral value: Swift.Float)
  public init(_ value: Swift.Float)
  public static var required: SnapKit.ConstraintPriority {
    get
  }
  public static var high: SnapKit.ConstraintPriority {
    get
  }
  public static var medium: SnapKit.ConstraintPriority {
    get
  }
  public static var low: SnapKit.ConstraintPriority {
    get
  }
  public static func == (lhs: SnapKit.ConstraintPriority, rhs: SnapKit.ConstraintPriority) -> Swift.Bool
  public func advanced(by n: SnapKit.ConstraintPriority.FloatLiteralType) -> SnapKit.ConstraintPriority
  public func distance(to other: SnapKit.ConstraintPriority) -> SnapKit.ConstraintPriority.FloatLiteralType
  public typealias Stride = SnapKit.ConstraintPriority.FloatLiteralType
}
public protocol ConstraintRelatableTarget {
}
extension Swift.Int : SnapKit.ConstraintRelatableTarget {
}
extension Swift.UInt : SnapKit.ConstraintRelatableTarget {
}
extension Swift.Float : SnapKit.ConstraintRelatableTarget {
}
extension Swift.Double : SnapKit.ConstraintRelatableTarget {
}
extension CoreGraphics.CGFloat : SnapKit.ConstraintRelatableTarget {
}
extension CoreGraphics.CGSize : SnapKit.ConstraintRelatableTarget {
}
extension CoreGraphics.CGPoint : SnapKit.ConstraintRelatableTarget {
}
extension UIKit.UIEdgeInsets : SnapKit.ConstraintRelatableTarget {
}
extension SnapKit.ConstraintItem : SnapKit.ConstraintRelatableTarget {
}
extension UIKit.UIView : SnapKit.ConstraintRelatableTarget {
}
@available(iOS 9.0, macOS 10.11, *)
extension UIKit.UILayoutGuide : SnapKit.ConstraintRelatableTarget {
}
public protocol ConstraintDSL {
  var target: Swift.AnyObject? { get }
  func setLabel(_ value: Swift.String?)
  func label() -> Swift.String?
}
extension SnapKit.ConstraintDSL {
  public func setLabel(_ value: Swift.String?)
  public func label() -> Swift.String?
}
public protocol ConstraintBasicAttributesDSL : SnapKit.ConstraintDSL {
}
extension SnapKit.ConstraintBasicAttributesDSL {
  public var left: SnapKit.ConstraintItem {
    get
  }
  public var top: SnapKit.ConstraintItem {
    get
  }
  public var right: SnapKit.ConstraintItem {
    get
  }
  public var bottom: SnapKit.ConstraintItem {
    get
  }
  public var leading: SnapKit.ConstraintItem {
    get
  }
  public var trailing: SnapKit.ConstraintItem {
    get
  }
  public var width: SnapKit.ConstraintItem {
    get
  }
  public var height: SnapKit.ConstraintItem {
    get
  }
  public var centerX: SnapKit.ConstraintItem {
    get
  }
  public var centerY: SnapKit.ConstraintItem {
    get
  }
  public var edges: SnapKit.ConstraintItem {
    get
  }
  public var size: SnapKit.ConstraintItem {
    get
  }
  public var center: SnapKit.ConstraintItem {
    get
  }
}
public protocol ConstraintAttributesDSL : SnapKit.ConstraintBasicAttributesDSL {
}
extension SnapKit.ConstraintAttributesDSL {
  public var baseline: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, macOS 10.11, *)
  public var lastBaseline: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, macOS 10.11, *)
  public var firstBaseline: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var leftMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var topMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var rightMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var bottomMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var leadingMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var trailingMargin: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var centerXWithinMargins: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var centerYWithinMargins: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var margins: SnapKit.ConstraintItem {
    get
  }
  @available(iOS 8.0, *)
  public var centerWithinMargins: SnapKit.ConstraintItem {
    get
  }
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers public class ConstraintMakerExtendable : SnapKit.ConstraintMakerRelatable {
  public var left: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var top: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var bottom: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var right: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var leading: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var trailing: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var width: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var height: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var centerX: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var centerY: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var baseline: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var lastBaseline: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, macOS 10.11, *)
  public var firstBaseline: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var leftMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var rightMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var topMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var bottomMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var leadingMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var trailingMargin: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var centerXWithinMargins: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var centerYWithinMargins: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var edges: SnapKit.ConstraintMakerExtendable {
    get
  }
  public var size: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var margins: SnapKit.ConstraintMakerExtendable {
    get
  }
  @available(iOS 8.0, *)
  public var centerWithinMargins: SnapKit.ConstraintMakerExtendable {
    get
  }
  @objc deinit
}
@_hasMissingDesignatedInitializers final public class ConstraintItem {
  @objc deinit
}
public func == (lhs: SnapKit.ConstraintItem, rhs: SnapKit.ConstraintItem) -> Swift.Bool
public protocol ConstraintPriorityTarget {
  var constraintPriorityTargetValue: Swift.Float { get }
}
extension Swift.Int : SnapKit.ConstraintPriorityTarget {
  public var constraintPriorityTargetValue: Swift.Float {
    get
  }
}
extension Swift.UInt : SnapKit.ConstraintPriorityTarget {
  public var constraintPriorityTargetValue: Swift.Float {
    get
  }
}
extension Swift.Float : SnapKit.ConstraintPriorityTarget {
  public var constraintPriorityTargetValue: Swift.Float {
    get
  }
}
extension Swift.Double : SnapKit.ConstraintPriorityTarget {
  public var constraintPriorityTargetValue: Swift.Float {
    get
  }
}
extension CoreGraphics.CGFloat : SnapKit.ConstraintPriorityTarget {
  public var constraintPriorityTargetValue: Swift.Float {
    get
  }
}
extension UIKit.UILayoutPriority : SnapKit.ConstraintPriorityTarget {
  public var constraintPriorityTargetValue: Swift.Float {
    get
  }
}
@available(iOS 8.0, *)
public typealias ConstraintLayoutSupport = UIKit.UILayoutSupport
@available(iOS 9.0, macOS 10.11, *)
extension UIKit.UILayoutGuide {
  @_Concurrency.MainActor(unsafe) public var snp: SnapKit.ConstraintLayoutGuideDSL {
    get
  }
}
public typealias ConstraintInterfaceLayoutDirection = UIKit.UIUserInterfaceLayoutDirection
public struct ConstraintConfig {
  public static var interfaceLayoutDirection: SnapKit.ConstraintInterfaceLayoutDirection
}
@_hasMissingDesignatedInitializers final public class Constraint {
  final public var layoutConstraints: [SnapKit.LayoutConstraint]
  final public var isActive: Swift.Bool {
    get
    set
  }
  final public func install()
  final public func uninstall()
  final public func activate()
  final public func deactivate()
  @discardableResult
  final public func update(offset: SnapKit.ConstraintOffsetTarget) -> SnapKit.Constraint
  @discardableResult
  final public func update(inset: SnapKit.ConstraintInsetTarget) -> SnapKit.Constraint
  @discardableResult
  final public func update(priority: SnapKit.ConstraintPriorityTarget) -> SnapKit.Constraint
  @discardableResult
  final public func update(priority: SnapKit.ConstraintPriority) -> SnapKit.Constraint
  final public func updateOffset(amount: SnapKit.ConstraintOffsetTarget)
  final public func updateInsets(amount: SnapKit.ConstraintInsetTarget)
  final public func updatePriority(amount: SnapKit.ConstraintPriorityTarget)
  final public func updatePriorityRequired()
  final public func updatePriorityHigh()
  final public func updatePriorityMedium()
  final public func updatePriorityLow()
  @objc deinit
}
public typealias ConstraintView = UIKit.UIView
public protocol LayoutConstraintItem : AnyObject {
}
@available(iOS 9.0, macOS 10.11, *)
extension UIKit.UILayoutGuide : SnapKit.LayoutConstraintItem {
}
extension UIKit.UIView : SnapKit.LayoutConstraintItem {
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers public class ConstraintMakerPriortizable : SnapKit.ConstraintMakerFinalizable {
  @discardableResult
  public func priority(_ amount: SnapKit.ConstraintPriority) -> SnapKit.ConstraintMakerFinalizable
  @discardableResult
  public func priority(_ amount: SnapKit.ConstraintPriorityTarget) -> SnapKit.ConstraintMakerFinalizable
  @discardableResult
  public func priorityRequired() -> SnapKit.ConstraintMakerFinalizable
  @discardableResult
  public func priorityHigh() -> SnapKit.ConstraintMakerFinalizable
  @discardableResult
  public func priorityMedium() -> SnapKit.ConstraintMakerFinalizable
  @discardableResult
  public func priorityLow() -> SnapKit.ConstraintMakerFinalizable
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @_Concurrency.MainActor(unsafe) public class LayoutConstraint : UIKit.NSLayoutConstraint {
  @_Concurrency.MainActor(unsafe) public var label: Swift.String? {
    get
    set
  }
  @objc override dynamic public init()
  @objc deinit
}
public protocol ConstraintInsetTarget : SnapKit.ConstraintConstantTarget {
}
extension Swift.Int : SnapKit.ConstraintInsetTarget {
}
extension Swift.UInt : SnapKit.ConstraintInsetTarget {
}
extension Swift.Float : SnapKit.ConstraintInsetTarget {
}
extension Swift.Double : SnapKit.ConstraintInsetTarget {
}
extension CoreGraphics.CGFloat : SnapKit.ConstraintInsetTarget {
}
extension UIKit.UIEdgeInsets : SnapKit.ConstraintInsetTarget {
}
public protocol ConstraintMultiplierTarget {
  var constraintMultiplierTargetValue: CoreGraphics.CGFloat { get }
}
extension Swift.Int : SnapKit.ConstraintMultiplierTarget {
  public var constraintMultiplierTargetValue: CoreGraphics.CGFloat {
    get
  }
}
extension Swift.UInt : SnapKit.ConstraintMultiplierTarget {
  public var constraintMultiplierTargetValue: CoreGraphics.CGFloat {
    get
  }
}
extension Swift.Float : SnapKit.ConstraintMultiplierTarget {
  public var constraintMultiplierTargetValue: CoreGraphics.CGFloat {
    get
  }
}
extension Swift.Double : SnapKit.ConstraintMultiplierTarget {
  public var constraintMultiplierTargetValue: CoreGraphics.CGFloat {
    get
  }
}
extension CoreGraphics.CGFloat : SnapKit.ConstraintMultiplierTarget {
  public var constraintMultiplierTargetValue: CoreGraphics.CGFloat {
    get
  }
}
public struct ConstraintViewDSL : SnapKit.ConstraintAttributesDSL {
  @discardableResult
  public func prepareConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void) -> [SnapKit.Constraint]
  public func makeConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void)
  public func remakeConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void)
  public func updateConstraints(_ closure: (_ make: SnapKit.ConstraintMaker) -> Swift.Void)
  public func removeConstraints()
  public var contentHuggingHorizontalPriority: Swift.Float {
    get
    set
  }
  public var contentHuggingVerticalPriority: Swift.Float {
    get
    set
  }
  public var contentCompressionResistanceHorizontalPriority: Swift.Float {
    get
    set
  }
  public var contentCompressionResistanceVerticalPriority: Swift.Float {
    get
    set
  }
  public var target: Swift.AnyObject? {
    get
  }
}
extension SnapKit.LayoutConstraint {
  @objc override dynamic public var description: Swift.String {
    @objc get
  }
}
@available(iOS 8.0, *)
public struct ConstraintLayoutSupportDSL : SnapKit.ConstraintDSL {
  public var target: Swift.AnyObject? {
    get
  }
  public var top: SnapKit.ConstraintItem {
    get
  }
  public var bottom: SnapKit.ConstraintItem {
    get
  }
  public var height: SnapKit.ConstraintItem {
    get
  }
}
@available(iOS 8.0, *)
extension UIKit.UILayoutSupport {
  @_Concurrency.MainActor(unsafe) public var snp: SnapKit.ConstraintLayoutSupportDSL {
    get
  }
}
@_hasMissingDesignatedInitializers public class ConstraintDescription {
  @objc deinit
}
