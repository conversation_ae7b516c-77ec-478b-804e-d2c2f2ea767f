// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 5.5 (swiftlang-1300.0.31.1 clang-1300.0.29.1)
// swift-module-flags: -target arm64-apple-ios9.0 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -module-name RxCocoa
import CoreGraphics
import Dispatch
import Foundation
@_exported import RxCocoa
@_exported import RxRelay
import RxSwift
import Swift
import UIKit
import WebKit
import _Concurrency
extension RxSwift.Reactive where Base : UIKit.UISlider {
  public var value: RxCocoa.ControlProperty<Swift.Float> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UIScrollView {
  public typealias EndZoomEvent = (view: UIKit.UIView?, scale: CoreGraphics.CGFloat)
  public typealias WillEndDraggingEvent = (velocity: CoreGraphics.CGPoint, targetContentOffset: Swift.UnsafeMutablePointer<CoreGraphics.CGPoint>)
  public var delegate: RxCocoa.DelegateProxy<UIKit.UIScrollView, UIKit.UIScrollViewDelegate> {
    get
  }
  public var contentOffset: RxCocoa.ControlProperty<CoreGraphics.CGPoint> {
    get
  }
  public var didScroll: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var willBeginDecelerating: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var didEndDecelerating: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var willBeginDragging: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var willEndDragging: RxCocoa.ControlEvent<RxSwift.Reactive<Base>.WillEndDraggingEvent> {
    get
  }
  public var didEndDragging: RxCocoa.ControlEvent<Swift.Bool> {
    get
  }
  public var didZoom: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var didScrollToTop: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var didEndScrollingAnimation: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var willBeginZooming: RxCocoa.ControlEvent<UIKit.UIView?> {
    get
  }
  public var didEndZooming: RxCocoa.ControlEvent<RxSwift.Reactive<Base>.EndZoomEvent> {
    get
  }
  public func setDelegate(_ delegate: UIKit.UIScrollViewDelegate) -> RxSwift.Disposable
}
extension RxSwift.Reactive where Base : UIKit.UIPickerView {
  public var delegate: RxCocoa.DelegateProxy<UIKit.UIPickerView, UIKit.UIPickerViewDelegate> {
    get
  }
  public func setDelegate(_ delegate: UIKit.UIPickerViewDelegate) -> RxSwift.Disposable
  public var dataSource: RxCocoa.DelegateProxy<UIKit.UIPickerView, UIKit.UIPickerViewDataSource> {
    get
  }
  public var itemSelected: RxCocoa.ControlEvent<(row: Swift.Int, component: Swift.Int)> {
    get
  }
  public func modelSelected<T>(_ modelType: T.Type) -> RxCocoa.ControlEvent<[T]>
  public func itemTitles<Sequence, Source>(_ source: Source) -> (_ titleForRow: @escaping (Swift.Int, Sequence.Element) -> Swift.String?) -> RxSwift.Disposable where Sequence : Swift.Sequence, Sequence == Source.Element, Source : RxSwift.ObservableType
  public func itemAttributedTitles<Sequence, Source>(_ source: Source) -> (_ attributedTitleForRow: @escaping (Swift.Int, Sequence.Element) -> Foundation.NSAttributedString?) -> RxSwift.Disposable where Sequence : Swift.Sequence, Sequence == Source.Element, Source : RxSwift.ObservableType
  public func items<Sequence, Source>(_ source: Source) -> (_ viewForRow: @escaping (Swift.Int, Sequence.Element, UIKit.UIView?) -> UIKit.UIView) -> RxSwift.Disposable where Sequence : Swift.Sequence, Sequence == Source.Element, Source : RxSwift.ObservableType
  public func items<Source, Adapter>(adapter: Adapter) -> (_ source: Source) -> RxSwift.Disposable where Source : RxSwift.ObservableType, Adapter : RxCocoa.RxPickerViewDataSourceType, Adapter : UIKit.UIPickerViewDataSource, Adapter : UIKit.UIPickerViewDelegate, Source.Element == Adapter.Element
  public func model<T>(at indexPath: Foundation.IndexPath) throws -> T
}
public protocol DelegateProxyType : AnyObject {
  associatedtype ParentObject : AnyObject
  associatedtype Delegate
  static func registerKnownImplementations()
  static var identifier: Swift.UnsafeRawPointer { get }
  static func currentDelegate(for object: Self.ParentObject) -> Self.Delegate?
  static func setCurrentDelegate(_ delegate: Self.Delegate?, to object: Self.ParentObject)
  func forwardToDelegate() -> Self.Delegate?
  func setForwardToDelegate(_ forwardToDelegate: Self.Delegate?, retainDelegate: Swift.Bool)
}
extension RxCocoa.DelegateProxyType {
  public static var identifier: Swift.UnsafeRawPointer {
    get
  }
}
extension RxCocoa.DelegateProxyType {
  public static func register<Parent>(make: @escaping (Parent) -> Self)
  public static func createProxy(for object: Swift.AnyObject) -> Self
  public static func proxy(for object: Self.ParentObject) -> Self
  public static func installForwardDelegate(_ forwardDelegate: Self.Delegate, retainDelegate: Swift.Bool, onProxyForObject object: Self.ParentObject) -> RxSwift.Disposable
}
public protocol HasDelegate : AnyObject {
  associatedtype Delegate
  var delegate: Self.Delegate? { get set }
}
extension RxCocoa.DelegateProxyType where Self.Delegate == Self.ParentObject.Delegate, Self.ParentObject : RxCocoa.HasDelegate {
  public static func currentDelegate(for object: Self.ParentObject) -> Self.Delegate?
  public static func setCurrentDelegate(_ delegate: Self.Delegate?, to object: Self.ParentObject)
}
public protocol HasDataSource : AnyObject {
  associatedtype DataSource
  var dataSource: Self.DataSource? { get set }
}
extension RxCocoa.DelegateProxyType where Self.Delegate == Self.ParentObject.DataSource, Self.ParentObject : RxCocoa.HasDataSource {
  public static func currentDelegate(for object: Self.ParentObject) -> Self.Delegate?
  public static func setCurrentDelegate(_ delegate: Self.Delegate?, to object: Self.ParentObject)
}
@available(iOS 10.0, tvOS 10.0, *)
public protocol HasPrefetchDataSource : AnyObject {
  associatedtype PrefetchDataSource
  var prefetchDataSource: Self.PrefetchDataSource? { get set }
}
@available(iOS 10.0, tvOS 10.0, *)
extension RxCocoa.DelegateProxyType where Self.Delegate == Self.ParentObject.PrefetchDataSource, Self.ParentObject : RxCocoa.HasPrefetchDataSource {
  public static func currentDelegate(for object: Self.ParentObject) -> Self.Delegate?
  public static func setCurrentDelegate(_ delegate: Self.Delegate?, to object: Self.ParentObject)
}
extension RxSwift.Reactive where Base : UIKit.UITextView {
  public var text: RxCocoa.ControlProperty<Swift.String?> {
    get
  }
  public var value: RxCocoa.ControlProperty<Swift.String?> {
    get
  }
  public var attributedText: RxCocoa.ControlProperty<Foundation.NSAttributedString?> {
    get
  }
  public var didBeginEditing: RxCocoa.ControlEvent<()> {
    get
  }
  public var didEndEditing: RxCocoa.ControlEvent<()> {
    get
  }
  public var didChange: RxCocoa.ControlEvent<()> {
    get
  }
  public var didChangeSelection: RxCocoa.ControlEvent<()> {
    get
  }
}
extension RxRelay.BehaviorRelay {
  final public func asDriver() -> RxCocoa.Driver<Element>
}
extension UIKit.UICollectionView : RxCocoa.HasDataSource {
  public typealias DataSource = UIKit.UICollectionViewDataSource
}
@_Concurrency.MainActor(unsafe) open class RxCollectionViewDataSourceProxy : RxCocoa.DelegateProxy<UIKit.UICollectionView, UIKit.UICollectionViewDataSource>, RxCocoa.DelegateProxyType, UIKit.UICollectionViewDataSource {
  @_Concurrency.MainActor(unsafe) weak public var collectionView: UIKit.UICollectionView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(collectionView: RxCocoa.RxCollectionViewDataSourceProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @_Concurrency.MainActor(unsafe) @objc public func collectionView(_ collectionView: UIKit.UICollectionView, numberOfItemsInSection section: Swift.Int) -> Swift.Int
  @_Concurrency.MainActor(unsafe) @objc public func collectionView(_ collectionView: UIKit.UICollectionView, cellForItemAt indexPath: Foundation.IndexPath) -> UIKit.UICollectionViewCell
  override open func setForwardToDelegate(_ forwardToDelegate: UIKit.UICollectionViewDataSource?, retainDelegate: Swift.Bool)
  @objc deinit
}
public enum SharingScheduler {
  public static var make: () -> RxSwift.SchedulerType {
    get
  }
  public static func mock(scheduler: RxSwift.SchedulerType, action: () throws -> Swift.Void) rethrows
  public static func mock(makeScheduler: @escaping () -> RxSwift.SchedulerType, action: () throws -> Swift.Void) rethrows
}
extension UIKit.NSTextStorage : RxCocoa.HasDelegate {
  public typealias Delegate = UIKit.NSTextStorageDelegate
}
open class RxTextStorageDelegateProxy : RxCocoa.DelegateProxy<UIKit.NSTextStorage, UIKit.NSTextStorageDelegate>, RxCocoa.DelegateProxyType, UIKit.NSTextStorageDelegate {
  weak public var textStorage: UIKit.NSTextStorage? {
    get
  }
  public init(textStorage: UIKit.NSTextStorage)
  public static func registerKnownImplementations()
  @objc deinit
}
public typealias Driver<Element> = RxCocoa.SharedSequence<RxCocoa.DriverSharingStrategy, Element>
public struct DriverSharingStrategy : RxCocoa.SharingStrategyProtocol {
  public static var scheduler: RxSwift.SchedulerType {
    get
  }
  public static func share<Element>(_ source: RxSwift.Observable<Element>) -> RxSwift.Observable<Element>
}
extension RxCocoa.SharedSequenceConvertibleType where Self.SharingStrategy == RxCocoa.DriverSharingStrategy {
  public func asDriver() -> RxCocoa.Driver<Self.Element>
}
@_Concurrency.MainActor(unsafe) open class RxTableViewDelegateProxy : RxCocoa.RxScrollViewDelegateProxy, UIKit.UITableViewDelegate {
  @_Concurrency.MainActor(unsafe) weak public var tableView: UIKit.UITableView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(tableView: UIKit.UITableView)
  @objc deinit
}
extension RxSwift.Reactive where Base : UIKit.UIRefreshControl {
  public var isRefreshing: RxSwift.Binder<Swift.Bool> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UIStepper {
  public var value: RxCocoa.ControlProperty<Swift.Double> {
    get
  }
}
extension RxSwift.ObservableConvertibleType {
  public func asSignal(onErrorJustReturn: Self.Element) -> RxCocoa.Signal<Self.Element>
  public func asSignal(onErrorSignalWith: RxCocoa.Signal<Self.Element>) -> RxCocoa.Signal<Self.Element>
  public func asSignal(onErrorRecover: @escaping (_ error: Swift.Error) -> RxCocoa.Signal<Self.Element>) -> RxCocoa.Signal<Self.Element>
}
public protocol ControlEventType : RxSwift.ObservableType {
  func asControlEvent() -> RxCocoa.ControlEvent<Self.Element>
}
public struct ControlEvent<PropertyType> : RxCocoa.ControlEventType {
  public typealias Element = PropertyType
  public init<Ev>(events: Ev) where PropertyType == Ev.Element, Ev : RxSwift.ObservableType
  public func subscribe<Observer>(_ observer: Observer) -> RxSwift.Disposable where PropertyType == Observer.Element, Observer : RxSwift.ObserverType
  public func asObservable() -> RxSwift.Observable<RxCocoa.ControlEvent<PropertyType>.Element>
  public func asControlEvent() -> RxCocoa.ControlEvent<RxCocoa.ControlEvent<PropertyType>.Element>
}
extension RxCocoa.ControlEvent {
  public func asSignal() -> RxCocoa.Signal<RxCocoa.ControlEvent<PropertyType>.Element>
}
extension UIKit.UIPickerView : RxCocoa.HasDataSource {
  public typealias DataSource = UIKit.UIPickerViewDataSource
}
@_Concurrency.MainActor(unsafe) public class RxPickerViewDataSourceProxy : RxCocoa.DelegateProxy<UIKit.UIPickerView, UIKit.UIPickerViewDataSource>, RxCocoa.DelegateProxyType, UIKit.UIPickerViewDataSource {
  @_Concurrency.MainActor(unsafe) weak public var pickerView: UIKit.UIPickerView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(pickerView: RxCocoa.RxPickerViewDataSourceProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @_Concurrency.MainActor(unsafe) @objc public func numberOfComponents(in pickerView: UIKit.UIPickerView) -> Swift.Int
  @_Concurrency.MainActor(unsafe) @objc public func pickerView(_ pickerView: UIKit.UIPickerView, numberOfRowsInComponent component: Swift.Int) -> Swift.Int
  override public func setForwardToDelegate(_ forwardToDelegate: UIKit.UIPickerViewDataSource?, retainDelegate: Swift.Bool)
  @objc deinit
}
extension RxSwift.Reactive where Base : UIKit.UIControl {
  public func controlEvent(_ controlEvents: UIKit.UIControl.Event) -> RxCocoa.ControlEvent<()>
  public func controlProperty<T>(editingEvents: UIKit.UIControl.Event, getter: @escaping (Base) -> T, setter: @escaping (Base, T) -> Swift.Void) -> RxCocoa.ControlProperty<T>
}
public protocol SectionedViewDataSourceType {
  func model(at indexPath: Foundation.IndexPath) throws -> Any
}
extension RxSwift.InfallibleType {
  public func bind<Observer>(to observers: Observer...) -> RxSwift.Disposable where Observer : RxSwift.ObserverType, Self.Element == Observer.Element
  public func bind<Observer>(to observers: Observer...) -> RxSwift.Disposable where Observer : RxSwift.ObserverType, Observer.Element == Self.Element?
  public func bind<Result>(to binder: (Self) -> Result) -> Result
  public func bind<R1, R2>(to binder: (Self) -> (R1) -> R2, curriedArgument: R1) -> R2
  public func bind(onNext: @escaping (Self.Element) -> Swift.Void) -> RxSwift.Disposable
  public func bind(to relays: RxRelay.BehaviorRelay<Self.Element>...) -> RxSwift.Disposable
  public func bind(to relays: RxRelay.BehaviorRelay<Self.Element?>...) -> RxSwift.Disposable
  public func bind(to relays: RxRelay.PublishRelay<Self.Element>...) -> RxSwift.Disposable
  public func bind(to relays: RxRelay.PublishRelay<Self.Element?>...) -> RxSwift.Disposable
  public func bind(to relays: RxRelay.ReplayRelay<Self.Element>...) -> RxSwift.Disposable
  public func bind(to relays: RxRelay.ReplayRelay<Self.Element?>...) -> RxSwift.Disposable
}
@available(iOS 10.0, tvOS 10.0, *)
extension UIKit.UICollectionView : RxCocoa.HasPrefetchDataSource {
  public typealias PrefetchDataSource = UIKit.UICollectionViewDataSourcePrefetching
}
@available(iOS 10.0, tvOS 10.0, *)
@_Concurrency.MainActor(unsafe) open class RxCollectionViewDataSourcePrefetchingProxy : RxCocoa.DelegateProxy<UIKit.UICollectionView, UIKit.UICollectionViewDataSourcePrefetching>, RxCocoa.DelegateProxyType, UIKit.UICollectionViewDataSourcePrefetching {
  @_Concurrency.MainActor(unsafe) weak public var collectionView: UIKit.UICollectionView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(collectionView: RxCocoa.RxCollectionViewDataSourcePrefetchingProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @_Concurrency.MainActor(unsafe) @objc public func collectionView(_ collectionView: UIKit.UICollectionView, prefetchItemsAt indexPaths: [Foundation.IndexPath])
  override open func setForwardToDelegate(_ forwardToDelegate: UIKit.UICollectionViewDataSourcePrefetching?, retainDelegate: Swift.Bool)
  @objc deinit
}
extension RxSwift.Reactive where Base : UIKit.UISearchController {
  public var delegate: RxCocoa.DelegateProxy<UIKit.UISearchController, UIKit.UISearchControllerDelegate> {
    get
  }
  public var didDismiss: RxSwift.Observable<Swift.Void> {
    get
  }
  public var didPresent: RxSwift.Observable<Swift.Void> {
    get
  }
  public var present: RxSwift.Observable<Swift.Void> {
    get
  }
  public var willDismiss: RxSwift.Observable<Swift.Void> {
    get
  }
  public var willPresent: RxSwift.Observable<Swift.Void> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UITableView {
  public func items<Sequence, Source>(_ source: Source) -> (_ cellFactory: @escaping (UIKit.UITableView, Swift.Int, Sequence.Element) -> UIKit.UITableViewCell) -> RxSwift.Disposable where Sequence : Swift.Sequence, Sequence == Source.Element, Source : RxSwift.ObservableType
  public func items<Sequence, Cell, Source>(cellIdentifier: Swift.String, cellType: Cell.Type = Cell.self) -> (_ source: Source) -> (_ configureCell: @escaping (Swift.Int, Sequence.Element, Cell) -> Swift.Void) -> RxSwift.Disposable where Sequence : Swift.Sequence, Sequence == Source.Element, Cell : UIKit.UITableViewCell, Source : RxSwift.ObservableType
  public func items<DataSource, Source>(dataSource: DataSource) -> (_ source: Source) -> RxSwift.Disposable where DataSource : RxCocoa.RxTableViewDataSourceType, DataSource : UIKit.UITableViewDataSource, Source : RxSwift.ObservableType, DataSource.Element == Source.Element
}
extension RxSwift.Reactive where Base : UIKit.UITableView {
  public var dataSource: RxCocoa.DelegateProxy<UIKit.UITableView, UIKit.UITableViewDataSource> {
    get
  }
  public func setDataSource(_ dataSource: UIKit.UITableViewDataSource) -> RxSwift.Disposable
  public var itemSelected: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemDeselected: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemHighlighted: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemUnhighlighted: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemAccessoryButtonTapped: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemInserted: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemDeleted: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemMoved: RxCocoa.ControlEvent<RxCocoa.ItemMovedEvent> {
    get
  }
  public var willDisplayCell: RxCocoa.ControlEvent<RxCocoa.WillDisplayCellEvent> {
    get
  }
  public var didEndDisplayingCell: RxCocoa.ControlEvent<RxCocoa.DidEndDisplayingCellEvent> {
    get
  }
  public func modelSelected<T>(_ modelType: T.Type) -> RxCocoa.ControlEvent<T>
  public func modelDeselected<T>(_ modelType: T.Type) -> RxCocoa.ControlEvent<T>
  public func modelDeleted<T>(_ modelType: T.Type) -> RxCocoa.ControlEvent<T>
  public func model<T>(at indexPath: Foundation.IndexPath) throws -> T
}
@available(iOS 10.0, tvOS 10.0, *)
extension RxSwift.Reactive where Base : UIKit.UITableView {
  public var prefetchDataSource: RxCocoa.DelegateProxy<UIKit.UITableView, UIKit.UITableViewDataSourcePrefetching> {
    get
  }
  public func setPrefetchDataSource(_ prefetchDataSource: UIKit.UITableViewDataSourcePrefetching) -> RxSwift.Disposable
  public var prefetchRows: RxCocoa.ControlEvent<[Foundation.IndexPath]> {
    get
  }
  public var cancelPrefetchingForRows: RxCocoa.ControlEvent<[Foundation.IndexPath]> {
    get
  }
}
public enum RxCocoaInterceptionMechanism {
  case unknown
  case kvo
  public static func == (a: RxCocoa.RxCocoaInterceptionMechanism, b: RxCocoa.RxCocoaInterceptionMechanism) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public enum RxCocoaObjCRuntimeError : Swift.Error, Swift.CustomDebugStringConvertible {
  case unknown(target: Swift.AnyObject)
  case objectMessagesAlreadyBeingIntercepted(target: Swift.AnyObject, interceptionMechanism: RxCocoa.RxCocoaInterceptionMechanism)
  case selectorNotImplemented(target: Swift.AnyObject)
  case cantInterceptCoreFoundationTollFreeBridgedObjects(target: Swift.AnyObject)
  case threadingCollisionWithOtherInterceptionMechanism(target: Swift.AnyObject)
  case savingOriginalForwardingMethodFailed(target: Swift.AnyObject)
  case replacingMethodWithForwardingImplementation(target: Swift.AnyObject)
  case observingPerformanceSensitiveMessages(target: Swift.AnyObject)
  case observingMessagesWithUnsupportedReturnType(target: Swift.AnyObject)
}
extension RxCocoa.RxCocoaObjCRuntimeError {
  public var debugDescription: Swift.String {
    get
  }
}
public enum RxCocoaURLError : Swift.Error {
  case unknown
  case nonHTTPResponse(response: Foundation.URLResponse)
  case httpRequestFailed(response: Foundation.HTTPURLResponse, data: Foundation.Data?)
  case deserializationError(error: Swift.Error)
}
extension RxCocoa.RxCocoaURLError : Swift.CustomDebugStringConvertible {
  public var debugDescription: Swift.String {
    get
  }
}
extension RxSwift.Reactive where Base : Foundation.URLSession {
  public func response(request: Foundation.URLRequest) -> RxSwift.Observable<(response: Foundation.HTTPURLResponse, data: Foundation.Data)>
  public func data(request: Foundation.URLRequest) -> RxSwift.Observable<Foundation.Data>
  public func json(request: Foundation.URLRequest, options: Foundation.JSONSerialization.ReadingOptions = []) -> RxSwift.Observable<Any>
  public func json(url: Foundation.URL) -> RxSwift.Observable<Any>
}
extension RxSwift.Reactive where Base == Foundation.URLSession {
  public static var shouldLogRequest: (Foundation.URLRequest) -> Swift.Bool
}
extension UIKit.UISearchController : RxCocoa.HasDelegate {
  public typealias Delegate = UIKit.UISearchControllerDelegate
}
@_Concurrency.MainActor(unsafe) open class RxSearchControllerDelegateProxy : RxCocoa.DelegateProxy<UIKit.UISearchController, UIKit.UISearchControllerDelegate>, RxCocoa.DelegateProxyType, UIKit.UISearchControllerDelegate {
  @_Concurrency.MainActor(unsafe) weak public var searchController: UIKit.UISearchController? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(searchController: UIKit.UISearchController)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @objc deinit
}
@_Concurrency.MainActor(unsafe) open class RxCollectionViewDelegateProxy : RxCocoa.RxScrollViewDelegateProxy, UIKit.UICollectionViewDelegate, UIKit.UICollectionViewDelegateFlowLayout {
  @_Concurrency.MainActor(unsafe) weak public var collectionView: UIKit.UICollectionView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(collectionView: UIKit.UICollectionView)
  @objc deinit
}
extension UIKit.UIScrollView : RxCocoa.HasDelegate {
  public typealias Delegate = UIKit.UIScrollViewDelegate
}
@_Concurrency.MainActor(unsafe) open class RxScrollViewDelegateProxy : RxCocoa.DelegateProxy<UIKit.UIScrollView, UIKit.UIScrollViewDelegate>, RxCocoa.DelegateProxyType, UIKit.UIScrollViewDelegate {
  @_Concurrency.MainActor(unsafe) weak public var scrollView: UIKit.UIScrollView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(scrollView: RxCocoa.RxScrollViewDelegateProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @_Concurrency.MainActor(unsafe) @objc public func scrollViewDidScroll(_ scrollView: UIKit.UIScrollView)
  @objc deinit
}
public protocol KVORepresentable {
  associatedtype KVOType
  init?(KVOValue: Self.KVOType)
}
public struct TextInput<Base> where Base : UIKit.UITextInput {
  public let base: Base
  public let text: RxCocoa.ControlProperty<Swift.String?>
  public init(base: Base, text: RxCocoa.ControlProperty<Swift.String?>)
}
extension RxSwift.Reactive where Base : UIKit.UITextField {
  public var textInput: RxCocoa.TextInput<Base> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UITextView {
  public var textInput: RxCocoa.TextInput<Base> {
    get
  }
}
extension RxCocoa.ControlEvent {
  public func asDriver() -> RxCocoa.Driver<RxCocoa.ControlEvent<PropertyType>.Element>
}
extension RxCocoa.SharedSequenceConvertibleType where Self.SharingStrategy == RxCocoa.DriverSharingStrategy {
  public func drive<Observer>(_ observers: Observer...) -> RxSwift.Disposable where Observer : RxSwift.ObserverType, Self.Element == Observer.Element
  public func drive<Observer>(_ observers: Observer...) -> RxSwift.Disposable where Observer : RxSwift.ObserverType, Observer.Element == Self.Element?
  public func drive(_ relays: RxRelay.BehaviorRelay<Self.Element>...) -> RxSwift.Disposable
  public func drive(_ relays: RxRelay.BehaviorRelay<Self.Element?>...) -> RxSwift.Disposable
  public func drive(_ relays: RxRelay.ReplayRelay<Self.Element>...) -> RxSwift.Disposable
  public func drive(_ relays: RxRelay.ReplayRelay<Self.Element?>...) -> RxSwift.Disposable
  public func drive<Result>(_ transformation: (RxSwift.Observable<Self.Element>) -> Result) -> Result
  public func drive<R1, R2>(_ with: (RxSwift.Observable<Self.Element>) -> (R1) -> R2, curriedArgument: R1) -> R2
  public func drive<Object>(with object: Object, onNext: ((Object, Self.Element) -> Swift.Void)? = nil, onCompleted: ((Object) -> Swift.Void)? = nil, onDisposed: ((Object) -> Swift.Void)? = nil) -> RxSwift.Disposable where Object : AnyObject
  public func drive(onNext: ((Self.Element) -> Swift.Void)? = nil, onCompleted: (() -> Swift.Void)? = nil, onDisposed: (() -> Swift.Void)? = nil) -> RxSwift.Disposable
  public func drive() -> RxSwift.Disposable
}
extension UIKit.UISearchBar : RxCocoa.HasDelegate {
  public typealias Delegate = UIKit.UISearchBarDelegate
}
@_Concurrency.MainActor(unsafe) open class RxSearchBarDelegateProxy : RxCocoa.DelegateProxy<UIKit.UISearchBar, UIKit.UISearchBarDelegate>, RxCocoa.DelegateProxyType, UIKit.UISearchBarDelegate {
  @_Concurrency.MainActor(unsafe) weak public var searchBar: UIKit.UISearchBar? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(searchBar: RxCocoa.RxSearchBarDelegateProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @objc deinit
}
public protocol RxPickerViewDataSourceType {
  associatedtype Element
  func pickerView(_ pickerView: UIKit.UIPickerView, observedEvent: RxSwift.Event<Self.Element>)
}
extension RxCocoa.SharedSequenceConvertibleType where Self.SharingStrategy == RxCocoa.SignalSharingStrategy {
  public func emit<Observer>(to observers: Observer...) -> RxSwift.Disposable where Observer : RxSwift.ObserverType, Self.Element == Observer.Element
  public func emit<Observer>(to observers: Observer...) -> RxSwift.Disposable where Observer : RxSwift.ObserverType, Observer.Element == Self.Element?
  public func emit(to relays: RxRelay.BehaviorRelay<Self.Element>...) -> RxSwift.Disposable
  public func emit(to relays: RxRelay.BehaviorRelay<Self.Element?>...) -> RxSwift.Disposable
  public func emit(to relays: RxRelay.PublishRelay<Self.Element>...) -> RxSwift.Disposable
  public func emit(to relays: RxRelay.PublishRelay<Self.Element?>...) -> RxSwift.Disposable
  public func emit(to relays: RxRelay.ReplayRelay<Self.Element>...) -> RxSwift.Disposable
  public func emit(to relays: RxRelay.ReplayRelay<Self.Element?>...) -> RxSwift.Disposable
  public func emit<Object>(with object: Object, onNext: ((Object, Self.Element) -> Swift.Void)? = nil, onCompleted: ((Object) -> Swift.Void)? = nil, onDisposed: ((Object) -> Swift.Void)? = nil) -> RxSwift.Disposable where Object : AnyObject
  public func emit(onNext: ((Self.Element) -> Swift.Void)? = nil, onCompleted: (() -> Swift.Void)? = nil, onDisposed: (() -> Swift.Void)? = nil) -> RxSwift.Disposable
  public func emit() -> RxSwift.Disposable
}
extension UIKit.UIPickerView : RxCocoa.HasDelegate {
  public typealias Delegate = UIKit.UIPickerViewDelegate
}
@_Concurrency.MainActor(unsafe) open class RxPickerViewDelegateProxy : RxCocoa.DelegateProxy<UIKit.UIPickerView, UIKit.UIPickerViewDelegate>, RxCocoa.DelegateProxyType, UIKit.UIPickerViewDelegate {
  @_Concurrency.MainActor(unsafe) weak public var pickerView: UIKit.UIPickerView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(pickerView: RxCocoa.RxPickerViewDelegateProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @objc deinit
}
public enum RxCocoaError : Swift.Error, Swift.CustomDebugStringConvertible {
  case unknown
  case invalidOperation(object: Any)
  case itemsNotYetBound(object: Any)
  case invalidPropertyName(object: Any, propertyName: Swift.String)
  case invalidObjectOnKeyPath(object: Any, sourceObject: Swift.AnyObject, propertyName: Swift.String)
  case errorDuringSwizzling
  case castingError(object: Any, targetType: Any.Type)
}
extension RxCocoa.RxCocoaError {
  public var debugDescription: Swift.String {
    get
  }
}
public struct SharedSequence<SharingStrategy, Element> : RxCocoa.SharedSequenceConvertibleType, RxSwift.ObservableConvertibleType where SharingStrategy : RxCocoa.SharingStrategyProtocol {
  public func asObservable() -> RxSwift.Observable<Element>
  public func asSharedSequence() -> RxCocoa.SharedSequence<SharingStrategy, Element>
}
public protocol SharingStrategyProtocol {
  static var scheduler: RxSwift.SchedulerType { get }
  static func share<Element>(_ source: RxSwift.Observable<Element>) -> RxSwift.Observable<Element>
}
public protocol SharedSequenceConvertibleType : RxSwift.ObservableConvertibleType {
  associatedtype SharingStrategy : RxCocoa.SharingStrategyProtocol
  func asSharedSequence() -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func asObservable() -> RxSwift.Observable<Self.Element>
}
extension RxCocoa.SharedSequence {
  public static func empty() -> RxCocoa.SharedSequence<SharingStrategy, Element>
  public static func never() -> RxCocoa.SharedSequence<SharingStrategy, Element>
  public static func just(_ element: Element) -> RxCocoa.SharedSequence<SharingStrategy, Element>
  public static func deferred(_ observableFactory: @escaping () -> RxCocoa.SharedSequence<SharingStrategy, Element>) -> RxCocoa.SharedSequence<SharingStrategy, Element>
  public static func of(_ elements: Element...) -> RxCocoa.SharedSequence<SharingStrategy, Element>
}
extension RxCocoa.SharedSequence {
  public static func from(_ array: [Element]) -> RxCocoa.SharedSequence<SharingStrategy, Element>
  public static func from<Sequence>(_ sequence: Sequence) -> RxCocoa.SharedSequence<SharingStrategy, Element> where Element == Sequence.Element, Sequence : Swift.Sequence
  public static func from(optional: Element?) -> RxCocoa.SharedSequence<SharingStrategy, Element>
}
extension RxCocoa.SharedSequence where Element : Swift.FixedWidthInteger {
  public static func interval(_ period: RxSwift.RxTimeInterval) -> RxCocoa.SharedSequence<SharingStrategy, Element>
}
extension RxCocoa.SharedSequence where Element : Swift.FixedWidthInteger {
  public static func timer(_ dueTime: RxSwift.RxTimeInterval, period: RxSwift.RxTimeInterval) -> RxCocoa.SharedSequence<SharingStrategy, Element>
}
extension RxCocoa.SharedSequence {
  public static func zip<O1, O2>(_ source1: O1, _ source2: O2, resultSelector: @escaping (O1.Element, O2.Element) throws -> Element) -> RxCocoa.SharedSequence<O1.SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func zip<O1, O2>(_ source1: O1, _ source2: O2) -> RxCocoa.SharedSequence<O1.SharingStrategy, (O1.Element, O2.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func combineLatest<O1, O2>(_ source1: O1, _ source2: O2, resultSelector: @escaping (O1.Element, O2.Element) throws -> Element) -> RxCocoa.SharedSequence<SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func combineLatest<O1, O2>(_ source1: O1, _ source2: O2) -> RxCocoa.SharedSequence<Self.SharingStrategy, (O1.Element, O2.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func zip<O1, O2, O3>(_ source1: O1, _ source2: O2, _ source3: O3, resultSelector: @escaping (O1.Element, O2.Element, O3.Element) throws -> Element) -> RxCocoa.SharedSequence<O1.SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func zip<O1, O2, O3>(_ source1: O1, _ source2: O2, _ source3: O3) -> RxCocoa.SharedSequence<O1.SharingStrategy, (O1.Element, O2.Element, O3.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func combineLatest<O1, O2, O3>(_ source1: O1, _ source2: O2, _ source3: O3, resultSelector: @escaping (O1.Element, O2.Element, O3.Element) throws -> Element) -> RxCocoa.SharedSequence<SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func combineLatest<O1, O2, O3>(_ source1: O1, _ source2: O2, _ source3: O3) -> RxCocoa.SharedSequence<Self.SharingStrategy, (O1.Element, O2.Element, O3.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func zip<O1, O2, O3, O4>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element) throws -> Element) -> RxCocoa.SharedSequence<O1.SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func zip<O1, O2, O3, O4>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4) -> RxCocoa.SharedSequence<O1.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func combineLatest<O1, O2, O3, O4>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element) throws -> Element) -> RxCocoa.SharedSequence<SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func combineLatest<O1, O2, O3, O4>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4) -> RxCocoa.SharedSequence<Self.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func zip<O1, O2, O3, O4, O5>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element) throws -> Element) -> RxCocoa.SharedSequence<O1.SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func zip<O1, O2, O3, O4, O5>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5) -> RxCocoa.SharedSequence<O1.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func combineLatest<O1, O2, O3, O4, O5>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element) throws -> Element) -> RxCocoa.SharedSequence<SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func combineLatest<O1, O2, O3, O4, O5>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5) -> RxCocoa.SharedSequence<Self.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func zip<O1, O2, O3, O4, O5, O6>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element) throws -> Element) -> RxCocoa.SharedSequence<O1.SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func zip<O1, O2, O3, O4, O5, O6>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6) -> RxCocoa.SharedSequence<O1.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func combineLatest<O1, O2, O3, O4, O5, O6>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element) throws -> Element) -> RxCocoa.SharedSequence<SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func combineLatest<O1, O2, O3, O4, O5, O6>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6) -> RxCocoa.SharedSequence<Self.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func zip<O1, O2, O3, O4, O5, O6, O7>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, _ source7: O7, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element, O7.Element) throws -> Element) -> RxCocoa.SharedSequence<O1.SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O7 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy, O6.SharingStrategy == O7.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func zip<O1, O2, O3, O4, O5, O6, O7>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, _ source7: O7) -> RxCocoa.SharedSequence<O1.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element, O7.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O7 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy, O6.SharingStrategy == O7.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func combineLatest<O1, O2, O3, O4, O5, O6, O7>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, _ source7: O7, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element, O7.Element) throws -> Element) -> RxCocoa.SharedSequence<SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O7 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy, O6.SharingStrategy == O7.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func combineLatest<O1, O2, O3, O4, O5, O6, O7>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, _ source7: O7) -> RxCocoa.SharedSequence<Self.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element, O7.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O7 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy, O6.SharingStrategy == O7.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func zip<O1, O2, O3, O4, O5, O6, O7, O8>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, _ source7: O7, _ source8: O8, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element, O7.Element, O8.Element) throws -> Element) -> RxCocoa.SharedSequence<O1.SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O7 : RxCocoa.SharedSequenceConvertibleType, O8 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy, O6.SharingStrategy == O7.SharingStrategy, O7.SharingStrategy == O8.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func zip<O1, O2, O3, O4, O5, O6, O7, O8>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, _ source7: O7, _ source8: O8) -> RxCocoa.SharedSequence<O1.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element, O7.Element, O8.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O7 : RxCocoa.SharedSequenceConvertibleType, O8 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy, O6.SharingStrategy == O7.SharingStrategy, O7.SharingStrategy == O8.SharingStrategy
}
extension RxCocoa.SharedSequence {
  public static func combineLatest<O1, O2, O3, O4, O5, O6, O7, O8>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, _ source7: O7, _ source8: O8, resultSelector: @escaping (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element, O7.Element, O8.Element) throws -> Element) -> RxCocoa.SharedSequence<SharingStrategy, Element> where SharingStrategy == O1.SharingStrategy, O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O7 : RxCocoa.SharedSequenceConvertibleType, O8 : RxCocoa.SharedSequenceConvertibleType, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy, O6.SharingStrategy == O7.SharingStrategy, O7.SharingStrategy == O8.SharingStrategy
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element == Any {
  public static func combineLatest<O1, O2, O3, O4, O5, O6, O7, O8>(_ source1: O1, _ source2: O2, _ source3: O3, _ source4: O4, _ source5: O5, _ source6: O6, _ source7: O7, _ source8: O8) -> RxCocoa.SharedSequence<Self.SharingStrategy, (O1.Element, O2.Element, O3.Element, O4.Element, O5.Element, O6.Element, O7.Element, O8.Element)> where O1 : RxCocoa.SharedSequenceConvertibleType, O2 : RxCocoa.SharedSequenceConvertibleType, O3 : RxCocoa.SharedSequenceConvertibleType, O4 : RxCocoa.SharedSequenceConvertibleType, O5 : RxCocoa.SharedSequenceConvertibleType, O6 : RxCocoa.SharedSequenceConvertibleType, O7 : RxCocoa.SharedSequenceConvertibleType, O8 : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == O1.SharingStrategy, O1.SharingStrategy == O2.SharingStrategy, O2.SharingStrategy == O3.SharingStrategy, O3.SharingStrategy == O4.SharingStrategy, O4.SharingStrategy == O5.SharingStrategy, O5.SharingStrategy == O6.SharingStrategy, O6.SharingStrategy == O7.SharingStrategy, O7.SharingStrategy == O8.SharingStrategy
}
extension RxSwift.Reactive where Base : UIKit.UITabBarController {
  public var willBeginCustomizing: RxCocoa.ControlEvent<[UIKit.UIViewController]> {
    get
  }
  public var willEndCustomizing: RxCocoa.ControlEvent<(viewControllers: [UIKit.UIViewController], changed: Swift.Bool)> {
    get
  }
  public var didEndCustomizing: RxCocoa.ControlEvent<(viewControllers: [UIKit.UIViewController], changed: Swift.Bool)> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UITabBarController {
  public var delegate: RxCocoa.DelegateProxy<UIKit.UITabBarController, UIKit.UITabBarControllerDelegate> {
    get
  }
  public var didSelect: RxCocoa.ControlEvent<UIKit.UIViewController> {
    get
  }
}
extension RxSwift.Reactive where Base : ObjectiveC.NSObject {
  public func observe<Element>(_ type: Element.Type, _ keyPath: Swift.String, options: RxCocoa.KeyValueObservingOptions = [.new, .initial], retainSelf: Swift.Bool = true) -> RxSwift.Observable<Element?> where Element : Swift.RawRepresentable, Element.RawValue : RxCocoa.KVORepresentable
}
extension RxSwift.Reactive where Base : ObjectiveC.NSObject {
  public func observeWeakly<Element>(_ type: Element.Type, _ keyPath: Swift.String, options: RxCocoa.KeyValueObservingOptions = [.new, .initial]) -> RxSwift.Observable<Element?> where Element : Swift.RawRepresentable, Element.RawValue : RxCocoa.KVORepresentable
}
extension CoreGraphics.CGRect : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSValue
  public init?(KVOValue: CoreGraphics.CGRect.KVOType)
}
extension CoreGraphics.CGPoint : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSValue
  public init?(KVOValue: CoreGraphics.CGPoint.KVOType)
}
extension CoreGraphics.CGSize : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSValue
  public init?(KVOValue: CoreGraphics.CGSize.KVOType)
}
extension RxSwift.ObservableType {
  public func bind<Observer>(to observers: Observer...) -> RxSwift.Disposable where Observer : RxSwift.ObserverType, Self.Element == Observer.Element
  public func bind<Observer>(to observers: Observer...) -> RxSwift.Disposable where Observer : RxSwift.ObserverType, Observer.Element == Self.Element?
  public func bind<Result>(to binder: (Self) -> Result) -> Result
  public func bind<R1, R2>(to binder: (Self) -> (R1) -> R2, curriedArgument: R1) -> R2
  public func bind<Object>(with object: Object, onNext: @escaping (Object, Self.Element) -> Swift.Void) -> RxSwift.Disposable where Object : AnyObject
  public func bind(onNext: @escaping (Self.Element) -> Swift.Void) -> RxSwift.Disposable
}
extension RxSwift.Reactive where Base : UIKit.UISegmentedControl {
  public var selectedSegmentIndex: RxCocoa.ControlProperty<Swift.Int> {
    get
  }
  public var value: RxCocoa.ControlProperty<Swift.Int> {
    get
  }
  public func enabledForSegment(at index: Swift.Int) -> RxSwift.Binder<Swift.Bool>
  public func titleForSegment(at index: Swift.Int) -> RxSwift.Binder<Swift.String?>
  public func imageForSegment(at index: Swift.Int) -> RxSwift.Binder<UIKit.UIImage?>
}
extension RxRelay.PublishRelay {
  final public func asSignal() -> RxCocoa.Signal<Element>
}
public typealias Signal<Element> = RxCocoa.SharedSequence<RxCocoa.SignalSharingStrategy, Element>
public struct SignalSharingStrategy : RxCocoa.SharingStrategyProtocol {
  public static var scheduler: RxSwift.SchedulerType {
    get
  }
  public static func share<Element>(_ source: RxSwift.Observable<Element>) -> RxSwift.Observable<Element>
}
extension RxCocoa.SharedSequenceConvertibleType where Self.SharingStrategy == RxCocoa.SignalSharingStrategy {
  public func asSignal() -> RxCocoa.Signal<Self.Element>
}
extension RxSwift.Reactive where Base : UIKit.UIButton {
  public var tap: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UIButton {
  public func title(for controlState: UIKit.UIControl.State = []) -> RxSwift.Binder<Swift.String?>
  public func image(for controlState: UIKit.UIControl.State = []) -> RxSwift.Binder<UIKit.UIImage?>
  public func backgroundImage(for controlState: UIKit.UIControl.State = []) -> RxSwift.Binder<UIKit.UIImage?>
}
extension RxSwift.Reactive where Base : UIKit.UIButton {
  public func attributedTitle(for controlState: UIKit.UIControl.State = []) -> RxSwift.Binder<Foundation.NSAttributedString?>
}
extension RxSwift.ObservableConvertibleType {
  public func asSharedSequence<S>(sharingStrategy: S.Type = S.self, onErrorJustReturn: Self.Element) -> RxCocoa.SharedSequence<S, Self.Element> where S : RxCocoa.SharingStrategyProtocol
  public func asSharedSequence<S>(sharingStrategy: S.Type = S.self, onErrorDriveWith: RxCocoa.SharedSequence<S, Self.Element>) -> RxCocoa.SharedSequence<S, Self.Element> where S : RxCocoa.SharingStrategyProtocol
  public func asSharedSequence<S>(sharingStrategy: S.Type = S.self, onErrorRecover: @escaping (_ error: Swift.Error) -> RxCocoa.SharedSequence<S, Self.Element>) -> RxCocoa.SharedSequence<S, Self.Element> where S : RxCocoa.SharingStrategyProtocol
}
public protocol ControlPropertyType : RxSwift.ObservableType, RxSwift.ObserverType {
  func asControlProperty() -> RxCocoa.ControlProperty<Self.Element>
}
public struct ControlProperty<PropertyType> : RxCocoa.ControlPropertyType {
  public typealias Element = PropertyType
  public init<Values, Sink>(values: Values, valueSink: Sink) where PropertyType == Values.Element, Values : RxSwift.ObservableType, Sink : RxSwift.ObserverType, Values.Element == Sink.Element
  public func subscribe<Observer>(_ observer: Observer) -> RxSwift.Disposable where PropertyType == Observer.Element, Observer : RxSwift.ObserverType
  public var changed: RxCocoa.ControlEvent<PropertyType> {
    get
  }
  public func asObservable() -> RxSwift.Observable<RxCocoa.ControlProperty<PropertyType>.Element>
  public func asControlProperty() -> RxCocoa.ControlProperty<RxCocoa.ControlProperty<PropertyType>.Element>
  public func on(_ event: RxSwift.Event<RxCocoa.ControlProperty<PropertyType>.Element>)
}
extension RxCocoa.ControlPropertyType where Self.Element == Swift.String? {
  public var orEmpty: RxCocoa.ControlProperty<Swift.String> {
    get
  }
}
extension UIKit.UITabBarController : RxCocoa.HasDelegate {
  public typealias Delegate = UIKit.UITabBarControllerDelegate
}
@_Concurrency.MainActor(unsafe) open class RxTabBarControllerDelegateProxy : RxCocoa.DelegateProxy<UIKit.UITabBarController, UIKit.UITabBarControllerDelegate>, RxCocoa.DelegateProxyType, UIKit.UITabBarControllerDelegate {
  @_Concurrency.MainActor(unsafe) weak public var tabBar: UIKit.UITabBarController? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(tabBar: RxCocoa.RxTabBarControllerDelegateProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @objc deinit
}
@available(iOS 8.0, macOS 10.10, macOSApplicationExtension 10.10, *)
open class RxWKNavigationDelegateProxy : RxCocoa.DelegateProxy<WebKit.WKWebView, WebKit.WKNavigationDelegate>, RxCocoa.DelegateProxyType, WebKit.WKNavigationDelegate {
  weak public var webView: WebKit.WKWebView? {
    get
  }
  public init(webView: RxCocoa.RxWKNavigationDelegateProxy.ParentObject)
  public static func registerKnownImplementations()
  public static func currentDelegate(for object: WebKit.WKWebView) -> WebKit.WKNavigationDelegate?
  public static func setCurrentDelegate(_ delegate: WebKit.WKNavigationDelegate?, to object: WebKit.WKWebView)
  @objc deinit
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func map<Result>(_ selector: @escaping (Self.Element) -> Result) -> RxCocoa.SharedSequence<Self.SharingStrategy, Result>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func compactMap<Result>(_ selector: @escaping (Self.Element) -> Result?) -> RxCocoa.SharedSequence<Self.SharingStrategy, Result>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func filter(_ predicate: @escaping (Self.Element) -> Swift.Bool) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element : RxCocoa.SharedSequenceConvertibleType {
  public func switchLatest() -> RxCocoa.SharedSequence<Self.Element.SharingStrategy, Self.Element.Element>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func flatMapLatest<Sharing, Result>(_ selector: @escaping (Self.Element) -> RxCocoa.SharedSequence<Sharing, Result>) -> RxCocoa.SharedSequence<Sharing, Result> where Sharing : RxCocoa.SharingStrategyProtocol
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func flatMapFirst<Sharing, Result>(_ selector: @escaping (Self.Element) -> RxCocoa.SharedSequence<Sharing, Result>) -> RxCocoa.SharedSequence<Sharing, Result> where Sharing : RxCocoa.SharingStrategyProtocol
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func `do`(onNext: ((Self.Element) -> Swift.Void)? = nil, afterNext: ((Self.Element) -> Swift.Void)? = nil, onCompleted: (() -> Swift.Void)? = nil, afterCompleted: (() -> Swift.Void)? = nil, onSubscribe: (() -> Swift.Void)? = nil, onSubscribed: (() -> Swift.Void)? = nil, onDispose: (() -> Swift.Void)? = nil) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func debug(_ identifier: Swift.String? = nil, trimOutput: Swift.Bool = false, file: Swift.String = #file, line: Swift.UInt = #line, function: Swift.String = #function) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element : Swift.Equatable {
  public func distinctUntilChanged() -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func distinctUntilChanged<Key>(_ keySelector: @escaping (Self.Element) -> Key) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element> where Key : Swift.Equatable
  public func distinctUntilChanged(_ comparer: @escaping (Self.Element, Self.Element) -> Swift.Bool) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
  public func distinctUntilChanged<K>(_ keySelector: @escaping (Self.Element) -> K, comparer: @escaping (K, K) -> Swift.Bool) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func flatMap<Sharing, Result>(_ selector: @escaping (Self.Element) -> RxCocoa.SharedSequence<Sharing, Result>) -> RxCocoa.SharedSequence<Sharing, Result> where Sharing : RxCocoa.SharingStrategyProtocol
}
extension RxCocoa.SharedSequenceConvertibleType {
  public static func merge<Collection>(_ sources: Collection) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element> where Collection : Swift.Collection, Collection.Element == RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
  public static func merge(_ sources: [RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>]) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
  public static func merge(_ sources: RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>...) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType where Self.Element : RxCocoa.SharedSequenceConvertibleType {
  public func merge() -> RxCocoa.SharedSequence<Self.Element.SharingStrategy, Self.Element.Element>
  public func merge(maxConcurrent: Swift.Int) -> RxCocoa.SharedSequence<Self.Element.SharingStrategy, Self.Element.Element>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func throttle(_ dueTime: RxSwift.RxTimeInterval, latest: Swift.Bool = true) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
  public func debounce(_ dueTime: RxSwift.RxTimeInterval) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func scan<A>(_ seed: A, accumulator: @escaping (A, Self.Element) -> A) -> RxCocoa.SharedSequence<Self.SharingStrategy, A>
}
extension RxCocoa.SharedSequence {
  public static func concat<Sequence>(_ sequence: Sequence) -> RxCocoa.SharedSequence<SharingStrategy, Element> where Sequence : Swift.Sequence, Sequence.Element == RxCocoa.SharedSequence<SharingStrategy, Element>
  public static func concat<Collection>(_ collection: Collection) -> RxCocoa.SharedSequence<SharingStrategy, Element> where Collection : Swift.Collection, Collection.Element == RxCocoa.SharedSequence<SharingStrategy, Element>
}
extension RxCocoa.SharedSequence {
  public static func zip<Collection, Result>(_ collection: Collection, resultSelector: @escaping ([Element]) throws -> Result) -> RxCocoa.SharedSequence<SharingStrategy, Result> where Collection : Swift.Collection, Collection.Element == RxCocoa.SharedSequence<SharingStrategy, Element>
  public static func zip<Collection>(_ collection: Collection) -> RxCocoa.SharedSequence<SharingStrategy, [Element]> where Collection : Swift.Collection, Collection.Element == RxCocoa.SharedSequence<SharingStrategy, Element>
}
extension RxCocoa.SharedSequence {
  public static func combineLatest<Collection, Result>(_ collection: Collection, resultSelector: @escaping ([Element]) throws -> Result) -> RxCocoa.SharedSequence<SharingStrategy, Result> where Collection : Swift.Collection, Collection.Element == RxCocoa.SharedSequence<SharingStrategy, Element>
  public static func combineLatest<Collection>(_ collection: Collection) -> RxCocoa.SharedSequence<SharingStrategy, [Element]> where Collection : Swift.Collection, Collection.Element == RxCocoa.SharedSequence<SharingStrategy, Element>
}
extension RxCocoa.SharedSequenceConvertibleType where Self.SharingStrategy == RxCocoa.SignalSharingStrategy {
  public func withUnretained<Object, Out>(_ obj: Object, resultSelector: @escaping (Object, Self.Element) -> Out) -> RxCocoa.SharedSequence<Self.SharingStrategy, Out> where Object : AnyObject
  public func withUnretained<Object>(_ obj: Object) -> RxCocoa.SharedSequence<Self.SharingStrategy, (Object, Self.Element)> where Object : AnyObject
}
extension RxCocoa.SharedSequenceConvertibleType where Self.SharingStrategy == RxCocoa.DriverSharingStrategy {
  @available(*, unavailable, message: "withUnretained has been deprecated for Driver. Consider using `drive(with:onNext:onCompleted:onDisposed:)`, instead")
  public func withUnretained<Object, Out>(_ obj: Object, resultSelector: @escaping (Object, Self.Element) -> Out) -> RxCocoa.SharedSequence<Self.SharingStrategy, Out> where Object : AnyObject
  @available(*, unavailable, message: "withUnretained has been deprecated for Driver. Consider using `drive(with:onNext:onCompleted:onDisposed:)`, instead")
  public func withUnretained<Object>(_ obj: Object) -> RxCocoa.SharedSequence<Self.SharingStrategy, (Object, Self.Element)> where Object : AnyObject
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func withLatestFrom<SecondO, ResultType>(_ second: SecondO, resultSelector: @escaping (Self.Element, SecondO.Element) -> ResultType) -> RxCocoa.SharedSequence<Self.SharingStrategy, ResultType> where SecondO : RxCocoa.SharedSequenceConvertibleType, Self.SharingStrategy == SecondO.SharingStrategy
  public func withLatestFrom<SecondO>(_ second: SecondO) -> RxCocoa.SharedSequence<Self.SharingStrategy, SecondO.Element> where SecondO : RxCocoa.SharedSequenceConvertibleType
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func skip(_ count: Swift.Int) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func startWith(_ element: Self.Element) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxCocoa.SharedSequenceConvertibleType {
  public func delay(_ dueTime: RxSwift.RxTimeInterval) -> RxCocoa.SharedSequence<Self.SharingStrategy, Self.Element>
}
extension RxSwift.Reactive where Base : UIKit.UIActivityIndicatorView {
  public var isAnimating: RxSwift.Binder<Swift.Bool> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.NSTextStorage {
  public var delegate: RxCocoa.DelegateProxy<UIKit.NSTextStorage, UIKit.NSTextStorageDelegate> {
    get
  }
  public var didProcessEditingRangeChangeInLength: RxSwift.Observable<(editedMask: UIKit.NSTextStorage.EditActions, editedRange: Foundation.NSRange, delta: Swift.Int)> {
    get
  }
}
@_Concurrency.MainActor(unsafe) open class RxTextViewDelegateProxy : RxCocoa.RxScrollViewDelegateProxy, UIKit.UITextViewDelegate {
  @_Concurrency.MainActor(unsafe) weak public var textView: UIKit.UITextView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(textView: UIKit.UITextView)
  @objc @_Concurrency.MainActor(unsafe) open func textView(_ textView: UIKit.UITextView, shouldChangeTextIn range: Foundation.NSRange, replacementText text: Swift.String) -> Swift.Bool
  @objc deinit
}
extension Swift.Int : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSNumber
  public init?(KVOValue: Swift.Int.KVOType)
}
extension Swift.Int32 : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSNumber
  public init?(KVOValue: Swift.Int32.KVOType)
}
extension Swift.Int64 : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSNumber
  public init?(KVOValue: Swift.Int64.KVOType)
}
extension Swift.UInt : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSNumber
  public init?(KVOValue: Swift.UInt.KVOType)
}
extension Swift.UInt32 : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSNumber
  public init?(KVOValue: Swift.UInt32.KVOType)
}
extension Swift.UInt64 : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSNumber
  public init?(KVOValue: Swift.UInt64.KVOType)
}
extension Swift.Bool : RxCocoa.KVORepresentable {
  public typealias KVOType = Foundation.NSNumber
  public init?(KVOValue: Swift.Bool.KVOType)
}
extension RxSwift.ObservableConvertibleType {
  public func asDriver(onErrorJustReturn: Self.Element) -> RxCocoa.Driver<Self.Element>
  public func asDriver(onErrorDriveWith: RxCocoa.Driver<Self.Element>) -> RxCocoa.Driver<Self.Element>
  public func asDriver(onErrorRecover: @escaping (_ error: Swift.Error) -> RxCocoa.Driver<Self.Element>) -> RxCocoa.Driver<Self.Element>
}
extension UIKit.UINavigationController : RxCocoa.HasDelegate {
  public typealias Delegate = UIKit.UINavigationControllerDelegate
}
@_Concurrency.MainActor(unsafe) open class RxNavigationControllerDelegateProxy : RxCocoa.DelegateProxy<UIKit.UINavigationController, UIKit.UINavigationControllerDelegate>, RxCocoa.DelegateProxyType, UIKit.UINavigationControllerDelegate {
  @_Concurrency.MainActor(unsafe) weak public var navigationController: UIKit.UINavigationController? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(navigationController: RxCocoa.RxNavigationControllerDelegateProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @objc deinit
}
extension RxSwift.Reactive where Base : UIKit.UIBarButtonItem {
  public var tap: RxCocoa.ControlEvent<()> {
    get
  }
}
public struct KeyValueObservingOptions : Swift.OptionSet {
  public let rawValue: Swift.UInt
  public init(rawValue: Swift.UInt)
  public static let initial: RxCocoa.KeyValueObservingOptions
  public static let new: RxCocoa.KeyValueObservingOptions
  public typealias ArrayLiteralElement = RxCocoa.KeyValueObservingOptions
  public typealias Element = RxCocoa.KeyValueObservingOptions
  public typealias RawValue = Swift.UInt
}
extension RxSwift.Reactive where Base : ObjectiveC.NSObject {
  public func observe<Element>(_ type: Element.Type, _ keyPath: Swift.String, options: RxCocoa.KeyValueObservingOptions = [.new, .initial], retainSelf: Swift.Bool = true) -> RxSwift.Observable<Element?> where Element : RxCocoa.KVORepresentable
}
extension RxSwift.Reactive where Base : ObjectiveC.NSObject {
  public func observeWeakly<Element>(_ type: Element.Type, _ keyPath: Swift.String, options: RxCocoa.KeyValueObservingOptions = [.new, .initial]) -> RxSwift.Observable<Element?> where Element : RxCocoa.KVORepresentable
}
extension RxSwift.Reactive where Base : UIKit.UIDatePicker {
  public var date: RxCocoa.ControlProperty<Foundation.Date> {
    get
  }
  public var value: RxCocoa.ControlProperty<Foundation.Date> {
    get
  }
  public var countDownDuration: RxCocoa.ControlProperty<Foundation.TimeInterval> {
    get
  }
}
extension UIKit.UITableView : RxCocoa.HasDataSource {
  public typealias DataSource = UIKit.UITableViewDataSource
}
@_Concurrency.MainActor(unsafe) open class RxTableViewDataSourceProxy : RxCocoa.DelegateProxy<UIKit.UITableView, UIKit.UITableViewDataSource>, RxCocoa.DelegateProxyType, UIKit.UITableViewDataSource {
  @_Concurrency.MainActor(unsafe) weak public var tableView: UIKit.UITableView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(tableView: UIKit.UITableView)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @_Concurrency.MainActor(unsafe) @objc public func tableView(_ tableView: UIKit.UITableView, numberOfRowsInSection section: Swift.Int) -> Swift.Int
  @_Concurrency.MainActor(unsafe) @objc public func tableView(_ tableView: UIKit.UITableView, cellForRowAt indexPath: Foundation.IndexPath) -> UIKit.UITableViewCell
  override open func setForwardToDelegate(_ forwardToDelegate: UIKit.UITableViewDataSource?, retainDelegate: Swift.Bool)
  @objc deinit
}
extension RxSwift.Reactive where Base : UIKit.UIGestureRecognizer {
  public var event: RxCocoa.ControlEvent<Base> {
    get
  }
}
extension RxCocoa.ControlProperty {
  public func asDriver() -> RxCocoa.Driver<RxCocoa.ControlProperty<PropertyType>.Element>
}
open class DelegateProxy<P, D> : RxCocoa._RXDelegateProxy where P : AnyObject {
  public typealias ParentObject = P
  public typealias Delegate = D
  public init<Proxy>(parentObject: RxCocoa.DelegateProxy<P, D>.ParentObject, delegateProxy: Proxy.Type) where P == Proxy.ParentObject, D == Proxy.Delegate, Proxy : RxCocoa.DelegateProxy<P, D>, Proxy : RxCocoa.DelegateProxyType
  open func sentMessage(_ selector: ObjectiveC.Selector) -> RxSwift.Observable<[Any]>
  open func methodInvoked(_ selector: ObjectiveC.Selector) -> RxSwift.Observable<[Any]>
  @objc override dynamic open func _sentMessage(_ selector: ObjectiveC.Selector, withArguments arguments: [Any])
  @objc override dynamic open func _methodInvoked(_ selector: ObjectiveC.Selector, withArguments arguments: [Any])
  open func forwardToDelegate() -> RxCocoa.DelegateProxy<P, D>.Delegate?
  open func setForwardToDelegate(_ delegate: RxCocoa.DelegateProxy<P, D>.Delegate?, retainDelegate: Swift.Bool)
  @objc override dynamic open func responds(to aSelector: ObjectiveC.Selector!) -> Swift.Bool
  @objc deinit
}
extension RxSwift.Reactive where Base : ObjectiveC.NSObject {
  public func observe<Element>(_ type: Element.Type, _ keyPath: Swift.String, options: RxCocoa.KeyValueObservingOptions = [.new, .initial], retainSelf: Swift.Bool = true) -> RxSwift.Observable<Element?>
  public func observe<Element>(_ keyPath: Swift.KeyPath<Base, Element>, options: Foundation.NSKeyValueObservingOptions = [.new, .initial]) -> RxSwift.Observable<Element>
}
extension RxSwift.Reactive where Base : ObjectiveC.NSObject {
  public func observeWeakly<Element>(_ type: Element.Type, _ keyPath: Swift.String, options: RxCocoa.KeyValueObservingOptions = [.new, .initial]) -> RxSwift.Observable<Element?>
}
extension RxSwift.Reactive where Base : AnyObject {
  public var deallocated: RxSwift.Observable<Swift.Void> {
    get
  }
  public func sentMessage(_ selector: ObjectiveC.Selector) -> RxSwift.Observable<[Any]>
  public func methodInvoked(_ selector: ObjectiveC.Selector) -> RxSwift.Observable<[Any]>
  public var deallocating: RxSwift.Observable<()> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UINavigationController {
  public typealias ShowEvent = (viewController: UIKit.UIViewController, animated: Swift.Bool)
  public var delegate: RxCocoa.DelegateProxy<UIKit.UINavigationController, UIKit.UINavigationControllerDelegate> {
    get
  }
  public var willShow: RxCocoa.ControlEvent<RxSwift.Reactive<Base>.ShowEvent> {
    get
  }
  public var didShow: RxCocoa.ControlEvent<RxSwift.Reactive<Base>.ShowEvent> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UITabBar {
  public var willBeginCustomizing: RxCocoa.ControlEvent<[UIKit.UITabBarItem]> {
    get
  }
  public var didBeginCustomizing: RxCocoa.ControlEvent<[UIKit.UITabBarItem]> {
    get
  }
  public var willEndCustomizing: RxCocoa.ControlEvent<([UIKit.UITabBarItem], Swift.Bool)> {
    get
  }
  public var didEndCustomizing: RxCocoa.ControlEvent<([UIKit.UITabBarItem], Swift.Bool)> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UITabBar {
  public var delegate: RxCocoa.DelegateProxy<UIKit.UITabBar, UIKit.UITabBarDelegate> {
    get
  }
  public var didSelectItem: RxCocoa.ControlEvent<UIKit.UITabBarItem> {
    get
  }
}
extension UIKit.UITabBar : RxCocoa.HasDelegate {
  public typealias Delegate = UIKit.UITabBarDelegate
}
@_Concurrency.MainActor(unsafe) open class RxTabBarDelegateProxy : RxCocoa.DelegateProxy<UIKit.UITabBar, UIKit.UITabBarDelegate>, RxCocoa.DelegateProxyType, UIKit.UITabBarDelegate {
  @_Concurrency.MainActor(unsafe) weak public var tabBar: UIKit.UITabBar? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(tabBar: RxCocoa.RxTabBarDelegateProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @_Concurrency.MainActor(unsafe) open class func currentDelegate(for object: RxCocoa.RxTabBarDelegateProxy.ParentObject) -> UIKit.UITabBarDelegate?
  @_Concurrency.MainActor(unsafe) open class func setCurrentDelegate(_ delegate: UIKit.UITabBarDelegate?, to object: RxCocoa.RxTabBarDelegateProxy.ParentObject)
  @objc deinit
}
extension RxSwift.Reactive where Base : UIKit.UISwitch {
  public var isOn: RxCocoa.ControlProperty<Swift.Bool> {
    get
  }
  public var value: RxCocoa.ControlProperty<Swift.Bool> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UICollectionView {
  public func items<Sequence, Source>(_ source: Source) -> (_ cellFactory: @escaping (UIKit.UICollectionView, Swift.Int, Sequence.Element) -> UIKit.UICollectionViewCell) -> RxSwift.Disposable where Sequence : Swift.Sequence, Sequence == Source.Element, Source : RxSwift.ObservableType
  public func items<Sequence, Cell, Source>(cellIdentifier: Swift.String, cellType: Cell.Type = Cell.self) -> (_ source: Source) -> (_ configureCell: @escaping (Swift.Int, Sequence.Element, Cell) -> Swift.Void) -> RxSwift.Disposable where Sequence : Swift.Sequence, Sequence == Source.Element, Cell : UIKit.UICollectionViewCell, Source : RxSwift.ObservableType
  public func items<DataSource, Source>(dataSource: DataSource) -> (_ source: Source) -> RxSwift.Disposable where DataSource : RxCocoa.RxCollectionViewDataSourceType, DataSource : UIKit.UICollectionViewDataSource, Source : RxSwift.ObservableType, DataSource.Element == Source.Element
}
extension RxSwift.Reactive where Base : UIKit.UICollectionView {
  public typealias DisplayCollectionViewCellEvent = (cell: UIKit.UICollectionViewCell, at: Foundation.IndexPath)
  public typealias DisplayCollectionViewSupplementaryViewEvent = (supplementaryView: UIKit.UICollectionReusableView, elementKind: Swift.String, at: Foundation.IndexPath)
  public var dataSource: RxCocoa.DelegateProxy<UIKit.UICollectionView, UIKit.UICollectionViewDataSource> {
    get
  }
  public func setDataSource(_ dataSource: UIKit.UICollectionViewDataSource) -> RxSwift.Disposable
  public var itemSelected: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemDeselected: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemHighlighted: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var itemUnhighlighted: RxCocoa.ControlEvent<Foundation.IndexPath> {
    get
  }
  public var willDisplayCell: RxCocoa.ControlEvent<RxSwift.Reactive<Base>.DisplayCollectionViewCellEvent> {
    get
  }
  public var willDisplaySupplementaryView: RxCocoa.ControlEvent<RxSwift.Reactive<Base>.DisplayCollectionViewSupplementaryViewEvent> {
    get
  }
  public var didEndDisplayingCell: RxCocoa.ControlEvent<RxSwift.Reactive<Base>.DisplayCollectionViewCellEvent> {
    get
  }
  public var didEndDisplayingSupplementaryView: RxCocoa.ControlEvent<RxSwift.Reactive<Base>.DisplayCollectionViewSupplementaryViewEvent> {
    get
  }
  public func modelSelected<T>(_ modelType: T.Type) -> RxCocoa.ControlEvent<T>
  public func modelDeselected<T>(_ modelType: T.Type) -> RxCocoa.ControlEvent<T>
  public func model<T>(at indexPath: Foundation.IndexPath) throws -> T
}
@available(iOS 10.0, tvOS 10.0, *)
extension RxSwift.Reactive where Base : UIKit.UICollectionView {
  public var prefetchDataSource: RxCocoa.DelegateProxy<UIKit.UICollectionView, UIKit.UICollectionViewDataSourcePrefetching> {
    get
  }
  public func setPrefetchDataSource(_ prefetchDataSource: UIKit.UICollectionViewDataSourcePrefetching) -> RxSwift.Disposable
  public var prefetchItems: RxCocoa.ControlEvent<[Foundation.IndexPath]> {
    get
  }
  public var cancelPrefetchingForItems: RxCocoa.ControlEvent<[Foundation.IndexPath]> {
    get
  }
}
public protocol RxCollectionViewDataSourceType {
  associatedtype Element
  func collectionView(_ collectionView: UIKit.UICollectionView, observedEvent: RxSwift.Event<Self.Element>)
}
extension RxSwift.Reactive where Base : UIKit.UITextField {
  public var text: RxCocoa.ControlProperty<Swift.String?> {
    get
  }
  public var value: RxCocoa.ControlProperty<Swift.String?> {
    get
  }
  public var attributedText: RxCocoa.ControlProperty<Foundation.NSAttributedString?> {
    get
  }
}
extension RxSwift.Reactive where Base : UIKit.UISearchBar {
  public var delegate: RxCocoa.DelegateProxy<UIKit.UISearchBar, UIKit.UISearchBarDelegate> {
    get
  }
  public var text: RxCocoa.ControlProperty<Swift.String?> {
    get
  }
  public var value: RxCocoa.ControlProperty<Swift.String?> {
    get
  }
  public var selectedScopeButtonIndex: RxCocoa.ControlProperty<Swift.Int> {
    get
  }
  public var cancelButtonClicked: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var bookmarkButtonClicked: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var resultsListButtonClicked: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var searchButtonClicked: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var textDidBeginEditing: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public var textDidEndEditing: RxCocoa.ControlEvent<Swift.Void> {
    get
  }
  public func setDelegate(_ delegate: UIKit.UISearchBarDelegate) -> RxSwift.Disposable
}
extension RxSwift.Reactive where Base : Foundation.NotificationCenter {
  public func notification(_ name: Foundation.Notification.Name?, object: Swift.AnyObject? = nil) -> RxSwift.Observable<Foundation.Notification>
}
public typealias ItemMovedEvent = (sourceIndex: Foundation.IndexPath, destinationIndex: Foundation.IndexPath)
public typealias WillDisplayCellEvent = (cell: UIKit.UITableViewCell, indexPath: Foundation.IndexPath)
public typealias DidEndDisplayingCellEvent = (cell: UIKit.UITableViewCell, indexPath: Foundation.IndexPath)
@available(iOS 8.0, macOS 10.10, macOSApplicationExtension 10.10, *)
extension RxSwift.Reactive where Base : WebKit.WKWebView {
  public var navigationDelegate: RxCocoa.DelegateProxy<WebKit.WKWebView, WebKit.WKNavigationDelegate> {
    get
  }
  public var didCommit: RxSwift.Observable<WebKit.WKNavigation> {
    get
  }
  public var didStartLoad: RxSwift.Observable<WebKit.WKNavigation> {
    get
  }
  public var didFinishLoad: RxSwift.Observable<WebKit.WKNavigation> {
    get
  }
  public var didFailLoad: RxSwift.Observable<(WebKit.WKNavigation, Swift.Error)> {
    get
  }
}
public protocol RxTableViewDataSourceType {
  associatedtype Element
  func tableView(_ tableView: UIKit.UITableView, observedEvent: RxSwift.Event<Self.Element>)
}
@available(iOS 10.0, tvOS 10.0, *)
extension UIKit.UITableView : RxCocoa.HasPrefetchDataSource {
  public typealias PrefetchDataSource = UIKit.UITableViewDataSourcePrefetching
}
@available(iOS 10.0, tvOS 10.0, *)
@_Concurrency.MainActor(unsafe) open class RxTableViewDataSourcePrefetchingProxy : RxCocoa.DelegateProxy<UIKit.UITableView, UIKit.UITableViewDataSourcePrefetching>, RxCocoa.DelegateProxyType, UIKit.UITableViewDataSourcePrefetching {
  @_Concurrency.MainActor(unsafe) weak public var tableView: UIKit.UITableView? {
    get
  }
  @_Concurrency.MainActor(unsafe) public init(tableView: RxCocoa.RxTableViewDataSourcePrefetchingProxy.ParentObject)
  @_Concurrency.MainActor(unsafe) public static func registerKnownImplementations()
  @_Concurrency.MainActor(unsafe) @objc public func tableView(_ tableView: UIKit.UITableView, prefetchRowsAt indexPaths: [Foundation.IndexPath])
  override open func setForwardToDelegate(_ forwardToDelegate: UIKit.UITableViewDataSourcePrefetching?, retainDelegate: Swift.Bool)
  @objc deinit
}
extension RxCocoa.RxCocoaInterceptionMechanism : Swift.Equatable {}
extension RxCocoa.RxCocoaInterceptionMechanism : Swift.Hashable {}
