---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/Lite_ting/tingLite/DerivedData/tingLite/Build/Intermediates.noindex/ArchiveIntermediates/tingLite/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/XMLOCBridge.framework/XMLOCBridge'
relocations:
  - { offset: 0x58E54, size: 0x8, addend: 0x0, symName: _XMLOCBridgeVersionString, symObjAddr: 0x0, symBinAddr: 0x4360, symSize: 0x0 }
  - { offset: 0x58E89, size: 0x8, addend: 0x0, symName: _XMLOCBridgeVersionNumber, symObjAddr: 0x30, symBinAddr: 0x4390, symSize: 0x0 }
  - { offset: 0x58EC6, size: 0x8, addend: 0x0, symName: '+[XMLADXOCUtils getAliBootMark]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0xE4 }
  - { offset: 0x58EDC, size: 0x8, addend: 0x0, symName: '+[XMLADXOCUtils getAliBootMark]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0xE4 }
  - { offset: 0x58FC7, size: 0x8, addend: 0x0, symName: '+[XMLADXOCUtils getAliUpdateMark]', symObjAddr: 0xE4, symBinAddr: 0x40E4, symSize: 0xEC }
...
