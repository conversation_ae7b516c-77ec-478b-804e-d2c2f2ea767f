---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Documents/Lite_ting/tingLite/DerivedData/tingLite/Build/Intermediates.noindex/ArchiveIntermediates/tingLite/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/XCGLogger.framework/XCGLogger'
relocations:
  - { offset: 0x59A14, size: 0x8, addend: 0x0, symName: _XCGLoggerVersionString, symObjAddr: 0x0, symBinAddr: 0x26B60, symSize: 0x0 }
  - { offset: 0x59A49, size: 0x8, addend: 0x0, symName: _XCGLoggerVersionNumber, symObjAddr: 0x28, symBinAddr: 0x26B88, symSize: 0x0 }
  - { offset: 0x59AAA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC6escapeSSvpZ', symObjAddr: 0x3AA0, symBinAddr: 0x26BE0, symSize: 0x0 }
  - { offset: 0x59AC4, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC5resetSSvpZ', symObjAddr: 0x3AB0, symBinAddr: 0x26BF0, symSize: 0x0 }
  - { offset: 0x59AD2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC6escapeSSvau', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0xC }
  - { offset: 0x59DC3, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC5resetSSvau', symObjAddr: 0x18, symBinAddr: 0x8018, symSize: 0xC }
  - { offset: 0x59E0D, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x9EC, symBinAddr: 0x89EC, symSize: 0x40 }
  - { offset: 0x59E2C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC13formatStringsSDyA2AC5LevelOSSGvpfi', symObjAddr: 0xD10, symBinAddr: 0x8D10, symSize: 0xC }
  - { offset: 0x59E48, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC13formatStringsSDyA2AC5LevelOSSGvpfi', symObjAddr: 0xD10, symBinAddr: 0x8D10, symSize: 0xC }
  - { offset: 0x5A05A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC09targetMaxD4Sizes6UInt64Vvpfi', symObjAddr: 0x1CB4, symBinAddr: 0x9CA8, symSize: 0x8 }
  - { offset: 0x5A072, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC21targetMaxTimeIntervalSdvpfi', symObjAddr: 0x1CBC, symBinAddr: 0x9CB0, symSize: 0x10 }
  - { offset: 0x5A08A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC17targetMaxLogFiless5UInt8Vvpfi', symObjAddr: 0x1CCC, symBinAddr: 0x9CC0, symSize: 0x8 }
  - { offset: 0x5A0A2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC22autoRotationCompletionySbcSgvpfi', symObjAddr: 0x1CD8, symBinAddr: 0x9CCC, symSize: 0xC }
  - { offset: 0x5A0BA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC33_customArchiveSuffixDateFormatterSo06NSDateJ0CSgvpfi', symObjAddr: 0x1CE4, symBinAddr: 0x9CD8, symSize: 0x8 }
  - { offset: 0x5A0D2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC27currentLogStartTimeIntervalSdvpfi', symObjAddr: 0x1CF4, symBinAddr: 0x9CE0, symSize: 0x8 }
  - { offset: 0x5A0EA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC04baseD4NameSSvpfi', symObjAddr: 0x1CFC, symBinAddr: 0x9CE8, symSize: 0x1C }
  - { offset: 0x5A102, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC13fileExtensionSSvpfi', symObjAddr: 0x1D18, symBinAddr: 0x9D04, symSize: 0x10 }
  - { offset: 0x5A11A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC11outputLevelA2AC0E0Ovpfi', symObjAddr: 0x1D30, symBinAddr: 0x9D14, symSize: 0x8 }
  - { offset: 0x5A132, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC20haveLoggedAppDetailsSbvpfi', symObjAddr: 0x1D38, symBinAddr: 0x9D1C, symSize: 0x8 }
  - { offset: 0x5A14A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterC16fileNamesToMatch33_AB3936EF3149C64E6DAB511A8F7AB709LLShySSGvpfi', symObjAddr: 0x1E08, symBinAddr: 0x9D60, symSize: 0xC }
  - { offset: 0x5A162, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC19expectedLogMessagesSaySSGvpfi', symObjAddr: 0x1E2C, symBinAddr: 0x9D6C, symSize: 0xC }
  - { offset: 0x5A17A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC04userC3KeySSvpfi', symObjAddr: 0x1E44, symBinAddr: 0x9D78, symSize: 0xC }
  - { offset: 0x5A239, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlF9XCGLoggerAJC5LevelO_Tg5', symObjAddr: 0x22F4, symBinAddr: 0xA160, symSize: 0x58 }
  - { offset: 0x5A2EC, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x234C, symBinAddr: 0xA1B8, symSize: 0x64 }
  - { offset: 0x5A319, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo18NSFileAttributeKeya_Tg5', symObjAddr: 0x23B0, symBinAddr: 0xA21C, symSize: 0x80 }
  - { offset: 0x5A3A2, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlF9XCGLoggerAKC5LevelO_Tg5', symObjAddr: 0x2430, symBinAddr: 0xA29C, symSize: 0xA0 }
  - { offset: 0x5A3F9, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x24D0, symBinAddr: 0xA33C, symSize: 0xE0 }
  - { offset: 0x5A471, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo18NSFileAttributeKeya_Tg5', symObjAddr: 0x25B0, symBinAddr: 0xA41C, symSize: 0x174 }
  - { offset: 0x5A557, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtF9XCGLoggerAEC5LevelO_SSTg5', symObjAddr: 0x2724, symBinAddr: 0xA590, symSize: 0xD4 }
  - { offset: 0x5A62A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtF9XCGLoggerAKC5LevelO_SSTg5', symObjAddr: 0x27F8, symBinAddr: 0xA664, symSize: 0xC8 }
  - { offset: 0x5A68C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtF9XCGLoggerAFC5LevelO_SSTg5', symObjAddr: 0x28C0, symBinAddr: 0xA72C, symSize: 0x320 }
  - { offset: 0x5A822, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x2BE0, symBinAddr: 0xAA4C, symSize: 0x108 }
  - { offset: 0x5A9A6, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF10Foundation3URLV3url_SS9timestampt_Tg5', symObjAddr: 0x2CFC, symBinAddr: 0xAB68, symSize: 0x184 }
  - { offset: 0x5AB42, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSnySiG_Tg5', symObjAddr: 0x2E80, symBinAddr: 0xACEC, symSize: 0x100 }
  - { offset: 0x5ACC6, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF9XCGLogger19DestinationProtocol_p_Tg5', symObjAddr: 0x3110, symBinAddr: 0xAF7C, symSize: 0x13C }
  - { offset: 0x5AE08, size: 0x8, addend: 0x0, symName: '_$sSaySSGSayxGSKsWl', symObjAddr: 0x3350, symBinAddr: 0xB1BC, symSize: 0x60 }
  - { offset: 0x5AE1C, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x33B0, symBinAddr: 0xB21C, symSize: 0x44 }
  - { offset: 0x5AE30, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterCMa', symObjAddr: 0x33F4, symBinAddr: 0xB260, symSize: 0x20 }
  - { offset: 0x5AE44, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x3414, symBinAddr: 0xB280, symSize: 0x3C }
  - { offset: 0x5AE58, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0x3450, symBinAddr: 0xB2BC, symSize: 0x24 }
  - { offset: 0x5AE6C, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x3474, symBinAddr: 0xB2E0, symSize: 0x20 }
  - { offset: 0x5AE80, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOSHAASQWb', symObjAddr: 0x3494, symBinAddr: 0xB300, symSize: 0x4 }
  - { offset: 0x5AE94, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOAESQAAWl', symObjAddr: 0x3498, symBinAddr: 0xB304, symSize: 0x44 }
  - { offset: 0x5AEA8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterCAA0cD8ProtocolAAWI', symObjAddr: 0x34DC, symBinAddr: 0xB348, symSize: 0x3C }
  - { offset: 0x5AEBC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0OwCP', symObjAddr: 0x3558, symBinAddr: 0xB3C4, symSize: 0x30 }
  - { offset: 0x5AED0, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x3588, symBinAddr: 0xB3F4, symSize: 0x4 }
  - { offset: 0x5AEE4, size: 0x8, addend: 0x0, symName: ___swift_memcpy25_8, symObjAddr: 0x358C, symBinAddr: 0xB3F8, symSize: 0x14 }
  - { offset: 0x5AEF8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0Owet', symObjAddr: 0x35A0, symBinAddr: 0xB40C, symSize: 0x48 }
  - { offset: 0x5AF0C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0Owst', symObjAddr: 0x35E8, symBinAddr: 0xB454, symSize: 0x48 }
  - { offset: 0x5AF20, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0Owug', symObjAddr: 0x3630, symBinAddr: 0xB49C, symSize: 0x18 }
  - { offset: 0x5AF34, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0Owup', symObjAddr: 0x3648, symBinAddr: 0xB4B4, symSize: 0x4 }
  - { offset: 0x5AF48, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0Owui', symObjAddr: 0x364C, symBinAddr: 0xB4B8, symSize: 0x1C }
  - { offset: 0x5AF5C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0OMa', symObjAddr: 0x3668, symBinAddr: 0xB4D4, symSize: 0x10 }
  - { offset: 0x5AF70, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x3678, symBinAddr: 0xB4E4, symSize: 0xC }
  - { offset: 0x5AF84, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOwet', symObjAddr: 0x3684, symBinAddr: 0xB4F0, symSize: 0x90 }
  - { offset: 0x5AF98, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOwst', symObjAddr: 0x3714, symBinAddr: 0xB580, symSize: 0xBC }
  - { offset: 0x5AFAC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOwug', symObjAddr: 0x37D0, symBinAddr: 0xB63C, symSize: 0x8 }
  - { offset: 0x5AFC0, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOwui', symObjAddr: 0x37DC, symBinAddr: 0xB644, symSize: 0x8 }
  - { offset: 0x5AFD4, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOMa', symObjAddr: 0x37E4, symBinAddr: 0xB64C, symSize: 0x10 }
  - { offset: 0x5AFE8, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x3854, symBinAddr: 0xB6BC, symSize: 0x30 }
  - { offset: 0x5AFFC, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x3884, symBinAddr: 0xB6EC, symSize: 0x30 }
  - { offset: 0x5B010, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyaSHSCSQWb', symObjAddr: 0x38B4, symBinAddr: 0xB71C, symSize: 0x30 }
  - { offset: 0x5B07C, size: 0x8, addend: 0x0, symName: '_$sSS_yptWOc', symObjAddr: 0x39E8, symBinAddr: 0xB850, symSize: 0x48 }
  - { offset: 0x5B090, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x3A30, symBinAddr: 0xB898, symSize: 0x10 }
  - { offset: 0x5B262, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xC10, symBinAddr: 0x8C10, symSize: 0x40 }
  - { offset: 0x5B3A3, size: 0x8, addend: 0x0, symName: '_$sSDyq_Sgxcis9XCGLoggerABC5LevelO_SSTg5', symObjAddr: 0x1158, symBinAddr: 0x914C, symSize: 0xB4 }
  - { offset: 0x5B4D0, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromD1C_6resulty01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x1EEC, symBinAddr: 0x9D90, symSize: 0x4 }
  - { offset: 0x5B4FE, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo18NSFileAttributeKeya_Tgmq5', symObjAddr: 0x1EF0, symBinAddr: 0x9D94, symSize: 0x84 }
  - { offset: 0x5B58F, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromD1C_6resultSb01_D5CTypeQz_xSgztFZTW', symObjAddr: 0x1F74, symBinAddr: 0x9E18, symSize: 0x4 }
  - { offset: 0x5B5AB, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo18NSFileAttributeKeya_Tgmq5', symObjAddr: 0x1F78, symBinAddr: 0x9E1C, symSize: 0x8C }
  - { offset: 0x5B64B, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromD1Cyx01_D5CTypeQzSgFZTW', symObjAddr: 0x2004, symBinAddr: 0x9EA8, symSize: 0x40 }
  - { offset: 0x5B6C9, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x208C, symBinAddr: 0x9F30, symSize: 0x40 }
  - { offset: 0x5B747, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x20CC, symBinAddr: 0x9F70, symSize: 0x70 }
  - { offset: 0x5B7CC, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x2174, symBinAddr: 0x9FE0, symSize: 0x88 }
  - { offset: 0x5B86E, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toefG0s0fG0VSgyFTW', symObjAddr: 0x2268, symBinAddr: 0xA0D4, symSize: 0x8C }
  - { offset: 0x5B956, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfC9XCGLoggerACC5LevelO_SSTgm5Tf4g_n', symObjAddr: 0x324C, symBinAddr: 0xB0B8, symSize: 0x104 }
  - { offset: 0x5BA85, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgm5Tf4g_n', symObjAddr: 0x38E4, symBinAddr: 0xB74C, symSize: 0x104 }
  - { offset: 0x5BC3D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC6escapeSSvgZ', symObjAddr: 0xC, symBinAddr: 0x800C, symSize: 0xC }
  - { offset: 0x5BC51, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC5resetSSvgZ', symObjAddr: 0x24, symBinAddr: 0x8024, symSize: 0x10 }
  - { offset: 0x5BC79, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0O14foregroundCodeSSvg', symObjAddr: 0x34, symBinAddr: 0x8034, symSize: 0x304 }
  - { offset: 0x5BE2C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0O14backgroundCodeSSvg', symObjAddr: 0x338, symBinAddr: 0x8338, symSize: 0x31C }
  - { offset: 0x5BFF8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0O11descriptionSSvg', symObjAddr: 0x654, symBinAddr: 0x8654, symSize: 0x398 }
  - { offset: 0x5C100, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC0B0Os23CustomStringConvertibleAAsAFP11descriptionSSvgTW', symObjAddr: 0xA2C, symBinAddr: 0x8A2C, symSize: 0x10 }
  - { offset: 0x5C114, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionO4codeSSvg', symObjAddr: 0xA3C, symBinAddr: 0x8A3C, symSize: 0x18 }
  - { offset: 0x5C133, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionO11descriptionSSvg', symObjAddr: 0xA54, symBinAddr: 0x8A54, symSize: 0xC4 }
  - { offset: 0x5C164, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionO21__derived_enum_equalsySbAE_AEtFZ', symObjAddr: 0xB18, symBinAddr: 0x8B18, symSize: 0x10 }
  - { offset: 0x5C199, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionO4hash4intoys6HasherVz_tF', symObjAddr: 0xB28, symBinAddr: 0x8B28, symSize: 0x24 }
  - { offset: 0x5C213, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionO9hashValueSivg', symObjAddr: 0xB4C, symBinAddr: 0x8B4C, symSize: 0x44 }
  - { offset: 0x5C309, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xB90, symBinAddr: 0x8B90, symSize: 0x14 }
  - { offset: 0x5C355, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOSHAASH9hashValueSivgTW', symObjAddr: 0xBA4, symBinAddr: 0x8BA4, symSize: 0x44 }
  - { offset: 0x5C41B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xBE8, symBinAddr: 0x8BE8, symSize: 0x28 }
  - { offset: 0x5C478, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC10ANSIOptionOs23CustomStringConvertibleAAsAFP11descriptionSSvgTW', symObjAddr: 0xC50, symBinAddr: 0x8C50, symSize: 0xC0 }
  - { offset: 0x5C4A5, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterCACycfC', symObjAddr: 0xD28, symBinAddr: 0x8D1C, symSize: 0x54 }
  - { offset: 0x5C502, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterCACycfc', symObjAddr: 0xD7C, symBinAddr: 0x8D70, symSize: 0x48 }
  - { offset: 0x5C580, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC8colorize5level4with2on7optionsyA2AC5LevelO_AC0B0OAMSayAC10ANSIOptionOGtF', symObjAddr: 0xDC4, symBinAddr: 0x8DB8, symSize: 0x394 }
  - { offset: 0x5C8CC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC8colorize5level6customyA2AC5LevelO_SStF', symObjAddr: 0x120C, symBinAddr: 0x9200, symSize: 0x264 }
  - { offset: 0x5CA3C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC15resetFormattingyyF', symObjAddr: 0x1470, symBinAddr: 0x9464, symSize: 0x210 }
  - { offset: 0x5CBDE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC15clearFormattingyyF', symObjAddr: 0x1680, symBinAddr: 0x9674, symSize: 0x1DC }
  - { offset: 0x5CE2F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC6format10logDetails7messageSSAA0cG0Vz_SSztF', symObjAddr: 0x185C, symBinAddr: 0x9850, symSize: 0xE4 }
  - { offset: 0x5CFEE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterC16debugDescriptionSSvg', symObjAddr: 0x1940, symBinAddr: 0x9934, symSize: 0x2D4 }
  - { offset: 0x5D260, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterCfd', symObjAddr: 0x1C14, symBinAddr: 0x9C08, symSize: 0x24 }
  - { offset: 0x5D291, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterCfD', symObjAddr: 0x1C38, symBinAddr: 0x9C2C, symSize: 0x2C }
  - { offset: 0x5D2CA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterCAA0cD8ProtocolA2aDP6format10logDetails7messageSSAA0cH0Vz_SSztFTW', symObjAddr: 0x1C64, symBinAddr: 0x9C58, symSize: 0x28 }
  - { offset: 0x5D2DE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21ANSIColorLogFormatterCs28CustomDebugStringConvertibleAAsADP16debugDescriptionSSvgTW', symObjAddr: 0x1C8C, symBinAddr: 0x9C80, symSize: 0x28 }
  - { offset: 0x5D3A9, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyaSYSCSY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x21FC, symBinAddr: 0xA068, symSize: 0x44 }
  - { offset: 0x5D3D2, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyaSYSCSY8rawValue03RawE0QzvgTW', symObjAddr: 0x2240, symBinAddr: 0xA0AC, symSize: 0x28 }
  - { offset: 0x5D6AD, size: 0x8, addend: 0x0, symName: '_$sS2Ss7CVarArg10FoundationWl', symObjAddr: 0xE8, symBinAddr: 0xB950, symSize: 0x44 }
  - { offset: 0x5D72D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger25AppleSystemLogDestinationCMa', symObjAddr: 0x218, symBinAddr: 0xBA80, symSize: 0x20 }
  - { offset: 0x5D797, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger25AppleSystemLogDestinationC8showDateSbvM', symObjAddr: 0xC, symBinAddr: 0xB8B8, symSize: 0x14 }
  - { offset: 0x5D7B7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger25AppleSystemLogDestinationC5write7messageySS_tF', symObjAddr: 0x24, symBinAddr: 0xB8CC, symSize: 0x84 }
  - { offset: 0x5D8A6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger25AppleSystemLogDestinationC5owner10identifierAc2ACSg_SStcfC', symObjAddr: 0x12C, symBinAddr: 0xB994, symSize: 0x54 }
  - { offset: 0x5D91C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger25AppleSystemLogDestinationC5owner10identifierAc2ACSg_SStcfc', symObjAddr: 0x180, symBinAddr: 0xB9E8, symSize: 0x8 }
  - { offset: 0x5D94E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger25AppleSystemLogDestinationC5owner10identifierAc2ACSg_SStcfc', symObjAddr: 0x180, symBinAddr: 0xB9E8, symSize: 0x8 }
  - { offset: 0x5D9B7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger25AppleSystemLogDestinationCfd', symObjAddr: 0x188, symBinAddr: 0xB9F0, symSize: 0x44 }
  - { offset: 0x5DA14, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger25AppleSystemLogDestinationCfD', symObjAddr: 0x1CC, symBinAddr: 0xBA34, symSize: 0x4C }
  - { offset: 0x5DB7C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC04autocd10DefaultMaxD4Sizes6UInt64VvpZ', symObjAddr: 0x5970, symBinAddr: 0x26F50, symSize: 0x0 }
  - { offset: 0x5DB96, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC04autocD22DefaultMaxTimeIntervalSdvpZ', symObjAddr: 0x5978, symBinAddr: 0x26F58, symSize: 0x0 }
  - { offset: 0x5DBB0, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC26archiveSuffixDateFormatterSo06NSDateI0CSgvg7StaticsL_VAdFvpZ', symObjAddr: 0x5A00, symBinAddr: 0x2EC28, symSize: 0x0 }
  - { offset: 0x5DBBE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC04autocd10DefaultMaxD4Sizes6UInt64Vvau', symObjAddr: 0x0, symBinAddr: 0xBAA0, symSize: 0xC }
  - { offset: 0x5E054, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC04autocD22DefaultMaxTimeIntervalSdvau', symObjAddr: 0x14, symBinAddr: 0xBAAC, symSize: 0xC }
  - { offset: 0x5E072, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOf', symObjAddr: 0x6B0, symBinAddr: 0xC0F8, symSize: 0x48 }
  - { offset: 0x5E086, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SgWOy', symObjAddr: 0x7C8, symBinAddr: 0xC210, symSize: 0x10 }
  - { offset: 0x5E09A, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SgWOe', symObjAddr: 0x834, symBinAddr: 0xC27C, symSize: 0x10 }
  - { offset: 0x5E3CE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationCfE', symObjAddr: 0x3D58, symBinAddr: 0xF774, symSize: 0x70 }
  - { offset: 0x5E3FB, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOd', symObjAddr: 0x3FA0, symBinAddr: 0xF91C, symSize: 0x48 }
  - { offset: 0x5E40F, size: 0x8, addend: 0x0, symName: '_$sSo7NSErrorCMa', symObjAddr: 0x3FE8, symBinAddr: 0xF964, symSize: 0x3C }
  - { offset: 0x5E423, size: 0x8, addend: 0x0, symName: '_$sSo18NSFileAttributeKeyaABSHSCWl', symObjAddr: 0x413C, symBinAddr: 0xFAB8, symSize: 0x48 }
  - { offset: 0x5E492, size: 0x8, addend: 0x0, symName: '_$sSr15_stableSortImpl2byySbx_xtKXE_tKF10Foundation3URLV3url_SS9timestampt_Tg5119$s9XCGLogger27AutoRotatingFileDestinationC08archivedD4URLsSay10Foundation3URLVGyFSbAG3url_SS9timestampt_AgI_SSAJttXEfU_Tf1cn_n', symObjAddr: 0x4184, symBinAddr: 0xFB00, symSize: 0x120 }
  - { offset: 0x5E693, size: 0x8, addend: 0x0, symName: '_$sSr15_stableSortImpl2byySbx_xtKXE_tKFySryxGz_SiztKXEfU_10Foundation3URLV3url_SS9timestampt_Tg5119$s9XCGLogger27AutoRotatingFileDestinationC08archivedD4URLsSay10Foundation3URLVGyFSbAG3url_SS9timestampt_AgI_SSAJttXEfU_Tf1nnncn_n', symObjAddr: 0x42A4, symBinAddr: 0xFC20, symSize: 0x6DC }
  - { offset: 0x5EB93, size: 0x8, addend: 0x0, symName: '_$sSMsSKRzrlE14_insertionSort6within9sortedEnd2byySny5IndexSlQzG_AFSb7ElementSTQz_AItKXEtKFSry10Foundation3URLV3url_SS9timestamptG_Tg5119$s9XCGLogger27AutoRotatingFileDestinationC08archivedD4URLsSay10Foundation3URLVGyFSbAG3url_SS9timestampt_AgI_SSAJttXEfU_Tf1nncn_n', symObjAddr: 0x4980, symBinAddr: 0x102FC, symSize: 0x240 }
  - { offset: 0x5EDA0, size: 0x8, addend: 0x0, symName: '_$sSr13_mergeTopRuns_6buffer2bySbSaySnySiGGz_SpyxGSbx_xtKXEtKF10Foundation3URLV3url_SS9timestampt_Tg5119$s9XCGLogger27AutoRotatingFileDestinationC08archivedD4URLsSay10Foundation3URLVGyFSbAG3url_SS9timestampt_AgI_SSAJttXEfU_Tf1nncn_n', symObjAddr: 0x4BC0, symBinAddr: 0x1053C, symSize: 0x29C }
  - { offset: 0x5F159, size: 0x8, addend: 0x0, symName: '_$sSr13_finalizeRuns_6buffer2bySbSaySnySiGGz_SpyxGSbx_xtKXEtKF10Foundation3URLV3url_SS9timestampt_Tg5119$s9XCGLogger27AutoRotatingFileDestinationC08archivedD4URLsSay10Foundation3URLVGyFSbAG3url_SS9timestampt_AgI_SSAJttXEfU_Tf1nncn_n', symObjAddr: 0x4E5C, symBinAddr: 0x107D8, symSize: 0x170 }
  - { offset: 0x5F2B9, size: 0x8, addend: 0x0, symName: '_$ss6_merge3low3mid4high6buffer2bySbSpyxG_A3GSbx_xtKXEtKlF10Foundation3URLV3url_SS9timestampt_Tg5119$s9XCGLogger27AutoRotatingFileDestinationC08archivedD4URLsSay10Foundation3URLVGyFSbAG3url_SS9timestampt_AgI_SSAJttXEfU_Tf1nnnnc_n', symObjAddr: 0x4FCC, symBinAddr: 0x10948, symSize: 0x4B8 }
  - { offset: 0x5F446, size: 0x8, addend: 0x0, symName: '_$ss6_merge3low3mid4high6buffer2bySbSpyxG_A3GSbx_xtKXEtKlF6$deferL_yylF10Foundation3URLV3url_SS9timestampt_Tg5', symObjAddr: 0x5484, symBinAddr: 0x10E00, symSize: 0xB4 }
  - { offset: 0x5F509, size: 0x8, addend: 0x0, symName: '_$ss20_ArrayBufferProtocolPsE15replaceSubrange_4with10elementsOfySnySiG_Siqd__ntSlRd__7ElementQyd__AGRtzlFs01_aB0Vy10Foundation3URLVG_s15EmptyCollectionVyANGTg5Tf4nndn_n', symObjAddr: 0x5538, symBinAddr: 0x10EB4, symSize: 0x10C }
  - { offset: 0x5F61B, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x56C4, symBinAddr: 0x11040, symSize: 0x14 }
  - { offset: 0x5F62F, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x56D8, symBinAddr: 0x11054, symSize: 0x44 }
  - { offset: 0x5F643, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLV9XCGLoggerE17extendedAttribute7forNameAA4DataVSgSS_tKFAISPys4Int8VGSgKXEfU_TA', symObjAddr: 0x571C, symBinAddr: 0x11098, symSize: 0x18 }
  - { offset: 0x5F657, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x5734, symBinAddr: 0x110B0, symSize: 0x44 }
  - { offset: 0x5F66B, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLV3url_SS9timestamptWOb', symObjAddr: 0x5778, symBinAddr: 0x110F4, symSize: 0x48 }
  - { offset: 0x5F67F, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOy', symObjAddr: 0x57C0, symBinAddr: 0x1113C, symSize: 0x14 }
  - { offset: 0x5F693, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_0, symObjAddr: 0x57D4, symBinAddr: 0x11150, symSize: 0x3C }
  - { offset: 0x5F6A7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationCMU', symObjAddr: 0x5810, symBinAddr: 0x1118C, symSize: 0x8 }
  - { offset: 0x5F6BB, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationCMa', symObjAddr: 0x5818, symBinAddr: 0x11194, symSize: 0x3C }
  - { offset: 0x5F6CF, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationCMr', symObjAddr: 0x5854, symBinAddr: 0x111D0, symSize: 0xB0 }
  - { offset: 0x5F6E3, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgMa', symObjAddr: 0x5904, symBinAddr: 0x11280, symSize: 0x54 }
  - { offset: 0x5F8E3, size: 0x8, addend: 0x0, symName: '_$sSMsSkRzrlE4sort2byySb7ElementSTQz_ADtKXE_tKFSay10Foundation3URLV3url_SS9timestamptG_Tg5119$s9XCGLogger27AutoRotatingFileDestinationC08archivedD4URLsSay10Foundation3URLVGyFSbAG3url_SS9timestampt_AgI_SSAJttXEfU_Tf1cn_n', symObjAddr: 0x36FC, symBinAddr: 0xF144, symSize: 0x88 }
  - { offset: 0x5FB87, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC09targetMaxD4Sizes6UInt64Vvg', symObjAddr: 0x30, symBinAddr: 0xBAB8, symSize: 0x44 }
  - { offset: 0x5FBCB, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC09targetMaxD4Sizes6UInt64Vvs', symObjAddr: 0x74, symBinAddr: 0xBAFC, symSize: 0x50 }
  - { offset: 0x5FC14, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC09targetMaxD4Sizes6UInt64VvM', symObjAddr: 0xC4, symBinAddr: 0xBB4C, symSize: 0x58 }
  - { offset: 0x5FC39, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC09targetMaxD4Sizes6UInt64VvM.resume.0', symObjAddr: 0x11C, symBinAddr: 0xBBA4, symSize: 0x44 }
  - { offset: 0x5FC6E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC21targetMaxTimeIntervalSdvg', symObjAddr: 0x160, symBinAddr: 0xBBE8, symSize: 0x44 }
  - { offset: 0x5FCA7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC21targetMaxTimeIntervalSdvs', symObjAddr: 0x1A4, symBinAddr: 0xBC2C, symSize: 0x60 }
  - { offset: 0x5FCF0, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC21targetMaxTimeIntervalSdvM', symObjAddr: 0x204, symBinAddr: 0xBC8C, symSize: 0x58 }
  - { offset: 0x5FD15, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC21targetMaxTimeIntervalSdvM.resume.0', symObjAddr: 0x25C, symBinAddr: 0xBCE4, symSize: 0x48 }
  - { offset: 0x5FD4A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC17targetMaxLogFiless5UInt8Vvg', symObjAddr: 0x2A4, symBinAddr: 0xBD2C, symSize: 0x44 }
  - { offset: 0x5FD83, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC17targetMaxLogFiless5UInt8Vvs', symObjAddr: 0x2E8, symBinAddr: 0xBD70, symSize: 0x5C }
  - { offset: 0x5FDD5, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC17targetMaxLogFiless5UInt8VvM', symObjAddr: 0x344, symBinAddr: 0xBDCC, symSize: 0x48 }
  - { offset: 0x5FDFA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC17targetMaxLogFiless5UInt8VvM.resume.0', symObjAddr: 0x38C, symBinAddr: 0xBE14, symSize: 0x38 }
  - { offset: 0x5FE2F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC16archiveFolderURL10Foundation0H0VSgvW', symObjAddr: 0x3C4, symBinAddr: 0xBE4C, symSize: 0x1E0 }
  - { offset: 0x5FE6B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC16archiveFolderURL10Foundation0H0VSgvg', symObjAddr: 0x5A4, symBinAddr: 0xC02C, symSize: 0x58 }
  - { offset: 0x5FE90, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC16archiveFolderURL10Foundation0H0VSgvs', symObjAddr: 0x63C, symBinAddr: 0xC084, symSize: 0x74 }
  - { offset: 0x5FEC5, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC16archiveFolderURL10Foundation0H0VSgvM', symObjAddr: 0x6F8, symBinAddr: 0xC140, symSize: 0x48 }
  - { offset: 0x5FEEA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC16archiveFolderURL10Foundation0H0VSgvM.resume.0', symObjAddr: 0x740, symBinAddr: 0xC188, symSize: 0x30 }
  - { offset: 0x5FF0B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC22autoRotationCompletionySbcSgvg', symObjAddr: 0x770, symBinAddr: 0xC1B8, symSize: 0x58 }
  - { offset: 0x5FF1F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC22autoRotationCompletionySbcSgvs', symObjAddr: 0x7D8, symBinAddr: 0xC220, symSize: 0x5C }
  - { offset: 0x5FF33, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC22autoRotationCompletionySbcSgvM', symObjAddr: 0x844, symBinAddr: 0xC28C, symSize: 0x44 }
  - { offset: 0x5FF47, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC22autoRotationCompletionySbcSgvM.resume.0', symObjAddr: 0x888, symBinAddr: 0xC2D0, symSize: 0x4 }
  - { offset: 0x5FF5B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC26archiveSuffixDateFormatterSo06NSDateI0CSgvg', symObjAddr: 0x88C, symBinAddr: 0xC2D4, symSize: 0x70 }
  - { offset: 0x5FFAE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC26archiveSuffixDateFormatterSo06NSDateI0CSgvs', symObjAddr: 0x8FC, symBinAddr: 0xC344, symSize: 0x18 }
  - { offset: 0x60009, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC26archiveSuffixDateFormatterSo06NSDateI0CSgvM', symObjAddr: 0x914, symBinAddr: 0xC35C, symSize: 0x90 }
  - { offset: 0x60067, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC26archiveSuffixDateFormatterSo06NSDateI0CSgvM.resume.0', symObjAddr: 0x9A4, symBinAddr: 0xC3EC, symSize: 0x14 }
  - { offset: 0x6007F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC26archiveSuffixDateFormatterSo06NSDateI0CSgvM.resume.0', symObjAddr: 0x9A4, symBinAddr: 0xC3EC, symSize: 0x14 }
  - { offset: 0x600F0, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC19defaultLogFolderURL10Foundation0I0VvgZ', symObjAddr: 0x9B8, symBinAddr: 0xC400, symSize: 0x228 }
  - { offset: 0x601B6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC5owner07writeToD010identifier12shouldAppend12appendMarker10attributes03maxD4Size0O12TimeInterval26archiveSuffixDateFormatter17targetMaxLogFilesAc2ACSg_ypSSSbSSSgSDySo18NSFileAttributeKeyaypGSgs6UInt64VSdSo06NSDateV0CSgs5UInt8VtcfC', symObjAddr: 0xBE0, symBinAddr: 0xC628, symSize: 0xB0 }
  - { offset: 0x601DF, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC5owner07writeToD010identifier12shouldAppend12appendMarker10attributes03maxD4Size0O12TimeInterval26archiveSuffixDateFormatter17targetMaxLogFilesAc2ACSg_ypSSSbSSSgSDySo18NSFileAttributeKeyaypGSgs6UInt64VSdSo06NSDateV0CSgs5UInt8Vtcfc', symObjAddr: 0xC90, symBinAddr: 0xC6D8, symSize: 0xE28 }
  - { offset: 0x6058D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC15cleanUpLogFilesyyF', symObjAddr: 0x1AB8, symBinAddr: 0xD500, symSize: 0x4D0 }
  - { offset: 0x60980, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC21purgeArchivedLogFilesyyF', symObjAddr: 0x1F88, symBinAddr: 0xD9D0, symSize: 0x3C4 }
  - { offset: 0x60BE9, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC08archivedD4URLsSay10Foundation3URLVGyF', symObjAddr: 0x234C, symBinAddr: 0xDD94, symSize: 0x1380 }
  - { offset: 0x61537, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC06rotateD0yyF', symObjAddr: 0x3784, symBinAddr: 0xF1CC, symSize: 0x38C }
  - { offset: 0x615DC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC12shouldRotateSbyF', symObjAddr: 0x3B10, symBinAddr: 0xF558, symSize: 0x16C }
  - { offset: 0x61608, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC5write7messageySS_tF', symObjAddr: 0x3C7C, symBinAddr: 0xF6C4, symSize: 0x84 }
  - { offset: 0x616E1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC5owner07writeToD010identifier12shouldAppend12appendMarker10attributesAc2ACSg_ypSSSbSSSgSDySo18NSFileAttributeKeyaypGSgtcfC', symObjAddr: 0x3D00, symBinAddr: 0xF748, symSize: 0x2C }
  - { offset: 0x61736, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationCfd', symObjAddr: 0x3DC8, symBinAddr: 0xF7E4, symSize: 0x88 }
  - { offset: 0x61769, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationCfD', symObjAddr: 0x3E50, symBinAddr: 0xF86C, symSize: 0x94 }
  - { offset: 0x617B8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger27AutoRotatingFileDestinationC26archiveSuffixDateFormatterSo06NSDateI0CSgvg7StaticsL_VAdFvpZfiAFyXEfU_', symObjAddr: 0x4024, symBinAddr: 0xF9A0, symSize: 0x118 }
  - { offset: 0x61923, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterC6format10logDetails7messageSSAA0cG0Vz_SSztF', symObjAddr: 0x0, symBinAddr: 0x112E8, symSize: 0x108 }
  - { offset: 0x61A26, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterCMa', symObjAddr: 0x29C, symBinAddr: 0x114E8, symSize: 0x20 }
  - { offset: 0x61A3A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterCAA0cD8ProtocolAAWI', symObjAddr: 0x3BC, symBinAddr: 0x11540, symSize: 0x24 }
  - { offset: 0x61A4E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterCACs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x3E0, symBinAddr: 0x11564, symSize: 0x44 }
  - { offset: 0x61A63, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterC6format10logDetails7messageSSAA0cG0Vz_SSztF', symObjAddr: 0x0, symBinAddr: 0x112E8, symSize: 0x108 }
  - { offset: 0x61ABD, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterCACycfC', symObjAddr: 0x1A4, symBinAddr: 0x113F0, symSize: 0x10 }
  - { offset: 0x61AD1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterCACycfc', symObjAddr: 0x1B4, symBinAddr: 0x11400, symSize: 0x8 }
  - { offset: 0x61B1B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterC16debugDescriptionSSvg', symObjAddr: 0x1BC, symBinAddr: 0x11408, symSize: 0xE0 }
  - { offset: 0x61B92, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterCfD', symObjAddr: 0x384, symBinAddr: 0x11508, symSize: 0x10 }
  - { offset: 0x61BB7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterCAA0cD8ProtocolA2aDP6format10logDetails7messageSSAA0cH0Vz_SSztFTW', symObjAddr: 0x394, symBinAddr: 0x11518, symSize: 0x28 }
  - { offset: 0x61BCB, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18Base64LogFormatterCs28CustomDebugStringConvertibleAAsADP16debugDescriptionSSvgTW', symObjAddr: 0x424, symBinAddr: 0x115A8, symSize: 0x28 }
  - { offset: 0x623EE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCMa', symObjAddr: 0xCE8, symBinAddr: 0x122B4, symSize: 0x20 }
  - { offset: 0x6247B, size: 0x8, addend: 0x0, symName: '_$sSo8NSThreadCMa', symObjAddr: 0x1ED0, symBinAddr: 0x13378, symSize: 0x3C }
  - { offset: 0x6248F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVWOc', symObjAddr: 0x1F0C, symBinAddr: 0x133B4, symSize: 0x44 }
  - { offset: 0x624A3, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVWOh', symObjAddr: 0x1F50, symBinAddr: 0x133F8, symSize: 0x3C }
  - { offset: 0x624B7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolAAWI', symObjAddr: 0x1F8C, symBinAddr: 0x13434, symSize: 0x3C }
  - { offset: 0x6267A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC5ownerA2ACSgvg', symObjAddr: 0x0, symBinAddr: 0x115D0, symSize: 0x34 }
  - { offset: 0x62694, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC5ownerA2ACSgvs', symObjAddr: 0x34, symBinAddr: 0x11604, symSize: 0x44 }
  - { offset: 0x626A8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC5ownerA2ACSgvM', symObjAddr: 0x78, symBinAddr: 0x11648, symSize: 0x3C }
  - { offset: 0x626BC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC10identifierSSvg', symObjAddr: 0xB4, symBinAddr: 0x11684, symSize: 0x48 }
  - { offset: 0x626D0, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC10identifierSSvs', symObjAddr: 0xFC, symBinAddr: 0x116CC, symSize: 0x50 }
  - { offset: 0x626E4, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC10identifierSSvM', symObjAddr: 0x14C, symBinAddr: 0x1171C, symSize: 0x3C }
  - { offset: 0x626F8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC11outputLevelA2AC0E0Ovg', symObjAddr: 0x188, symBinAddr: 0x11758, symSize: 0x30 }
  - { offset: 0x6270C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC11outputLevelA2AC0E0Ovs', symObjAddr: 0x1B8, symBinAddr: 0x11788, symSize: 0x3C }
  - { offset: 0x62720, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC11outputLevelA2AC0E0OvM', symObjAddr: 0x1F4, symBinAddr: 0x117C4, symSize: 0x3C }
  - { offset: 0x62734, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC20haveLoggedAppDetailsSbvg', symObjAddr: 0x230, symBinAddr: 0x11800, symSize: 0x30 }
  - { offset: 0x62748, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC20haveLoggedAppDetailsSbvs', symObjAddr: 0x260, symBinAddr: 0x11830, symSize: 0x3C }
  - { offset: 0x6275C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC20haveLoggedAppDetailsSbvM', symObjAddr: 0x29C, symBinAddr: 0x1186C, symSize: 0x3C }
  - { offset: 0x62770, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC10formattersSayAA20LogFormatterProtocol_pGSgvg', symObjAddr: 0x2D8, symBinAddr: 0x118A8, symSize: 0x34 }
  - { offset: 0x62784, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC10formattersSayAA20LogFormatterProtocol_pGSgvs', symObjAddr: 0x30C, symBinAddr: 0x118DC, symSize: 0x44 }
  - { offset: 0x62798, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC10formattersSayAA20LogFormatterProtocol_pGSgvM', symObjAddr: 0x350, symBinAddr: 0x11920, symSize: 0x3C }
  - { offset: 0x627AC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC7filtersSayAA14FilterProtocol_pGSgvg', symObjAddr: 0x390, symBinAddr: 0x1195C, symSize: 0x34 }
  - { offset: 0x627C0, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC7filtersSayAA14FilterProtocol_pGSgvs', symObjAddr: 0x3C4, symBinAddr: 0x11990, symSize: 0x44 }
  - { offset: 0x627D4, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC7filtersSayAA14FilterProtocol_pGSgvM', symObjAddr: 0x408, symBinAddr: 0x119D4, symSize: 0x3C }
  - { offset: 0x627E8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC17showLogIdentifierSbvg', symObjAddr: 0x444, symBinAddr: 0x11A10, symSize: 0x30 }
  - { offset: 0x627FC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC17showLogIdentifierSbvs', symObjAddr: 0x474, symBinAddr: 0x11A40, symSize: 0x3C }
  - { offset: 0x62810, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC17showLogIdentifierSbvM', symObjAddr: 0x4B0, symBinAddr: 0x11A7C, symSize: 0x3C }
  - { offset: 0x62824, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC16showFunctionNameSbvg', symObjAddr: 0x4EC, symBinAddr: 0x11AB8, symSize: 0x30 }
  - { offset: 0x62838, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC16showFunctionNameSbvs', symObjAddr: 0x51C, symBinAddr: 0x11AE8, symSize: 0x3C }
  - { offset: 0x6284C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC16showFunctionNameSbvM', symObjAddr: 0x558, symBinAddr: 0x11B24, symSize: 0x3C }
  - { offset: 0x62860, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC14showThreadNameSbvg', symObjAddr: 0x594, symBinAddr: 0x11B60, symSize: 0x30 }
  - { offset: 0x62874, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC14showThreadNameSbvs', symObjAddr: 0x5C4, symBinAddr: 0x11B90, symSize: 0x3C }
  - { offset: 0x62888, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC14showThreadNameSbvM', symObjAddr: 0x600, symBinAddr: 0x11BCC, symSize: 0x3C }
  - { offset: 0x6289C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC12showFileNameSbvg', symObjAddr: 0x63C, symBinAddr: 0x11C08, symSize: 0x30 }
  - { offset: 0x628B0, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC12showFileNameSbvs', symObjAddr: 0x66C, symBinAddr: 0x11C38, symSize: 0x3C }
  - { offset: 0x628C4, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC12showFileNameSbvM', symObjAddr: 0x6A8, symBinAddr: 0x11C74, symSize: 0x3C }
  - { offset: 0x628D8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC14showLineNumberSbvg', symObjAddr: 0x6E4, symBinAddr: 0x11CB0, symSize: 0x30 }
  - { offset: 0x628EC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC14showLineNumberSbvs', symObjAddr: 0x714, symBinAddr: 0x11CE0, symSize: 0x3C }
  - { offset: 0x62900, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC14showLineNumberSbvM', symObjAddr: 0x750, symBinAddr: 0x11D1C, symSize: 0x3C }
  - { offset: 0x62914, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC9showLevelSbvg', symObjAddr: 0x78C, symBinAddr: 0x11D58, symSize: 0x30 }
  - { offset: 0x62928, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC9showLevelSbvs', symObjAddr: 0x7BC, symBinAddr: 0x11D88, symSize: 0x3C }
  - { offset: 0x6293C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC9showLevelSbvM', symObjAddr: 0x7F8, symBinAddr: 0x11DC4, symSize: 0x3C }
  - { offset: 0x62950, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC17levelDescriptionsSDyA2AC5LevelOSSGvg', symObjAddr: 0x834, symBinAddr: 0x11E00, symSize: 0x34 }
  - { offset: 0x62964, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC17levelDescriptionsSDyA2AC5LevelOSSGvs', symObjAddr: 0x868, symBinAddr: 0x11E34, symSize: 0x44 }
  - { offset: 0x62978, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC17levelDescriptionsSDyA2AC5LevelOSSGvM', symObjAddr: 0x8AC, symBinAddr: 0x11E78, symSize: 0x3C }
  - { offset: 0x629E1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC16debugDescriptionSSvg', symObjAddr: 0x8E8, symBinAddr: 0x11EB4, symSize: 0x400 }
  - { offset: 0x62D59, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC7process10logDetailsyAA03LogF0V_tF', symObjAddr: 0xDC8, symBinAddr: 0x122D4, symSize: 0x764 }
  - { offset: 0x63355, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC15processInternal10logDetailsyAA03LogG0V_tF', symObjAddr: 0x152C, symBinAddr: 0x12A38, symSize: 0x2AC }
  - { offset: 0x6358B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC12isEnabledFor5levelSbA2AC5LevelO_tF', symObjAddr: 0x17D8, symBinAddr: 0x12CE4, symSize: 0x34 }
  - { offset: 0x63612, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC8showDateSbvg', symObjAddr: 0x180C, symBinAddr: 0x12D18, symSize: 0x30 }
  - { offset: 0x63626, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC8showDateSbvs', symObjAddr: 0x183C, symBinAddr: 0x12D48, symSize: 0x3C }
  - { offset: 0x6363A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC8showDateSbvM', symObjAddr: 0x1878, symBinAddr: 0x12D84, symSize: 0x3C }
  - { offset: 0x63654, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC5owner10identifierAc2ACSg_SStcfC', symObjAddr: 0x18B4, symBinAddr: 0x12DC0, symSize: 0x54 }
  - { offset: 0x63668, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC5owner10identifierAc2ACSg_SStcfc', symObjAddr: 0x1908, symBinAddr: 0x12E14, symSize: 0x98 }
  - { offset: 0x636C9, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC6output10logDetails7messageyAA03LogF0V_SStF', symObjAddr: 0x19A0, symBinAddr: 0x12EAC, symSize: 0x4 }
  - { offset: 0x63705, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationC6output10logDetails7messageyAA03LogF0V_SStF', symObjAddr: 0x19A0, symBinAddr: 0x12EAC, symSize: 0x4 }
  - { offset: 0x63727, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCfd', symObjAddr: 0x19A4, symBinAddr: 0x12EB0, symSize: 0x3C }
  - { offset: 0x6375D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCfD', symObjAddr: 0x19E0, symBinAddr: 0x12EEC, symSize: 0x44 }
  - { offset: 0x63796, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP5ownerA2ACSgvMTW', symObjAddr: 0x1A74, symBinAddr: 0x12F30, symSize: 0x50 }
  - { offset: 0x637AA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP10identifierSSvgTW', symObjAddr: 0x1AC8, symBinAddr: 0x12F84, symSize: 0x28 }
  - { offset: 0x637BE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP10identifierSSvsTW', symObjAddr: 0x1AF0, symBinAddr: 0x12FAC, symSize: 0x28 }
  - { offset: 0x637D2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP10identifierSSvMTW', symObjAddr: 0x1B18, symBinAddr: 0x12FD4, symSize: 0x50 }
  - { offset: 0x637E6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP11outputLevelA2AC0F0OvgTW', symObjAddr: 0x1B6C, symBinAddr: 0x13024, symSize: 0x28 }
  - { offset: 0x637FA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP11outputLevelA2AC0F0OvsTW', symObjAddr: 0x1B94, symBinAddr: 0x1304C, symSize: 0x28 }
  - { offset: 0x6380E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP11outputLevelA2AC0F0OvMTW', symObjAddr: 0x1BBC, symBinAddr: 0x13074, symSize: 0x50 }
  - { offset: 0x63822, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP20haveLoggedAppDetailsSbvgTW', symObjAddr: 0x1C10, symBinAddr: 0x130C4, symSize: 0x2C }
  - { offset: 0x63836, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP20haveLoggedAppDetailsSbvsTW', symObjAddr: 0x1C3C, symBinAddr: 0x130F0, symSize: 0x28 }
  - { offset: 0x6384A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP20haveLoggedAppDetailsSbvMTW', symObjAddr: 0x1C64, symBinAddr: 0x13118, symSize: 0x50 }
  - { offset: 0x6385E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP10formattersSayAA012LogFormatterD0_pGSgvgTW', symObjAddr: 0x1CB8, symBinAddr: 0x13168, symSize: 0x28 }
  - { offset: 0x63872, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP10formattersSayAA012LogFormatterD0_pGSgvsTW', symObjAddr: 0x1CE0, symBinAddr: 0x13190, symSize: 0x28 }
  - { offset: 0x63886, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP10formattersSayAA012LogFormatterD0_pGSgvMTW', symObjAddr: 0x1D08, symBinAddr: 0x131B8, symSize: 0x50 }
  - { offset: 0x6389A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP7filtersSayAA06FilterD0_pGSgvgTW', symObjAddr: 0x1D5C, symBinAddr: 0x13208, symSize: 0x28 }
  - { offset: 0x638AE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP7filtersSayAA06FilterD0_pGSgvsTW', symObjAddr: 0x1D84, symBinAddr: 0x13230, symSize: 0x28 }
  - { offset: 0x638C2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP7filtersSayAA06FilterD0_pGSgvMTW', symObjAddr: 0x1DAC, symBinAddr: 0x13258, symSize: 0x50 }
  - { offset: 0x638D6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP7process10logDetailsyAA03LogG0V_tFTW', symObjAddr: 0x1E2C, symBinAddr: 0x132D4, symSize: 0x28 }
  - { offset: 0x638EA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP15processInternal10logDetailsyAA03LogH0V_tFTW', symObjAddr: 0x1E54, symBinAddr: 0x132FC, symSize: 0x28 }
  - { offset: 0x638FE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP12isEnabledFor5levelSbA2AC5LevelO_tFTW', symObjAddr: 0x1E7C, symBinAddr: 0x13324, symSize: 0x2C }
  - { offset: 0x63912, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCs28CustomDebugStringConvertibleAAsADP16debugDescriptionSSvgTW', symObjAddr: 0x1EA8, symBinAddr: 0x13350, symSize: 0x28 }
  - { offset: 0x63B34, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVWOb', symObjAddr: 0x8AC, symBinAddr: 0x13C8C, symSize: 0x44 }
  - { offset: 0x63B48, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21BaseQueuedDestinationC6output10logDetails7messageyAA03LogG0V_SStFyycfU_TA', symObjAddr: 0x8F0, symBinAddr: 0x13CD0, symSize: 0x50 }
  - { offset: 0x63B5C, size: 0x8, addend: 0x0, symName: '_$sIegh_IeyBh_TR', symObjAddr: 0x97C, symBinAddr: 0x13D20, symSize: 0x2C }
  - { offset: 0x63B74, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x9A8, symBinAddr: 0x13D4C, symSize: 0x10 }
  - { offset: 0x63B88, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x9B8, symBinAddr: 0x13D5C, symSize: 0x8 }
  - { offset: 0x63B9C, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGMa', symObjAddr: 0xA40, symBinAddr: 0x13DA4, symSize: 0x54 }
  - { offset: 0x63BB0, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21BaseQueuedDestinationCfE', symObjAddr: 0xAEC, symBinAddr: 0x13DF8, symSize: 0x8 }
  - { offset: 0x63BDD, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21BaseQueuedDestinationCMa', symObjAddr: 0xB40, symBinAddr: 0x13E00, symSize: 0x20 }
  - { offset: 0x63C3C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21BaseQueuedDestinationC6output10logDetails7messageyAA03LogG0V_SStF', symObjAddr: 0x4C, symBinAddr: 0x13474, symSize: 0x324 }
  - { offset: 0x63D8C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21BaseQueuedDestinationC6output10logDetails7messageyAA03LogG0V_SStFyycfU_', symObjAddr: 0x6B4, symBinAddr: 0x13AD8, symSize: 0xC4 }
  - { offset: 0x63E29, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21BaseQueuedDestinationC8logQueueSo17OS_dispatch_queueCSgvg', symObjAddr: 0x370, symBinAddr: 0x13798, symSize: 0x44 }
  - { offset: 0x63E3D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21BaseQueuedDestinationC8logQueueSo17OS_dispatch_queueCSgvs', symObjAddr: 0x3B4, symBinAddr: 0x137DC, symSize: 0x44 }
  - { offset: 0x63E51, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger21BaseQueuedDestinationC8logQueueSo17OS_dispatch_queueCSgvM', symObjAddr: 0x3F8, symBinAddr: 0x13820, symSize: 0x3C }
  - { offset: 0x63F5A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18ConsoleDestinationC5write7messageySS_tF', symObjAddr: 0x0, symBinAddr: 0x13E64, symSize: 0x84 }
  - { offset: 0x64056, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18ConsoleDestinationCMa', symObjAddr: 0x1B0, symBinAddr: 0x13EE8, symSize: 0x20 }
  - { offset: 0x64077, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger18ConsoleDestinationC5write7messageySS_tF', symObjAddr: 0x0, symBinAddr: 0x13E64, symSize: 0x84 }
  - { offset: 0x641F5, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP13shouldExclude10logDetails7messageSbAA03LogH0Vz_SSztFTW', symObjAddr: 0x0, symBinAddr: 0x13F08, symSize: 0x4 }
  - { offset: 0x6420D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP13shouldExclude10logDetails7messageSbAA03LogH0Vz_SSztFTW', symObjAddr: 0x0, symBinAddr: 0x13F08, symSize: 0x4 }
  - { offset: 0x64277, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger19DestinationProtocolPAAE13shouldExclude10logDetails7messageSbAA03LogG0Vz_SSztF', symObjAddr: 0x4, symBinAddr: 0x13F0C, symSize: 0x180 }
  - { offset: 0x643C9, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15BaseDestinationCAA0C8ProtocolA2aDP15applyFormatters10logDetails7messageyAA03LogH0Vz_SSztFTW', symObjAddr: 0x1C8, symBinAddr: 0x1408C, symSize: 0x4 }
  - { offset: 0x64422, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger19DestinationProtocolPAAE15applyFormatters10logDetails7messageyAA03LogG0Vz_SSztF', symObjAddr: 0x1CC, symBinAddr: 0x14090, symSize: 0x168 }
  - { offset: 0x647E7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger9DevFilterCMa', symObjAddr: 0x198, symBinAddr: 0x14390, symSize: 0x20 }
  - { offset: 0x64825, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger9DevFilterCfd', symObjAddr: 0xF4, symBinAddr: 0x142EC, symSize: 0x24 }
  - { offset: 0x64877, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger9DevFilterCfD', symObjAddr: 0x118, symBinAddr: 0x14310, symSize: 0x2C }
  - { offset: 0x6499D, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueC9XCGLoggerE17currentQueueLabelSSSgvgZ', symObjAddr: 0x0, symBinAddr: 0x143B0, symSize: 0x18 }
  - { offset: 0x649D8, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueC9XCGLoggerE17currentQueueLabelSSSgvgZ', symObjAddr: 0x0, symBinAddr: 0x143B0, symSize: 0x18 }
  - { offset: 0x64E60, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationCfE', symObjAddr: 0x235C, symBinAddr: 0x165C8, symSize: 0x58 }
  - { offset: 0x64E8D, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0x2768, symBinAddr: 0x168F4, symSize: 0x48 }
  - { offset: 0x64EA1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC5flush7closureyyycSg_tFyyYbcfU_TA', symObjAddr: 0x282C, symBinAddr: 0x16970, symSize: 0x40 }
  - { offset: 0x64EDF, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x286C, symBinAddr: 0x169B0, symSize: 0x10 }
  - { offset: 0x64EF3, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x287C, symBinAddr: 0x169C0, symSize: 0x8 }
  - { offset: 0x64F07, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x2894, symBinAddr: 0x169C8, symSize: 0x60 }
  - { offset: 0x64F1B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationCMa', symObjAddr: 0x28F4, symBinAddr: 0x16A28, symSize: 0x3C }
  - { offset: 0x64F2F, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLV9XCGLoggerE20setExtendedAttribute4data7forNameyAA4DataV_SStKFySPys4Int8VGSgKXEfU_TA', symObjAddr: 0x29AC, symBinAddr: 0x16A64, symSize: 0x1C }
  - { offset: 0x64F43, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOb', symObjAddr: 0x29C8, symBinAddr: 0x16A80, symSize: 0x48 }
  - { offset: 0x64F57, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationCMU', symObjAddr: 0x2A10, symBinAddr: 0x16AC8, symSize: 0x8 }
  - { offset: 0x64F6B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationCMr', symObjAddr: 0x2A18, symBinAddr: 0x16AD0, symSize: 0xA4 }
  - { offset: 0x64F7F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC04openB033_5A899ABDC53510C688AE4F7BFED3E6EBLLyyFyycfU_TA', symObjAddr: 0x2BB8, symBinAddr: 0x16C34, symSize: 0x1C }
  - { offset: 0x65151, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC5owner07writeToB010identifier12shouldAppend12appendMarker10attributesAc2ACSg_ypSSSbSSSgSDySo18NSFileAttributeKeyaypGSgtcfc', symObjAddr: 0x0, symBinAddr: 0x143C8, symSize: 0x3C8 }
  - { offset: 0x6521E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC5write7messageySS_tF', symObjAddr: 0x48C, symBinAddr: 0x14790, symSize: 0x144 }
  - { offset: 0x652DB, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationCfd', symObjAddr: 0x5D0, symBinAddr: 0x148D4, symSize: 0xC4 }
  - { offset: 0x6535E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC5ownerA2ACSgvs', symObjAddr: 0x6C8, symBinAddr: 0x14998, symSize: 0xC0 }
  - { offset: 0x653C6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC5ownerA2ACSgvM', symObjAddr: 0x788, symBinAddr: 0x14A58, symSize: 0x48 }
  - { offset: 0x653EB, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC5ownerA2ACSgvM.resume.0', symObjAddr: 0x7D0, symBinAddr: 0x14AA0, symSize: 0x9C }
  - { offset: 0x6545C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC5owner10identifierAc2ACSg_SStcfC', symObjAddr: 0x86C, symBinAddr: 0x14B3C, symSize: 0x2C }
  - { offset: 0x654B1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC07writeToB3URL10Foundation0F0VSgvg', symObjAddr: 0x898, symBinAddr: 0x14B68, symSize: 0x50 }
  - { offset: 0x654EA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC07writeToB3URL10Foundation0F0VSgvs', symObjAddr: 0x8E8, symBinAddr: 0x14BB8, symSize: 0x74 }
  - { offset: 0x6553C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC07writeToB3URL10Foundation0F0VSgvM', symObjAddr: 0x95C, symBinAddr: 0x14C2C, symSize: 0x48 }
  - { offset: 0x65561, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC07writeToB3URL10Foundation0F0VSgvM.resume.0', symObjAddr: 0x9A4, symBinAddr: 0x14C74, symSize: 0x30 }
  - { offset: 0x6564D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC04openB033_5A899ABDC53510C688AE4F7BFED3E6EBLLyyF', symObjAddr: 0x9D4, symBinAddr: 0x14CA4, symSize: 0x96C }
  - { offset: 0x65901, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC5flush7closureyyycSg_tF', symObjAddr: 0x1340, symBinAddr: 0x15610, symSize: 0x210 }
  - { offset: 0x659A6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC06rotateB02to7closureSbyp_ySbcSgtF', symObjAddr: 0x1550, symBinAddr: 0x15820, symSize: 0xD18 }
  - { offset: 0x65E93, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationC5owner07writeToB010identifier12shouldAppend12appendMarker10attributesAc2ACSg_ypSSSbSSSgSDySo18NSFileAttributeKeyaypGSgtcfC', symObjAddr: 0x22CC, symBinAddr: 0x16538, symSize: 0x90 }
  - { offset: 0x65EA7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15FileDestinationCfD', symObjAddr: 0x23B4, symBinAddr: 0x16620, symSize: 0x24 }
  - { offset: 0x65ED7, size: 0x8, addend: 0x0, symName: '_$sSo12NSFileHandleC12forWritingToAB10Foundation3URLV_tKcfCTO', symObjAddr: 0x23D8, symBinAddr: 0x16644, symSize: 0x124 }
  - { offset: 0x661A9, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterCMa', symObjAddr: 0x98C, symBinAddr: 0x175C8, symSize: 0x20 }
  - { offset: 0x661BD, size: 0x8, addend: 0x0, symName: '_$sSaySSGMa', symObjAddr: 0xA6C, symBinAddr: 0x175E8, symSize: 0x54 }
  - { offset: 0x661D1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterCAA0D8ProtocolAAWI', symObjAddr: 0xAC0, symBinAddr: 0x1763C, symSize: 0x3C }
  - { offset: 0x6625A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterC7inverseSbvg', symObjAddr: 0x0, symBinAddr: 0x16C68, symSize: 0x30 }
  - { offset: 0x66274, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterC7inverseSbvs', symObjAddr: 0x30, symBinAddr: 0x16C98, symSize: 0x3C }
  - { offset: 0x66288, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterC7inverseSbvM', symObjAddr: 0x6C, symBinAddr: 0x16CD4, symSize: 0x3C }
  - { offset: 0x662A2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterC3add04fileC0SbSS_tF', symObjAddr: 0x198, symBinAddr: 0x16DFC, symSize: 0xD0 }
  - { offset: 0x662F2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterC3add9fileNamesyx_tSTRzSS7ElementRtzlF', symObjAddr: 0x268, symBinAddr: 0x16ECC, symSize: 0x160 }
  - { offset: 0x66354, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterC5clearyyF', symObjAddr: 0x3C8, symBinAddr: 0x1702C, symSize: 0x50 }
  - { offset: 0x66379, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterC13shouldExclude10logDetails7messageSbAA03LogH0Vz_SSztF', symObjAddr: 0x418, symBinAddr: 0x1707C, symSize: 0x100 }
  - { offset: 0x663FB, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterC16debugDescriptionSSvg', symObjAddr: 0x518, symBinAddr: 0x1717C, symSize: 0x33C }
  - { offset: 0x665B1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterCfd', symObjAddr: 0x854, symBinAddr: 0x174B8, symSize: 0x1C }
  - { offset: 0x665E7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterCfD', symObjAddr: 0x870, symBinAddr: 0x174D4, symSize: 0x24 }
  - { offset: 0x66628, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14FileNameFilterCAA0D8ProtocolA2aDP13shouldExclude10logDetails7messageSbAA03LogI0Vz_SSztFTW', symObjAddr: 0x894, symBinAddr: 0x174F8, symSize: 0x2C }
  - { offset: 0x66740, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger4_try_5catch7finallyyyyc_ySo11NSExceptionCcyycSgtF', symObjAddr: 0x0, symBinAddr: 0x17678, symSize: 0x270 }
  - { offset: 0x66758, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger4_try_5catch7finallyyyyc_ySo11NSExceptionCcyycSgtF', symObjAddr: 0x0, symBinAddr: 0x17678, symSize: 0x270 }
  - { offset: 0x667F0, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x29C, symBinAddr: 0x178E8, symSize: 0x10 }
  - { offset: 0x66804, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2AC, symBinAddr: 0x178F8, symSize: 0x8 }
  - { offset: 0x66818, size: 0x8, addend: 0x0, symName: '_$sSo11NSExceptionCIegg_ABIeyBy_TR', symObjAddr: 0x2B4, symBinAddr: 0x17900, symSize: 0x4C }
  - { offset: 0x6683B, size: 0x8, addend: 0x0, symName: '_$sytIegr_Ieg_TRTA', symObjAddr: 0x328, symBinAddr: 0x17970, symSize: 0x20 }
  - { offset: 0x66ABD, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV5level4date7message12functionName04fileH010lineNumber8userInfoAc2AC5LevelO_10Foundation4DateVS3SSiSDySSypGtcfC', symObjAddr: 0x0, symBinAddr: 0x179A4, symSize: 0xD4 }
  - { offset: 0x66D69, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVMa', symObjAddr: 0xD4, symBinAddr: 0x17A78, symSize: 0x3C }
  - { offset: 0x66D7D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVwCP', symObjAddr: 0x4F4, symBinAddr: 0x17E7C, symSize: 0x100 }
  - { offset: 0x66D91, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVwxx', symObjAddr: 0x5F4, symBinAddr: 0x17F7C, symSize: 0x84 }
  - { offset: 0x66DA5, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVwcp', symObjAddr: 0x678, symBinAddr: 0x18000, symSize: 0xD4 }
  - { offset: 0x66DB9, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVwca', symObjAddr: 0x74C, symBinAddr: 0x180D4, symSize: 0x118 }
  - { offset: 0x66DCD, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVwtk', symObjAddr: 0x864, symBinAddr: 0x181EC, symSize: 0xA0 }
  - { offset: 0x66DE1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVwta', symObjAddr: 0x904, symBinAddr: 0x1828C, symSize: 0xDC }
  - { offset: 0x66DF5, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVwet', symObjAddr: 0x9E0, symBinAddr: 0x18368, symSize: 0xC }
  - { offset: 0x66E09, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVwst', symObjAddr: 0xA70, symBinAddr: 0x183F8, symSize: 0xC }
  - { offset: 0x66E1D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsVMr', symObjAddr: 0xAFC, symBinAddr: 0x18484, symSize: 0x9C }
  - { offset: 0x66E86, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV5level4date7message12functionName04fileH010lineNumber8userInfoAc2AC5LevelO_10Foundation4DateVS3SSiSDySSypGtcfC', symObjAddr: 0x0, symBinAddr: 0x179A4, symSize: 0xD4 }
  - { offset: 0x66F09, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV5levelA2AC5LevelOvg', symObjAddr: 0x110, symBinAddr: 0x17AB4, symSize: 0x8 }
  - { offset: 0x66F23, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV5levelA2AC5LevelOvs', symObjAddr: 0x118, symBinAddr: 0x17ABC, symSize: 0x8 }
  - { offset: 0x66F37, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV5levelA2AC5LevelOvM', symObjAddr: 0x120, symBinAddr: 0x17AC4, symSize: 0x10 }
  - { offset: 0x66F4B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV4date10Foundation4DateVvg', symObjAddr: 0x134, symBinAddr: 0x17AD4, symSize: 0x48 }
  - { offset: 0x66F5F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV4date10Foundation4DateVvs', symObjAddr: 0x17C, symBinAddr: 0x17B1C, symSize: 0x48 }
  - { offset: 0x66F73, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV4date10Foundation4DateVvM', symObjAddr: 0x1C4, symBinAddr: 0x17B64, symSize: 0x28 }
  - { offset: 0x66F87, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV7messageSSvg', symObjAddr: 0x1F0, symBinAddr: 0x17B8C, symSize: 0x3C }
  - { offset: 0x66F9B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV7messageSSvs', symObjAddr: 0x22C, symBinAddr: 0x17BC8, symSize: 0x44 }
  - { offset: 0x66FAF, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV7messageSSvM', symObjAddr: 0x270, symBinAddr: 0x17C0C, symSize: 0x28 }
  - { offset: 0x66FC3, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV12functionNameSSvg', symObjAddr: 0x29C, symBinAddr: 0x17C34, symSize: 0x3C }
  - { offset: 0x66FD7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV12functionNameSSvs', symObjAddr: 0x2D8, symBinAddr: 0x17C70, symSize: 0x44 }
  - { offset: 0x66FEB, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV12functionNameSSvM', symObjAddr: 0x31C, symBinAddr: 0x17CB4, symSize: 0x28 }
  - { offset: 0x66FFF, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV8fileNameSSvg', symObjAddr: 0x348, symBinAddr: 0x17CDC, symSize: 0x3C }
  - { offset: 0x67013, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV8fileNameSSvs', symObjAddr: 0x384, symBinAddr: 0x17D18, symSize: 0x44 }
  - { offset: 0x67027, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV8fileNameSSvM', symObjAddr: 0x3C8, symBinAddr: 0x17D5C, symSize: 0x28 }
  - { offset: 0x6703B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV10lineNumberSivg', symObjAddr: 0x3F4, symBinAddr: 0x17D84, symSize: 0x20 }
  - { offset: 0x6704F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV10lineNumberSivs', symObjAddr: 0x414, symBinAddr: 0x17DA4, symSize: 0x2C }
  - { offset: 0x67063, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV10lineNumberSivM', symObjAddr: 0x440, symBinAddr: 0x17DD0, symSize: 0x28 }
  - { offset: 0x6707D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV8userInfoSDySSypGvg', symObjAddr: 0x46C, symBinAddr: 0x17DF8, symSize: 0x20 }
  - { offset: 0x67091, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV8userInfoSDySSypGvs', symObjAddr: 0x48C, symBinAddr: 0x17E18, symSize: 0x3C }
  - { offset: 0x670A5, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger10LogDetailsV8userInfoSDySSypGvM', symObjAddr: 0x4C8, symBinAddr: 0x17E54, symSize: 0x28 }
  - { offset: 0x6719D, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterCACycfC', symObjAddr: 0x0, symBinAddr: 0x18520, symSize: 0x50 }
  - { offset: 0x674BB, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tF9XCGLoggerAFC5LevelO_SSTg5', symObjAddr: 0x7FC, symBinAddr: 0x18CCC, symSize: 0xA8 }
  - { offset: 0x67569, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyF9XCGLoggerADC5LevelO_SSTg5', symObjAddr: 0x8A4, symBinAddr: 0x18D74, symSize: 0x1BC }
  - { offset: 0x67627, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tF9XCGLoggerAIC5LevelO_SSTg5', symObjAddr: 0xA60, symBinAddr: 0x18F30, symSize: 0x1B8 }
  - { offset: 0x67718, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterCMa', symObjAddr: 0xC18, symBinAddr: 0x190E8, symSize: 0x20 }
  - { offset: 0x6772C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterCAA0eF8ProtocolAAWI', symObjAddr: 0xCF8, symBinAddr: 0x19108, symSize: 0x24 }
  - { offset: 0x67740, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterCACs28CustomDebugStringConvertibleAAWl', symObjAddr: 0xD1C, symBinAddr: 0x1912C, symSize: 0x44 }
  - { offset: 0x67774, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterCACycfC', symObjAddr: 0x0, symBinAddr: 0x18520, symSize: 0x50 }
  - { offset: 0x677D1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterCACycfc', symObjAddr: 0x50, symBinAddr: 0x18570, symSize: 0x3C }
  - { offset: 0x67820, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterC5apply6prefix7postfix2toySSSg_Ah2AC5LevelOSgtF', symObjAddr: 0x8C, symBinAddr: 0x185AC, symSize: 0x198 }
  - { offset: 0x67946, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterC15clearFormattingyyF', symObjAddr: 0x224, symBinAddr: 0x18744, symSize: 0x88 }
  - { offset: 0x6799C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterC6format10logDetails7messageSSAA0eI0Vz_SSztF', symObjAddr: 0x2AC, symBinAddr: 0x187CC, symSize: 0x138 }
  - { offset: 0x67B65, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterC16debugDescriptionSSvg', symObjAddr: 0x3E4, symBinAddr: 0x18904, symSize: 0x378 }
  - { offset: 0x67E81, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterCAA0eF8ProtocolA2aDP6format10logDetails7messageSSAA0eJ0Vz_SSztFTW', symObjAddr: 0x7AC, symBinAddr: 0x18C7C, symSize: 0x28 }
  - { offset: 0x67E95, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger22PrePostFixLogFormatterCs28CustomDebugStringConvertibleAAsADP16debugDescriptionSSvgTW', symObjAddr: 0x7D4, symBinAddr: 0x18CA4, symSize: 0x28 }
  - { offset: 0x67FB6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger9TagFilterCMa', symObjAddr: 0x198, symBinAddr: 0x192B8, symSize: 0x20 }
  - { offset: 0x680A6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC19expectedLogMessagesSaySSGvg', symObjAddr: 0x0, symBinAddr: 0x192D8, symSize: 0x34 }
  - { offset: 0x6830F, size: 0x8, addend: 0x0, symName: '_$sIeg_IyB_TR', symObjAddr: 0x450, symBinAddr: 0x19724, symSize: 0x20 }
  - { offset: 0x683ED, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationCfE', symObjAddr: 0x988, symBinAddr: 0x19C5C, symSize: 0x1C }
  - { offset: 0x6841A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC6output10logDetails7messageyAA03LogF0V_SStFyyXEfU_TA', symObjAddr: 0xB94, symBinAddr: 0x19E68, symSize: 0xC }
  - { offset: 0x6842E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationCMa', symObjAddr: 0xBA0, symBinAddr: 0x19E74, symSize: 0x20 }
  - { offset: 0x68442, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xC20, symBinAddr: 0x19EB4, symSize: 0x10 }
  - { offset: 0x68456, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xC30, symBinAddr: 0x19EC4, symSize: 0x8 }
  - { offset: 0x6846A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC3add18expectedLogMessageySS_tFyyXEfU_TA', symObjAddr: 0xCB8, symBinAddr: 0x19ECC, symSize: 0xC }
  - { offset: 0x684E3, size: 0x8, addend: 0x0, symName: '_$sSlsSQ7ElementRpzrlE10firstIndex2of0C0QzSgAB_tFSaySSG_Tg5', symObjAddr: 0x6DC, symBinAddr: 0x199B0, symSize: 0xE4 }
  - { offset: 0x68621, size: 0x8, addend: 0x0, symName: '_$sSa6remove2atxSi_tFSnySiG_Tg5', symObjAddr: 0x7C0, symBinAddr: 0x19A94, symSize: 0x88 }
  - { offset: 0x686D8, size: 0x8, addend: 0x0, symName: '_$sSa6remove2atxSi_tFSS_Tg5', symObjAddr: 0x848, symBinAddr: 0x19B1C, symSize: 0x88 }
  - { offset: 0x687DA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC19expectedLogMessagesSaySSGvg', symObjAddr: 0x0, symBinAddr: 0x192D8, symSize: 0x34 }
  - { offset: 0x687F4, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC19expectedLogMessagesSaySSGvs', symObjAddr: 0x34, symBinAddr: 0x1930C, symSize: 0x44 }
  - { offset: 0x68808, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC19expectedLogMessagesSaySSGvM', symObjAddr: 0x78, symBinAddr: 0x19350, symSize: 0x3C }
  - { offset: 0x6881C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC21unexpectedLogMessagesSaySSGvg', symObjAddr: 0xB8, symBinAddr: 0x1938C, symSize: 0x34 }
  - { offset: 0x68830, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC21unexpectedLogMessagesSaySSGvs', symObjAddr: 0xEC, symBinAddr: 0x193C0, symSize: 0x44 }
  - { offset: 0x68844, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC21unexpectedLogMessagesSaySSGvM', symObjAddr: 0x130, symBinAddr: 0x19404, symSize: 0x3C }
  - { offset: 0x68858, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC36remainingNumberOfExpectedLogMessagesSivg', symObjAddr: 0x16C, symBinAddr: 0x19440, symSize: 0x30 }
  - { offset: 0x688BC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC29numberOfUnexpectedLogMessagesSivg', symObjAddr: 0x19C, symBinAddr: 0x19470, symSize: 0x30 }
  - { offset: 0x68927, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC3add18expectedLogMessageySS_tF', symObjAddr: 0x1CC, symBinAddr: 0x194A0, symSize: 0x58 }
  - { offset: 0x68970, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC3add18expectedLogMessageySS_tFyyXEfU_', symObjAddr: 0x224, symBinAddr: 0x194F8, symSize: 0xE8 }
  - { offset: 0x68AB3, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC4sync33_2FCCD4EED4A45BCC73434B96F50EF5ACLL7closureyyyXE_tF', symObjAddr: 0x30C, symBinAddr: 0x195E0, symSize: 0x144 }
  - { offset: 0x68B2E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC5resetyyF', symObjAddr: 0x470, symBinAddr: 0x19744, symSize: 0x6C }
  - { offset: 0x68B53, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC6output10logDetails7messageyAA03LogF0V_SStF', symObjAddr: 0x4DC, symBinAddr: 0x197B0, symSize: 0x30 }
  - { offset: 0x68B95, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC6output10logDetails7messageyAA03LogF0V_SStFyyXEfU_', symObjAddr: 0x50C, symBinAddr: 0x197E0, symSize: 0x1D0 }
  - { offset: 0x68D9E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC5owner10identifierAc2ACSg_SStcfC', symObjAddr: 0x8D0, symBinAddr: 0x19BA4, symSize: 0x68 }
  - { offset: 0x68E0C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC5owner10identifierAc2ACSg_SStcfc', symObjAddr: 0x938, symBinAddr: 0x19C0C, symSize: 0x50 }
  - { offset: 0x68EA7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationCfd', symObjAddr: 0x9A4, symBinAddr: 0x19C78, symSize: 0x54 }
  - { offset: 0x68EF6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationCfD', symObjAddr: 0x9F8, symBinAddr: 0x19CCC, symSize: 0x20 }
  - { offset: 0x68F19, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger15TestDestinationC4sync33_2FCCD4EED4A45BCC73434B96F50EF5ACLL7closureyyyXE_tF014$s9XCGLogger15bC38C3add18expectedLogMessageySS_tFyyXEfU_ACSSTf1cn_nTf4ngg_n', symObjAddr: 0xA18, symBinAddr: 0x19CEC, symSize: 0x17C }
  - { offset: 0x6907C, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLV9XCGLoggerE17extendedAttribute7forNameAA4DataVSgSS_tKFAISPys4Int8VGSgKXEfU_', symObjAddr: 0x0, symBinAddr: 0x19F04, symSize: 0x154 }
  - { offset: 0x691ED, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLV9XCGLoggerE17extendedAttribute7forNameAA4DataVSgSS_tKFAISPys4Int8VGSgKXEfU_', symObjAddr: 0x0, symBinAddr: 0x19F04, symSize: 0x154 }
  - { offset: 0x69388, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLV9XCGLoggerE20setExtendedAttribute4data7forNameyAA4DataV_SStKFySPys4Int8VGSgKXEfU_', symObjAddr: 0x164, symBinAddr: 0x1A068, symSize: 0x2EC }
  - { offset: 0x696FC, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationO22withUnsafeMutableBytesyxxSwKXEKlFSi_Tg5060$s10Foundation3URLV9XCGLoggerE17extendedAttribute7forNameAA4B39VSgSS_tKFAISPys4Int8VGSgKXEfU_SiSwXEfU_SPys0S0VGSgSSSiTf1cn_nTf4nngn_n', symObjAddr: 0x4FC, symBinAddr: 0x1A400, symSize: 0x438 }
  - { offset: 0x699A1, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLV9XCGLoggerE10posixError024_7F5A76CDC6E1EA77C484F46F7B8E2538LLySo7NSErrorCs5Int32VFZTf4nd_n', symObjAddr: 0x944, symBinAddr: 0x1A848, symSize: 0x174 }
  - { offset: 0x69B83, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV5countACSi_tcfCTf4nd_n', symObjAddr: 0x460, symBinAddr: 0x1A364, symSize: 0x9C }
  - { offset: 0x69FCA, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSS_Tgm5', symObjAddr: 0xE30, symBinAddr: 0x1B75C, symSize: 0x7C }
  - { offset: 0x6A083, size: 0x8, addend: 0x0, symName: '_$sSr15_stableSortImpl2byySbx_xtKXE_tKFSS_Tg559$sSTsSL7ElementRpzrlE6sortedSayABGyFSbAB_ABtcfu_ShySSG_TGm5Tf1cn_n', symObjAddr: 0xEAC, symBinAddr: 0x1B7D8, symSize: 0xFC }
  - { offset: 0x6A2C9, size: 0x8, addend: 0x0, symName: '_$sSr15_stableSortImpl2byySbx_xtKXE_tKFySryxGz_SiztKXEfU_SS_Tg559$sSTsSL7ElementRpzrlE6sortedSayABGyFSbAB_ABtcfu_ShySSG_TGm5Tf1nnncn_n', symObjAddr: 0xFA8, symBinAddr: 0x1B8D4, symSize: 0x378 }
  - { offset: 0x6A7F9, size: 0x8, addend: 0x0, symName: '_$sSMsSKRzrlE14_insertionSort6within9sortedEnd2byySny5IndexSlQzG_AFSb7ElementSTQz_AItKXEtKFSrySSG_Tg508$sSTsSL7H44RpzrlE6sortedSayABGyFSbAB_ABtcfu_ShySSG_TGm5Tf1nncn_n', symObjAddr: 0x1320, symBinAddr: 0x1BC4C, symSize: 0xCC }
  - { offset: 0x6AA04, size: 0x8, addend: 0x0, symName: '_$sSr13_mergeTopRuns_6buffer2bySbSaySnySiGGz_SpyxGSbx_xtKXEtKFSS_Tg559$sSTsSL7ElementRpzrlE6sortedSayABGyFSbAB_ABtcfu_ShySSG_TGm5Tf1nncn_n', symObjAddr: 0x13EC, symBinAddr: 0x1BD18, symSize: 0x274 }
  - { offset: 0x6AD68, size: 0x8, addend: 0x0, symName: '_$sSr13_finalizeRuns_6buffer2bySbSaySnySiGGz_SpyxGSbx_xtKXEtKFSS_Tg559$sSTsSL7ElementRpzrlE6sortedSayABGyFSbAB_ABtcfu_ShySSG_TGm5Tf1nncn_n', symObjAddr: 0x1660, symBinAddr: 0x1BF8C, symSize: 0x158 }
  - { offset: 0x6AED3, size: 0x8, addend: 0x0, symName: '_$ss6_merge3low3mid4high6buffer2bySbSpyxG_A3GSbx_xtKXEtKlFSS_Tg559$sSTsSL7ElementRpzrlE6sortedSayABGyFSbAB_ABtcfu_ShySSG_TGm5Tf1nnnnc_n', symObjAddr: 0x17B8, symBinAddr: 0x1C0E4, symSize: 0x22C }
  - { offset: 0x6B03F, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyFSnySiG_Tgq5', symObjAddr: 0x19E4, symBinAddr: 0x1C310, symSize: 0x14 }
  - { offset: 0x6B06D, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnFSS_Tg5', symObjAddr: 0x1AF8, symBinAddr: 0x1C324, symSize: 0x1AC }
  - { offset: 0x6B14C, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtFSS_Tg5', symObjAddr: 0x1CA4, symBinAddr: 0x1C4D0, symSize: 0x198 }
  - { offset: 0x6B240, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyFSS_Tg5', symObjAddr: 0x1E3C, symBinAddr: 0x1C668, symSize: 0x1B0 }
  - { offset: 0x6B2CB, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tFSS_Tg5', symObjAddr: 0x1FEC, symBinAddr: 0x1C818, symSize: 0x2AC }
  - { offset: 0x6B371, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tFSS_Tg5', symObjAddr: 0x2298, symBinAddr: 0x1CAC4, symSize: 0x2DC }
  - { offset: 0x6B4B3, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lFSS_SaySSGTg5', symObjAddr: 0x2574, symBinAddr: 0x1CDA0, symSize: 0x18C }
  - { offset: 0x6B64F, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV19genericIntersectionyAByxGqd__7ElementQyd__RszSTRd__lFADs13_UnsafeBitsetVXEfU_SS_SaySSGTg5', symObjAddr: 0x2700, symBinAddr: 0x1CF2C, symSize: 0x210 }
  - { offset: 0x6B79F, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13extractSubset5using5countAByxGs13_UnsafeBitsetV_SitFSS_Tg5', symObjAddr: 0x2910, symBinAddr: 0x1D13C, symSize: 0x284 }
  - { offset: 0x6B860, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADFSS_Tg5', symObjAddr: 0x2B94, symBinAddr: 0x1D3C0, symSize: 0x18C }
  - { offset: 0x6B9AF, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV12intersectionyAByxGADFADs13_UnsafeBitsetVXEfU_SS_Tg5', symObjAddr: 0x2D20, symBinAddr: 0x1D54C, symSize: 0x514 }
  - { offset: 0x6BB16, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFShySSG_Tg5', symObjAddr: 0x3234, symBinAddr: 0x1DA60, symSize: 0x24C }
  - { offset: 0x6BB9B, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNewAByxGyFSS_Tg5', symObjAddr: 0x3494, symBinAddr: 0x1DCAC, symSize: 0x14 }
  - { offset: 0x6BBC8, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x354C, symBinAddr: 0x1DD64, symSize: 0x40 }
  - { offset: 0x6BBDC, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterCMa', symObjAddr: 0x3638, symBinAddr: 0x1DDA4, symSize: 0x20 }
  - { offset: 0x6BC3D, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayVyAByxGqd__c7ElementQyd__RszSTRd__lufCSS_ShySSGTgm5Tf4g_n', symObjAddr: 0x367C, symBinAddr: 0x1DDC4, symSize: 0xC0 }
  - { offset: 0x6BD0B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterCAA0D8ProtocolAAWI', symObjAddr: 0x3790, symBinAddr: 0x1DE84, symSize: 0x3C }
  - { offset: 0x6BD1F, size: 0x8, addend: 0x0, symName: '_$sSh8IteratorV8_VariantOySS__GWOe', symObjAddr: 0x380C, symBinAddr: 0x1DEC0, symSize: 0x8 }
  - { offset: 0x6BD4D, size: 0x8, addend: 0x0, symName: '_$sSMsSkRzrlE4sort2byySb7ElementSTQz_ADtKXE_tKFs15ContiguousArrayVySSG_Tg508$sSTsSL7C44RpzrlE6sortedSayABGyFSbAB_ABtcfu_ShySSG_TGm5Tf1cn_n', symObjAddr: 0x0, symBinAddr: 0x1A9BC, symSize: 0x68 }
  - { offset: 0x6BEBC, size: 0x8, addend: 0x0, symName: '_$sSh8containsySbxFSS_Tg5', symObjAddr: 0xBF0, symBinAddr: 0x1B548, symSize: 0x134 }
  - { offset: 0x6C007, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC04userC3KeySSvg', symObjAddr: 0xE4, symBinAddr: 0x1AA7C, symSize: 0x48 }
  - { offset: 0x6C01B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC04userC3KeySSvs', symObjAddr: 0x12C, symBinAddr: 0x1AAC4, symSize: 0x50 }
  - { offset: 0x6C02F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC05applyD18ToInternalMessagesSbvg', symObjAddr: 0x1BC, symBinAddr: 0x1AB14, symSize: 0x30 }
  - { offset: 0x6C043, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC05applyD18ToInternalMessagesSbvs', symObjAddr: 0x1EC, symBinAddr: 0x1AB44, symSize: 0x3C }
  - { offset: 0x6C057, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC05applyD18ToInternalMessagesSbvM', symObjAddr: 0x228, symBinAddr: 0x1AB80, symSize: 0x3C }
  - { offset: 0x6C06B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC7inverseSbvg', symObjAddr: 0x264, symBinAddr: 0x1ABBC, symSize: 0x30 }
  - { offset: 0x6C07F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC7inverseSbvs', symObjAddr: 0x294, symBinAddr: 0x1ABEC, symSize: 0x3C }
  - { offset: 0x6C093, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC7inverseSbvM', symObjAddr: 0x2D0, symBinAddr: 0x1AC28, symSize: 0x3C }
  - { offset: 0x6C0AD, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC3add4itemSbSS_tF', symObjAddr: 0x30C, symBinAddr: 0x1AC64, symSize: 0x78 }
  - { offset: 0x6C0FD, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC3add5itemsyx_tSTRzSS7ElementRtzlF', symObjAddr: 0x384, symBinAddr: 0x1ACDC, symSize: 0x160 }
  - { offset: 0x6C15F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC5clearyyF', symObjAddr: 0x4E4, symBinAddr: 0x1AE3C, symSize: 0x50 }
  - { offset: 0x6C184, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC13shouldExclude10logDetails7messageSbAA03LogH0Vz_SSztF', symObjAddr: 0x534, symBinAddr: 0x1AE8C, symSize: 0x338 }
  - { offset: 0x6C2E3, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterC16debugDescriptionSSvg', symObjAddr: 0x86C, symBinAddr: 0x1B1C4, symSize: 0x384 }
  - { offset: 0x6C4C5, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterCAA0D8ProtocolA2aDP13shouldExclude10logDetails7messageSbAA03LogI0Vz_SSztFTW', symObjAddr: 0xDDC, symBinAddr: 0x1B708, symSize: 0x2C }
  - { offset: 0x6C4D9, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger14UserInfoFilterCs28CustomDebugStringConvertibleAAsADP16debugDescriptionSSvgTW', symObjAddr: 0xE08, symBinAddr: 0x1B734, symSize: 0x28 }
  - { offset: 0x6C6AD, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV14baseIdentifierSSvpZ', symObjAddr: 0x52A0, symBinAddr: 0x2D0F8, symSize: 0x0 }
  - { offset: 0x6C6C7, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV25defaultInstanceIdentifierSSvpZ', symObjAddr: 0x1D818, symBinAddr: 0x32298, symSize: 0x0 }
  - { offset: 0x6C6E1, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV32baseConsoleDestinationIdentifierSSvpZ', symObjAddr: 0x1D828, symBinAddr: 0x322A8, symSize: 0x0 }
  - { offset: 0x6C6FB, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV30systemLogDestinationIdentifierSSvpZ', symObjAddr: 0x1D838, symBinAddr: 0x322B8, symSize: 0x0 }
  - { offset: 0x6C715, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV25fileDestinationIdentifierSSvpZ', symObjAddr: 0x1D848, symBinAddr: 0x322C8, symSize: 0x0 }
  - { offset: 0x6C72F, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV18logQueueIdentifierSSvpZ', symObjAddr: 0x52B0, symBinAddr: 0x2D108, symSize: 0x0 }
  - { offset: 0x6C749, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV15userInfoKeyTagsSSvpZ', symObjAddr: 0x52C0, symBinAddr: 0x2D118, symSize: 0x0 }
  - { offset: 0x6C763, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV15userInfoKeyDevsSSvpZ', symObjAddr: 0x52D0, symBinAddr: 0x2D128, symSize: 0x0 }
  - { offset: 0x6C77D, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV19userInfoKeyInternalSSvpZ', symObjAddr: 0x52E0, symBinAddr: 0x2D138, symSize: 0x0 }
  - { offset: 0x6C797, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV13versionStringSSvpZ', symObjAddr: 0x4D18, symBinAddr: 0x27468, symSize: 0x0 }
  - { offset: 0x6C7B1, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV16internalUserInfoSDySSypGvpZ', symObjAddr: 0x1D858, symBinAddr: 0x322D8, symSize: 0x0 }
  - { offset: 0x6C7CB, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV41extendedAttributeArchivedLogIdentifierKeySSvpZ', symObjAddr: 0x52F0, symBinAddr: 0x2D148, symSize: 0x0 }
  - { offset: 0x6C7E5, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV40extendedAttributeArchivedLogTimestampKeySSvpZ', symObjAddr: 0x5300, symBinAddr: 0x2D158, symSize: 0x0 }
  - { offset: 0x6C7FF, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelO3allSayADGvpZ', symObjAddr: 0x1D860, symBinAddr: 0x322E0, symSize: 0x0 }
  - { offset: 0x6CA10, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV25defaultInstanceIdentifier_WZ', symObjAddr: 0x1B4, symBinAddr: 0x1E03C, symSize: 0x60 }
  - { offset: 0x6CA65, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV32baseConsoleDestinationIdentifier_WZ', symObjAddr: 0x274, symBinAddr: 0x1E0FC, symSize: 0x60 }
  - { offset: 0x6CABA, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV30systemLogDestinationIdentifier_WZ', symObjAddr: 0x334, symBinAddr: 0x1E1BC, symSize: 0x60 }
  - { offset: 0x6CB0F, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV25fileDestinationIdentifier_WZ', symObjAddr: 0x3F4, symBinAddr: 0x1E27C, symSize: 0x60 }
  - { offset: 0x6CB84, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV16internalUserInfo_WZ', symObjAddr: 0x5A0, symBinAddr: 0x1E428, symSize: 0xB8 }
  - { offset: 0x6CC47, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelO3all_WZ', symObjAddr: 0x690, symBinAddr: 0x1E518, symSize: 0x30 }
  - { offset: 0x6D365, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC7defaultABvpZ', symObjAddr: 0x1D868, symBinAddr: 0x322E8, symSize: 0x0 }
  - { offset: 0x6D37F, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC8logQueueSo17OS_dispatch_queueCvpZ', symObjAddr: 0x1D870, symBinAddr: 0x322F0, symSize: 0x0 }
  - { offset: 0x6D399, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC13dateFormatterSo06NSDateC0CSgvg7StaticsL_VAcEvpZ', symObjAddr: 0x5488, symBinAddr: 0x30838, symSize: 0x0 }
  - { offset: 0x6D42C, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOSLAASL1loiySbx_xtFZTW', symObjAddr: 0x878, symBinAddr: 0x1E640, symSize: 0x14 }
  - { offset: 0x6D468, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger1loiySbA2AC5LevelO_AEtF', symObjAddr: 0x88C, symBinAddr: 0x1E654, symSize: 0x10 }
  - { offset: 0x6D49E, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC7default_WZ', symObjAddr: 0x8E0, symBinAddr: 0x1E6A8, symSize: 0x8C }
  - { offset: 0x6D4DB, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC7defaultABvau', symObjAddr: 0x9C0, symBinAddr: 0x1E788, symSize: 0x40 }
  - { offset: 0x6D558, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC8logQueue_WZ', symObjAddr: 0x10F4, symBinAddr: 0x1EC10, symSize: 0x19C }
  - { offset: 0x6D5AA, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC8logQueueSo17OS_dispatch_queueCvau', symObjAddr: 0x1290, symBinAddr: 0x1EDAC, symSize: 0x40 }
  - { offset: 0x6D7BC, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyF10Foundation3URLV3url_SS9timestampt_Tg5', symObjAddr: 0x4300, symBinAddr: 0x21DA4, symSize: 0x14 }
  - { offset: 0x6D7F4, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyFSS_Tg5', symObjAddr: 0x4328, symBinAddr: 0x21DB8, symSize: 0x14 }
  - { offset: 0x6D82C, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyF9XCGLogger19DestinationProtocol_p_Tg5', symObjAddr: 0x433C, symBinAddr: 0x21DCC, symSize: 0x14 }
  - { offset: 0x6D859, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF9XCGLogger19DestinationProtocol_p_Tg5', symObjAddr: 0x4350, symBinAddr: 0x21DE0, symSize: 0x1C }
  - { offset: 0x6D8C9, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x44F0, symBinAddr: 0x21DFC, symSize: 0x108 }
  - { offset: 0x6DA0B, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF9XCGLogger19DestinationProtocol_p_Tg5', symObjAddr: 0x45F8, symBinAddr: 0x21F04, symSize: 0x13C }
  - { offset: 0x6DB0B, size: 0x8, addend: 0x0, symName: ___swift_mutable_project_boxed_opaque_existential_1, symObjAddr: 0x4744, symBinAddr: 0x22050, symSize: 0x28 }
  - { offset: 0x6DB1F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger19DestinationProtocol_pWOb', symObjAddr: 0x4858, symBinAddr: 0x22078, symSize: 0x18 }
  - { offset: 0x6DB33, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAACMa', symObjAddr: 0x4978, symBinAddr: 0x22090, symSize: 0x20 }
  - { offset: 0x6DB47, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOSHAASQWb', symObjAddr: 0x49BC, symBinAddr: 0x220B0, symSize: 0x4 }
  - { offset: 0x6DB5B, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOADSQAAWl', symObjAddr: 0x49C0, symBinAddr: 0x220B4, symSize: 0x44 }
  - { offset: 0x6DB6F, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOs12CaseIterableAA8AllCasessAEP_SlWT', symObjAddr: 0x4A04, symBinAddr: 0x220F8, symSize: 0x2C }
  - { offset: 0x6DB83, size: 0x8, addend: 0x0, symName: '_$sSay9XCGLoggerAAC5LevelOGMa', symObjAddr: 0x4A30, symBinAddr: 0x22124, symSize: 0x54 }
  - { offset: 0x6DB97, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsVMa', symObjAddr: 0x4A88, symBinAddr: 0x22178, symSize: 0x10 }
  - { offset: 0x6DBAB, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOwet', symObjAddr: 0x4AA8, symBinAddr: 0x22188, symSize: 0x90 }
  - { offset: 0x6DBBF, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOwst', symObjAddr: 0x4B38, symBinAddr: 0x22218, symSize: 0xBC }
  - { offset: 0x6DBD3, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOMa', symObjAddr: 0x4C08, symBinAddr: 0x222D4, symSize: 0x10 }
  - { offset: 0x6DBE7, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x4C18, symBinAddr: 0x222E4, symSize: 0x3C }
  - { offset: 0x6DBFB, size: 0x8, addend: 0x0, symName: '_$sSaySo17OS_dispatch_queueC8DispatchE10AttributesVGMa', symObjAddr: 0x4C94, symBinAddr: 0x22320, symSize: 0x54 }
  - { offset: 0x6DD80, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOSLAASL2leoiySbx_xtFZTW', symObjAddr: 0x89C, symBinAddr: 0x1E664, symSize: 0x14 }
  - { offset: 0x6DDFD, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOSLAASL2geoiySbx_xtFZTW', symObjAddr: 0x8B0, symBinAddr: 0x1E678, symSize: 0x14 }
  - { offset: 0x6DE7A, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOSLAASL1goiySbx_xtFZTW', symObjAddr: 0x8C4, symBinAddr: 0x1E68C, symSize: 0x14 }
  - { offset: 0x6E1EC, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelO8allCasesSayADGvgZ', symObjAddr: 0x0, symBinAddr: 0x1DEC8, symSize: 0x28 }
  - { offset: 0x6E240, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV41extendedAttributeArchivedLogIdentifierKeySSvau', symObjAddr: 0x68, symBinAddr: 0x1DEF0, symSize: 0xC }
  - { offset: 0x6E254, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV40extendedAttributeArchivedLogTimestampKeySSvau', symObjAddr: 0x74, symBinAddr: 0x1DEFC, symSize: 0xC }
  - { offset: 0x6E268, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelO11descriptionSSvg', symObjAddr: 0x80, symBinAddr: 0x1DF08, symSize: 0xE8 }
  - { offset: 0x6E28D, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV15userInfoKeyDevsSSvau', symObjAddr: 0x168, symBinAddr: 0x1DFF0, symSize: 0xC }
  - { offset: 0x6E2A1, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV15userInfoKeyTagsSSvau', symObjAddr: 0x174, symBinAddr: 0x1DFFC, symSize: 0xC }
  - { offset: 0x6E2B5, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV19userInfoKeyInternalSSvau', symObjAddr: 0x180, symBinAddr: 0x1E008, symSize: 0xC }
  - { offset: 0x6E2C9, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV14baseIdentifierSSvau', symObjAddr: 0x18C, symBinAddr: 0x1E014, symSize: 0xC }
  - { offset: 0x6E2E3, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV14baseIdentifierSSvgZ', symObjAddr: 0x198, symBinAddr: 0x1E020, symSize: 0x1C }
  - { offset: 0x6E304, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV25defaultInstanceIdentifierSSvau', symObjAddr: 0x214, symBinAddr: 0x1E09C, symSize: 0x40 }
  - { offset: 0x6E318, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV32baseConsoleDestinationIdentifierSSvau', symObjAddr: 0x2D4, symBinAddr: 0x1E15C, symSize: 0x40 }
  - { offset: 0x6E32C, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV30systemLogDestinationIdentifierSSvau', symObjAddr: 0x394, symBinAddr: 0x1E21C, symSize: 0x40 }
  - { offset: 0x6E340, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV25fileDestinationIdentifierSSvau', symObjAddr: 0x454, symBinAddr: 0x1E2DC, symSize: 0x40 }
  - { offset: 0x6E354, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV18logQueueIdentifierSSvau', symObjAddr: 0x504, symBinAddr: 0x1E38C, symSize: 0xC }
  - { offset: 0x6E368, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV18logQueueIdentifierSSvgZ', symObjAddr: 0x510, symBinAddr: 0x1E398, symSize: 0x1C }
  - { offset: 0x6E37C, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV15userInfoKeyTagsSSvgZ', symObjAddr: 0x52C, symBinAddr: 0x1E3B4, symSize: 0x1C }
  - { offset: 0x6E390, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV15userInfoKeyDevsSSvgZ', symObjAddr: 0x548, symBinAddr: 0x1E3D0, symSize: 0x1C }
  - { offset: 0x6E3A4, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV19userInfoKeyInternalSSvgZ', symObjAddr: 0x564, symBinAddr: 0x1E3EC, symSize: 0x1C }
  - { offset: 0x6E3B8, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV13versionStringSSvau', symObjAddr: 0x580, symBinAddr: 0x1E408, symSize: 0xC }
  - { offset: 0x6E3CC, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV13versionStringSSvgZ', symObjAddr: 0x58C, symBinAddr: 0x1E414, symSize: 0x14 }
  - { offset: 0x6E3F2, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV41extendedAttributeArchivedLogIdentifierKeySSvgZ', symObjAddr: 0x658, symBinAddr: 0x1E4E0, symSize: 0x1C }
  - { offset: 0x6E406, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9ConstantsV40extendedAttributeArchivedLogTimestampKeySSvgZ', symObjAddr: 0x674, symBinAddr: 0x1E4FC, symSize: 0x1C }
  - { offset: 0x6E41A, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelO3allSayADGvau', symObjAddr: 0x6C0, symBinAddr: 0x1E548, symSize: 0x40 }
  - { offset: 0x6E435, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelO3allSayADGvgZ', symObjAddr: 0x700, symBinAddr: 0x1E588, symSize: 0x40 }
  - { offset: 0x6E456, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelO8rawValueADSgSi_tcfC', symObjAddr: 0x740, symBinAddr: 0x1E5C8, symSize: 0x4 }
  - { offset: 0x6E46A, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelO8rawValueSivg', symObjAddr: 0x744, symBinAddr: 0x1E5CC, symSize: 0x8 }
  - { offset: 0x6E49A, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOSYAASY8rawValuexSg03RawD0Qz_tcfCTW', symObjAddr: 0x80C, symBinAddr: 0x1E5D4, symSize: 0x28 }
  - { offset: 0x6E4C3, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOSYAASY8rawValue03RawD0QzvgTW', symObjAddr: 0x834, symBinAddr: 0x1E5FC, symSize: 0xC }
  - { offset: 0x6E4DE, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOs12CaseIterableAAsAEP8allCases03AllF0QzvgZTW', symObjAddr: 0x840, symBinAddr: 0x1E608, symSize: 0x38 }
  - { offset: 0x6E536, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelOs23CustomStringConvertibleAAsAEP11descriptionSSvgTW', symObjAddr: 0x8D8, symBinAddr: 0x1E6A0, symSize: 0x8 }
  - { offset: 0x6E564, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC10identifier26includeDefaultDestinationsABSS_SbtcfC', symObjAddr: 0x96C, symBinAddr: 0x1E734, symSize: 0x54 }
  - { offset: 0x6E578, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC7defaultABvgZ', symObjAddr: 0xA00, symBinAddr: 0x1E7C8, symSize: 0x40 }
  - { offset: 0x6E5F5, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC11outputLevelAB0C0Ovs', symObjAddr: 0xB44, symBinAddr: 0x1E808, symSize: 0x128 }
  - { offset: 0x6E739, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC11outputLevelAB0C0OvM', symObjAddr: 0xC6C, symBinAddr: 0x1E930, symSize: 0x50 }
  - { offset: 0x6E75E, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC11outputLevelAB0C0OvM.resume.0', symObjAddr: 0xCBC, symBinAddr: 0x1E980, symSize: 0x11C }
  - { offset: 0x6E88A, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC16noMessageClosureypSgycvpfi', symObjAddr: 0xDD8, symBinAddr: 0x1EA9C, symSize: 0x10 }
  - { offset: 0x6E89E, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC16noMessageClosureypSgycvpfiADycfU_', symObjAddr: 0xDE8, symBinAddr: 0x1EAAC, symSize: 0x18 }
  - { offset: 0x6E8B2, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC16noMessageClosureypSgycvg', symObjAddr: 0xE00, symBinAddr: 0x1EAC4, symSize: 0x48 }
  - { offset: 0x6E8C6, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC16noMessageClosureypSgycvs', symObjAddr: 0xE48, symBinAddr: 0x1EB0C, symSize: 0x50 }
  - { offset: 0x6E8DA, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC10formattersSayAA20LogFormatterProtocol_pGSgvg', symObjAddr: 0xF88, symBinAddr: 0x1EB5C, symSize: 0x34 }
  - { offset: 0x6E8EE, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC10formattersSayAA20LogFormatterProtocol_pGSgvs', symObjAddr: 0xFBC, symBinAddr: 0x1EB90, symSize: 0x44 }
  - { offset: 0x6E902, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC10formattersSayAA20LogFormatterProtocol_pGSgvM', symObjAddr: 0x1000, symBinAddr: 0x1EBD4, symSize: 0x3C }
  - { offset: 0x6E954, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC8logQueueSo17OS_dispatch_queueCvgZ', symObjAddr: 0x12D0, symBinAddr: 0x1EDEC, symSize: 0x40 }
  - { offset: 0x6E975, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC13dateFormatterSo06NSDateC0CSgvg', symObjAddr: 0x1310, symBinAddr: 0x1EE2C, symSize: 0x68 }
  - { offset: 0x6E9B2, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC13dateFormatterSo06NSDateC0CSgvs', symObjAddr: 0x1378, symBinAddr: 0x1EE94, symSize: 0x10 }
  - { offset: 0x6EA0D, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC13dateFormatterSo06NSDateC0CSgvM', symObjAddr: 0x1388, symBinAddr: 0x1EEA4, symSize: 0x88 }
  - { offset: 0x6EA6B, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC13dateFormatterSo06NSDateC0CSgvM.resume.0', symObjAddr: 0x1410, symBinAddr: 0x1EF2C, symSize: 0x10 }
  - { offset: 0x6EA83, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC13dateFormatterSo06NSDateC0CSgvM.resume.0', symObjAddr: 0x1410, symBinAddr: 0x1EF2C, symSize: 0x10 }
  - { offset: 0x6EAA8, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC12destinationsSayAA19DestinationProtocol_pGvM', symObjAddr: 0x1498, symBinAddr: 0x1EF3C, symSize: 0x3C }
  - { offset: 0x6EB29, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC10identifier26includeDefaultDestinationsABSS_Sbtcfc', symObjAddr: 0x14D4, symBinAddr: 0x1EF78, symSize: 0x174 }
  - { offset: 0x6EBEF, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5setup5level17showLogIdentifier0D12FunctionName0d6ThreadH00D5Level0D9FileNames0D11LineNumbers0D4Date07writeToK004fileJ0yAB0J0O_S7bypSgAOSgtFZ', symObjAddr: 0x1648, symBinAddr: 0x1F0EC, symSize: 0xCC }
  - { offset: 0x6ECC7, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5setup5level17showLogIdentifier0D12FunctionName0d6ThreadH00D5Level0D9FileNames0D11LineNumbers0D4Date07writeToK004fileJ0yAB0J0O_S7bypSgAOSgtF', symObjAddr: 0x1714, symBinAddr: 0x1F1B8, symSize: 0x4B0 }
  - { offset: 0x6EE0A, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5logln_5level12functionName04fileE010lineNumber8userInfoyypSgyXK_AB5LevelOs12StaticStringVAMSiSDySSypGtFZ', symObjAddr: 0x1BC4, symBinAddr: 0x1F668, symSize: 0xD4 }
  - { offset: 0x6EE9E, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5logln_12functionName04fileD010lineNumber8userInfo7closureyAB5LevelO_s12StaticStringVALSiSDySSypGypSgyXEtFZ', symObjAddr: 0x1C98, symBinAddr: 0x1F73C, symSize: 0xCC }
  - { offset: 0x6EF33, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5logln_12functionName04fileD010lineNumber8userInfo7closureyAB5LevelO_S2SSiSDySSypGypSgyXEtFZ', symObjAddr: 0x1D64, symBinAddr: 0x1F808, symSize: 0xC4 }
  - { offset: 0x6EFC9, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5logln_5level12functionName04fileE010lineNumber8userInfoyypSgyXK_AB5LevelOs12StaticStringVAMSiSDySSypGtF', symObjAddr: 0x1E28, symBinAddr: 0x1F8CC, symSize: 0x3C }
  - { offset: 0x6F051, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5logln_12functionName04fileD010lineNumber8userInfo7closureyAB5LevelO_s12StaticStringVALSiSDySSypGypSgyXEtF', symObjAddr: 0x1E64, symBinAddr: 0x1F908, symSize: 0xC4 }
  - { offset: 0x6F132, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5logln_12functionName04fileD010lineNumber8userInfo7closureyAB5LevelO_S2SSiSDySSypGypSgyXEtF', symObjAddr: 0x1F28, symBinAddr: 0x1F9CC, symSize: 0x324 }
  - { offset: 0x6F586, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC4exec_7closureyAB5LevelO_yyXEtFZ', symObjAddr: 0x224C, symBinAddr: 0x1FCF0, symSize: 0x74 }
  - { offset: 0x6F5D8, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC4exec_7closureyAB5LevelO_yyXEtF', symObjAddr: 0x22C0, symBinAddr: 0x1FD64, symSize: 0x40 }
  - { offset: 0x6F65D, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC13logAppDetails19selectedDestinationyAA0F8Protocol_pSg_tF', symObjAddr: 0x2300, symBinAddr: 0x1FDA4, symSize: 0x808 }
  - { offset: 0x6FD07, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC11verboseExecyyyyXEF', symObjAddr: 0x34A0, symBinAddr: 0x20F44, symSize: 0x18 }
  - { offset: 0x6FD3C, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9debugExecyyyyXEF', symObjAddr: 0x34C0, symBinAddr: 0x20F64, symSize: 0x18 }
  - { offset: 0x6FD71, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC8infoExecyyyyXEF', symObjAddr: 0x34E0, symBinAddr: 0x20F84, symSize: 0x18 }
  - { offset: 0x6FDA6, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC11warningExecyyyyXEF', symObjAddr: 0x3500, symBinAddr: 0x20FA4, symSize: 0x18 }
  - { offset: 0x6FDDB, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC9errorExecyyyyXEF', symObjAddr: 0x3520, symBinAddr: 0x20FC4, symSize: 0x18 }
  - { offset: 0x6FE10, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC10severeExecyyyyXEF', symObjAddr: 0x35B4, symBinAddr: 0x21058, symSize: 0x18 }
  - { offset: 0x6FE45, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC11destination14withIdentifierAA19DestinationProtocol_pSgSS_tF', symObjAddr: 0x35CC, symBinAddr: 0x21070, symSize: 0x120 }
  - { offset: 0x6FF8C, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC3add11destinationSbAA19DestinationProtocol_p_tF', symObjAddr: 0x36EC, symBinAddr: 0x21190, symSize: 0x1D4 }
  - { offset: 0x70106, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC6remove11destinationSbAA19DestinationProtocol_p_tF', symObjAddr: 0x38C0, symBinAddr: 0x21364, symSize: 0x278 }
  - { offset: 0x70319, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC6remove25destinationWithIdentifierSbSS_tF', symObjAddr: 0x3B38, symBinAddr: 0x215DC, symSize: 0x7C }
  - { offset: 0x70368, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC12isEnabledFor5levelSbAB5LevelO_tF', symObjAddr: 0x3BB4, symBinAddr: 0x21658, symSize: 0x34 }
  - { offset: 0x703F2, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC6_logln_5level6sourceySS_AB5LevelOAA19DestinationProtocol_pSgtF', symObjAddr: 0x3BE8, symBinAddr: 0x2168C, symSize: 0x318 }
  - { offset: 0x7062D, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC16debugDescriptionSSvg', symObjAddr: 0x3F00, symBinAddr: 0x219A4, symSize: 0x238 }
  - { offset: 0x708CA, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAACfd', symObjAddr: 0x4138, symBinAddr: 0x21BDC, symSize: 0x4C }
  - { offset: 0x708ED, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAACfD', symObjAddr: 0x4184, symBinAddr: 0x21C28, symSize: 0x20 }
  - { offset: 0x70910, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAACs28CustomDebugStringConvertibleAAsACP16debugDescriptionSSvgTW', symObjAddr: 0x41A4, symBinAddr: 0x21C48, symSize: 0x28 }
  - { offset: 0x70932, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC13dateFormatterSo06NSDateC0CSgvg7StaticsL_VAcEvpZfiAEyXEfU_', symObjAddr: 0x41E8, symBinAddr: 0x21C8C, symSize: 0x118 }
  - { offset: 0x7098F, size: 0x8, addend: 0x0, symName: '_$s9XCGLoggerAAC5LevelO8rawValueADSgSi_tcfCTf4nd_n', symObjAddr: 0x4734, symBinAddr: 0x22040, symSize: 0x10 }
  - { offset: 0x70B0B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC6escapeSSvpZ', symObjAddr: 0x1320, symBinAddr: 0x27660, symSize: 0x0 }
  - { offset: 0x70B25, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC15resetForegroundSSvpZ', symObjAddr: 0x1330, symBinAddr: 0x27670, symSize: 0x0 }
  - { offset: 0x70B3F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC15resetBackgroundSSvpZ', symObjAddr: 0x1340, symBinAddr: 0x27680, symSize: 0x0 }
  - { offset: 0x70B59, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC5resetSSvpZ', symObjAddr: 0x1350, symBinAddr: 0x27690, symSize: 0x0 }
  - { offset: 0x70B73, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV3redAEvpZ', symObjAddr: 0x1360, symBinAddr: 0x276A0, symSize: 0x0 }
  - { offset: 0x71072, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5greenAEvpZ', symObjAddr: 0x1378, symBinAddr: 0x276B8, symSize: 0x0 }
  - { offset: 0x7108C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4blueAEvpZ', symObjAddr: 0x1390, symBinAddr: 0x276D0, symSize: 0x0 }
  - { offset: 0x710A6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5blackAEvpZ', symObjAddr: 0x13A8, symBinAddr: 0x276E8, symSize: 0x0 }
  - { offset: 0x710C0, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5whiteAEvpZ', symObjAddr: 0x13C0, symBinAddr: 0x27700, symSize: 0x0 }
  - { offset: 0x710DA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV9lightGreyAEvpZ', symObjAddr: 0x13D8, symBinAddr: 0x27718, symSize: 0x0 }
  - { offset: 0x710F4, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV8darkGreyAEvpZ', symObjAddr: 0x13F0, symBinAddr: 0x27730, symSize: 0x0 }
  - { offset: 0x7110E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV6orangeAEvpZ', symObjAddr: 0x1408, symBinAddr: 0x27748, symSize: 0x0 }
  - { offset: 0x71128, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV6purpleAEvpZ', symObjAddr: 0x1420, symBinAddr: 0x27760, symSize: 0x0 }
  - { offset: 0x71142, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV9darkGreenAEvpZ', symObjAddr: 0x1438, symBinAddr: 0x27778, symSize: 0x0 }
  - { offset: 0x7115C, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4cyanAEvpZ', symObjAddr: 0x1450, symBinAddr: 0x27790, symSize: 0x0 }
  - { offset: 0x7116A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC6escapeSSvau', symObjAddr: 0x0, symBinAddr: 0x22374, symSize: 0xC }
  - { offset: 0x71188, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC15resetForegroundSSvau', symObjAddr: 0x18, symBinAddr: 0x22380, symSize: 0xC }
  - { offset: 0x711A6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC15resetBackgroundSSvau', symObjAddr: 0x38, symBinAddr: 0x223A0, symSize: 0xC }
  - { offset: 0x711C4, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC5resetSSvau', symObjAddr: 0x58, symBinAddr: 0x223C0, symSize: 0xC }
  - { offset: 0x7129A, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterCMa', symObjAddr: 0x117C, symBinAddr: 0x23424, symSize: 0x20 }
  - { offset: 0x712AE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterCAA0dE8ProtocolAAWI', symObjAddr: 0x121C, symBinAddr: 0x23444, symSize: 0x24 }
  - { offset: 0x712C2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterCACs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x1240, symBinAddr: 0x23468, symSize: 0x44 }
  - { offset: 0x712D6, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0x1284, symBinAddr: 0x234AC, symSize: 0x14 }
  - { offset: 0x712EA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorVwet', symObjAddr: 0x129C, symBinAddr: 0x234C0, symSize: 0x20 }
  - { offset: 0x712FE, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorVwst', symObjAddr: 0x12BC, symBinAddr: 0x234E0, symSize: 0x2C }
  - { offset: 0x71312, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorVMa', symObjAddr: 0x12E8, symBinAddr: 0x2350C, symSize: 0x10 }
  - { offset: 0x7141B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC15resetForegroundSSvgZ', symObjAddr: 0x24, symBinAddr: 0x2238C, symSize: 0x14 }
  - { offset: 0x7142F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC15resetBackgroundSSvgZ', symObjAddr: 0x44, symBinAddr: 0x223AC, symSize: 0x14 }
  - { offset: 0x71443, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC5resetSSvgZ', symObjAddr: 0x64, symBinAddr: 0x223CC, symSize: 0x10 }
  - { offset: 0x71471, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV3redSivs', symObjAddr: 0x78, symBinAddr: 0x223DC, symSize: 0x10 }
  - { offset: 0x714A6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV3redSivs', symObjAddr: 0x78, symBinAddr: 0x223DC, symSize: 0x10 }
  - { offset: 0x714C1, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV3redSivM', symObjAddr: 0x88, symBinAddr: 0x223EC, symSize: 0x14 }
  - { offset: 0x714E6, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV3redSivM.resume.0', symObjAddr: 0x9C, symBinAddr: 0x22400, symSize: 0x1C }
  - { offset: 0x7151B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5greenSivg', symObjAddr: 0xB8, symBinAddr: 0x2241C, symSize: 0x8 }
  - { offset: 0x71554, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5greenSivs', symObjAddr: 0xC0, symBinAddr: 0x22424, symSize: 0x10 }
  - { offset: 0x71589, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5greenSivs', symObjAddr: 0xC0, symBinAddr: 0x22424, symSize: 0x10 }
  - { offset: 0x715A4, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5greenSivM', symObjAddr: 0xD0, symBinAddr: 0x22434, symSize: 0x14 }
  - { offset: 0x715C8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5greenSivM.resume.0', symObjAddr: 0xE4, symBinAddr: 0x22448, symSize: 0x1C }
  - { offset: 0x715FD, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4blueSivg', symObjAddr: 0x100, symBinAddr: 0x22464, symSize: 0x8 }
  - { offset: 0x71636, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4blueSivs', symObjAddr: 0x108, symBinAddr: 0x2246C, symSize: 0x10 }
  - { offset: 0x7166B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4blueSivs', symObjAddr: 0x108, symBinAddr: 0x2246C, symSize: 0x10 }
  - { offset: 0x71686, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4blueSivM', symObjAddr: 0x118, symBinAddr: 0x2247C, symSize: 0x14 }
  - { offset: 0x716AA, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4blueSivM.resume.0', symObjAddr: 0x12C, symBinAddr: 0x22490, symSize: 0x1C }
  - { offset: 0x7171E, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5colorAESo7UIColorC_tcfC', symObjAddr: 0x248, symBinAddr: 0x225A8, symSize: 0x17C }
  - { offset: 0x71874, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV11descriptionSSvg', symObjAddr: 0x3C4, symBinAddr: 0x22724, symSize: 0xAC }
  - { offset: 0x71903, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV3redAEvau', symObjAddr: 0x4B0, symBinAddr: 0x227D0, symSize: 0xC }
  - { offset: 0x71917, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV3redAEvgZ', symObjAddr: 0x4BC, symBinAddr: 0x227DC, symSize: 0x10 }
  - { offset: 0x7192B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5greenAEvau', symObjAddr: 0x4CC, symBinAddr: 0x227EC, symSize: 0xC }
  - { offset: 0x7193F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5greenAEvgZ', symObjAddr: 0x4D8, symBinAddr: 0x227F8, symSize: 0x10 }
  - { offset: 0x71953, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4blueAEvau', symObjAddr: 0x4E8, symBinAddr: 0x22808, symSize: 0xC }
  - { offset: 0x71967, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4blueAEvgZ', symObjAddr: 0x4F4, symBinAddr: 0x22814, symSize: 0x10 }
  - { offset: 0x7197B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5blackAEvau', symObjAddr: 0x504, symBinAddr: 0x22824, symSize: 0xC }
  - { offset: 0x7198F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5blackAEvgZ', symObjAddr: 0x510, symBinAddr: 0x22830, symSize: 0x10 }
  - { offset: 0x719A3, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5whiteAEvau', symObjAddr: 0x520, symBinAddr: 0x22840, symSize: 0xC }
  - { offset: 0x719B7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV5whiteAEvgZ', symObjAddr: 0x52C, symBinAddr: 0x2284C, symSize: 0x10 }
  - { offset: 0x719CB, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV9lightGreyAEvau', symObjAddr: 0x53C, symBinAddr: 0x2285C, symSize: 0xC }
  - { offset: 0x719DF, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV9lightGreyAEvgZ', symObjAddr: 0x548, symBinAddr: 0x22868, symSize: 0x10 }
  - { offset: 0x719F3, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV8darkGreyAEvau', symObjAddr: 0x558, symBinAddr: 0x22878, symSize: 0xC }
  - { offset: 0x71A07, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV8darkGreyAEvgZ', symObjAddr: 0x564, symBinAddr: 0x22884, symSize: 0x10 }
  - { offset: 0x71A1B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV6orangeAEvau', symObjAddr: 0x574, symBinAddr: 0x22894, symSize: 0xC }
  - { offset: 0x71A2F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV6orangeAEvgZ', symObjAddr: 0x580, symBinAddr: 0x228A0, symSize: 0x10 }
  - { offset: 0x71A43, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV6purpleAEvau', symObjAddr: 0x590, symBinAddr: 0x228B0, symSize: 0xC }
  - { offset: 0x71A57, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV6purpleAEvgZ', symObjAddr: 0x59C, symBinAddr: 0x228BC, symSize: 0x10 }
  - { offset: 0x71A6B, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV9darkGreenAEvau', symObjAddr: 0x5AC, symBinAddr: 0x228CC, symSize: 0xC }
  - { offset: 0x71A7F, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV9darkGreenAEvgZ', symObjAddr: 0x5B8, symBinAddr: 0x228D8, symSize: 0x10 }
  - { offset: 0x71A93, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4cyanAEvau', symObjAddr: 0x5C8, symBinAddr: 0x228E8, symSize: 0xC }
  - { offset: 0x71AA7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorV4cyanAEvgZ', symObjAddr: 0x5D4, symBinAddr: 0x228F4, symSize: 0x10 }
  - { offset: 0x71ABB, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC0B5ColorVs23CustomStringConvertibleAAsAFP11descriptionSSvgTW', symObjAddr: 0x5E4, symBinAddr: 0x22904, symSize: 0xC }
  - { offset: 0x71AE8, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterCACycfC', symObjAddr: 0x5F0, symBinAddr: 0x22910, symSize: 0x54 }
  - { offset: 0x71B45, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterCACycfc', symObjAddr: 0x644, symBinAddr: 0x22964, symSize: 0x48 }
  - { offset: 0x71BA3, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC8colorize5level4with2onyA2AC5LevelO_AC0B5ColorVSgAMtF', symObjAddr: 0x68C, symBinAddr: 0x229AC, symSize: 0x400 }
  - { offset: 0x71EB2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC15resetFormattingyyF', symObjAddr: 0xA8C, symBinAddr: 0x22DAC, symSize: 0x14C }
  - { offset: 0x71ED7, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC15clearFormattingyyF', symObjAddr: 0xBD8, symBinAddr: 0x22EF8, symSize: 0x14C }
  - { offset: 0x71F21, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC6format10logDetails7messageSSAA0dH0Vz_SSztF', symObjAddr: 0xD24, symBinAddr: 0x23044, symSize: 0xE4 }
  - { offset: 0x720D2, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterC16debugDescriptionSSvg', symObjAddr: 0xE08, symBinAddr: 0x23128, symSize: 0x2D4 }
  - { offset: 0x72321, size: 0x8, addend: 0x0, symName: '_$s9XCGLogger23XcodeColorsLogFormatterCAA0dE8ProtocolA2aDP6format10logDetails7messageSSAA0dI0Vz_SSztFTW', symObjAddr: 0x112C, symBinAddr: 0x233FC, symSize: 0x28 }
...
