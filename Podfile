source 'https://github.com/CocoaPods/Specs.git'
source '***********************:iphone/XMSpecs.git'
inhibit_all_warnings!
use_frameworks!
platform :ios, '12.0'
workspace 'tingLite.xcworkspace'
project 'tingLite.xcodeproj'

def xm3rdParty
  xmComm3rdParty
  pod 'DigitalUnion', '*******'
  pod 'WechatOpenSDK', '*******'
  pod 'TencentOpenAPI', '********'
  pod 'Weibo_SDK', '~> *******'
  pod 'iCarousel', :git => '***********************:iphone/iCarousel.git', :tag => '1.8.3'
  pod 'XMJADYun', '*******'
  pod 'BaiduMobAdSDK', :git => '***********************:iphone/baidumobadsdk.git', :tag => '5.39.2'
  pod 'Ads-CN', '*******.1'
  pod 'GDTMobSDK', '*********'
  pod 'PhoneNetSDK', :git => '***********************:yanglei/xmnetdiagnosis.git'
  ENV["XMMobileActivateSDK_source"] = '1'
  pod 'XMMobileActivateSDK', :git => '***********************:iphone/xmmobileactivatesdk.git', :tag => '0.3.0'
  xm**********************
  pod 'XMLOCBridge', :git => '***********************:iphone/lite/XMLOCBridge.git', :tag => '0.0.1'
#  pod 'XMThirdParty/YYThreadSafeArray', '0.4.5'
  pod 'YYThreadSafeArray', '0.0.2'
end

def xmCommentKit
  ENV['XMCommentKit_source'] = '1'
  pod 'XMCommentKit', :git => '***********************:iphone/XMCommentKit.git', :commit => '056e007'
  xmChatConstant
  xmRecorderManager
end

def xmDebug
  pod 'LookinServer', :git=>'***********************:iphone/third/LookinServer.git', :tag=>"1.2.6" , :configurations => ['Debug']
	pod 'XMFLEX','4.2.4', :configurations => ['Debug']
#	pod 'MLeaksFinder', '1.0.0', :configurations => ['Debug']
end

def xmCrash
  pod 'XMFirebase', '*******', :configurations => ['Alpha', 'Release']
  pod 'Bugly', '********'
#  pod 'KillBug', '~> 0.1.1'
end

def xmBXMSDK
  pod 'BXMSDK', :git => '***********************:iphone/lite/bxmsdk.git', :tag => '*******'
end

def xmComm3rdParty
  pod 'XM3rdParty/Kingfisher','0.2.11'
  pod 'XM3rdParty/SnapKit'
  pod 'XM3rdParty/RxSwift'
  pod 'XM3rdParty/SwiftyJSON'
  pod 'XM3rdParty/SwiftyUserDefaults'
  pod 'XM3rdParty/KeychainAccess'
  pod 'XM3rdParty/CryptoSwift'
  pod 'XM3rdParty/Lottie'
  pod 'MJRefresh', :git => '***********************:iphone/third/MJRefresh.git', :tag => '3.5.0'
  pod 'XMPKHUD', '1.1.5'
  pod 'XMKingfisherWebP', '0.0.2'
  pod 'XMYBImageBrowser', '2.3.3'
end

def xmAPM
#    ENV["XMAPM_source"] = '1'
    ENV["XMAPMUITest_source"] = '1'
    ENV["XMAPMUITest_nousefb"] = '1'
    pod 'fishhook', '0.2.2'
    pod 'XMAPM', '1.5.67'
    pod 'XMProtobuf', '*******'
    pod 'XMAPMUITest', '1.1.11', :configurations => ['Debug']
end

def xmMedia
  ENV["XMMedia_source"] = '1'
  pod 'XMMedia', :git => '***********************:iphone/ipad/xmmedia.git', :commit =>'a8eb4d7'
#  pod 'XMMedia', :path => "/Volumes/T7/Gits/XMMedia"
end

def xmPlayer
  # AVKit
  pod 'XMFFmpeg', '0.0.6'
#  pod 'XMAVKit/XMAVPlayer'  , :git => '***********************:iphone/XMAVKit.git', :tag =>'*******' #:branch => 'feature/player_for_audio_lite' 接入直播后不要使用commit 用tag
#  pod 'XMAVKit/XMAVDownload', :git => '***********************:iphone/XMAVKit.git', :tag =>'*******' #:branch => 'feature/player_for_audio_lite'
  pod 'XMAVKit', :git => '***********************:iphone/XMAVKit.git', :tag =>'*******' # :branch => 'feature/player_for_audio_lite'
  pod 'XMVideoPlayerKit', '0.3.21'
  pod 'XMVideoHttpCache', '1.0.3'
  pod 'XMOpenSDKEncrypted', '1.0.17'
  xmMedia
  pod 'XMIJKMediaPlayer', '0.2.0'
end

def xmPay
  # Pay
	pod 'XMPaySDK', '0.6.4'
  pod 'XMIAP', '0.1.2'
end

def xmAccount
  # Account
#  ENV['XMAccount_source'] = '1'
#  pod 'XMAccount', :git => '***********************:iphone/XMAccount.git', :commit => 'b604b91' #:branch => 'chenjl/mobtech'
#  ENV["XMAccountFastLogin_source"] = '1'
#  pod 'XMAccountFastLogin', :git => '***********************:iphone/XMAccountFastLogin.git', :commit => 'dfbe007'
  pod 'XMAccount', '2.0.0'
  pod 'XMAccountFastLogin', '3.0.0'
end

def xmSecurity
  # Security
  pod 'XMCipher', '1.1.1'
  pod 'XMUniKeyPlayGame', '1.0.20'
end

def xmHotFix
  ENV["XMApoloEngine_source"] = '1'
  pod 'XMApoloEngine', :git => '***********************:iphone/XMApoloEngine.git', :commit => 'cdc948b'
  ENV["XMScriptLoader_source"] = '1'
  pod 'XMScriptLoader', '0.0.21'
end

def xm_eventTracker
   pod 'XMEventTracker', :git => '***********************:iphone/XMEventTracker.git', :commit => '1acbfeb' #:branch => 'L/XMBehavi'
end

def xmABTest
  pod 'XMABTest', '0.0.30'
end

def xm_UBT
  pod 'XMUBT', :git => '***********************:iOSTechLab/XMUBT.git', :tag => '0.5.27', :configurations => ['Debug']
  pod 'XMUBTracker', '0.3.5', :configurations => ['Alpha', 'Release']
  #  pod 'XMEventTracker', '0.1.14'
  xm_eventTracker
end

def xmFingerPrint
    ENV["XMFingerPrint_source"] = '1'
    pod 'XMFingerPrint', '0.1.65'
end

def xmLog
  # XLog
#  pod 'XMXlog', :git => '***********************:iphone/XMXlog.git', :commit => "63d9e24"
  pod 'XMXlog', '0.5.11'
  pod 'XCGLogger', :git => '***********************:iphone/XCGLogger.git', :tag => '7.0.0'
  pod 'ObjcExceptionBridging', :git => '***********************:iphone/XCGLogger.git', :tag => '7.0.0'
  pod 'XMDauTrack', '0.0.32'
end

def xm**********************
  pod '**********************', :git => '***********************:iphone/**********************.git', :tag => '1.1'
end

def xmHybrid
  ENV["XMHybrid_source"] = "1"
  pod 'XMHybrid', :git => '***********************:iphone/XMHybrid.git', :commit => "20192c1e" #:branch => 'feature/lite'
#  pod 'XMHybrid', :path => "../XMHybrid"
end

def xmAlipayAuthLite
  pod 'AlipayAuthLiteSDK', :git => '***********************:iphone/lite/alipayauthlitesdk.git', :tag => '1.0'
end

def xmConfig
  pod 'XMConfigCenter','0.6.14'
  pod 'XMHTTPDNS', '0.6.19'
#  ENV["XMHTTPDNS_source"] = "1"
#  pod 'XMHTTPDNS', :path => '../XMHTTPDNS'
#  pod 'XMLCAIDSDK', :git => '***********************:yanglei/XMLCAIDSDK.git', :tag => '2.3.1'
end

def xmSocial
  pod 'XMSocial', '0.1.18'
  pod 'DouyinOpenSDK', '********'
end

def xmNetwork
  # 此必须用源码，打包机内部处理
  ENV['XMNetworkRequest_source'] = "1"
  pod 'XMNetworkRequest', '1.1.13'
  ENV['AFNetworking_source'] = '1'
  pod 'AFNetworking', '*******'
end

def xmUIKit
  ENV['XMUIKit_source'] = "1"
  pod 'XMUIKit', :git => '***********************:iphone/XMUIKit.git', :commit => '6a95c5e'
end

def xmAd

end

def tingModel
    ENV["XMTingModel_source"] = '1'
    pod 'XMTingModel', :git => '***********************:iphone/XMTingModel.git', :commit => '66ecc4d' #:branch => 'feature/model_for_lite'
end

def yyText
  pod 'YYText', '********'
end

def xmFoundation
  ENV["XMXUID_source"] = '1'
  pod 'XMXUID', '1.1.5'
  # Base
#  ENV["XMCategories_source"] = '1'
  pod 'XMCategories', '0.4.100'
  xmBase
  pod 'XMDataBase', '0.2.3'
#  pod 'XMTingModel', '0.0.7'
  pod 'XMThirdParty/Masonry', '0.4.5'
#  pod 'XMThirdParty/YYThreadSafeArray', '0.4.5'
  tingModel
end

def xmBase
  ENV["XMBase_source"] = '1'
#  pod 'XMBase', '0.2.9'
  pod 'XMBase', :git => '***********************:iphone/XMBase.git', :commit => '4c604a4e' # branch: feature/switch_https
  pod 'YYModel', '*******'
end

def xmSceneRadio
  pod 'XMSceneRadioKit', :git => '***********************:iphone/XMSceneRadioKit.git', :commit => '38164b2'
  # pod 'XMSceneRadioKit', :path => '../XMSceneRadioKit'
end

def xmLive
   
   xmLiveSupport
   
   ENV["XMLive_source"] = '1'
   ENV["XMLiveCore_source"] = '1'
   ENV["XMLiveGiftKit_source"] = '1'
   
   pod 'XMZegoLib', '0.5.1'

   pod 'XMPMTween', '0.0.5'
   pod 'XMIGListKit', '4.0.1'
   pod 'XMLiveMic', '0.0.8'
   pod 'XMLiveAudio', '0.0.26'
   pod 'XMLiveChat', '0.4.9'
   pod 'XMLive', :git => '***********************:iphone/XMLive.git', :commit => '7be0cb3e2'
   pod 'XMLiveCore', :git => '***********************:iphone/XMLiveCore.git', :commit => '579aa4b3'
   pod 'XMLiveGiftKit', :git => '***********************:iphone/XMLiveGiftKit.git', :commit => '4fb30ca'

#   pod 'XMLiveCore', :path => '../XMLiveCore'
#   pod 'XMLiveGiftKit', :path => '../XMLiveGiftKit'
   
end

def xmRouter
  pod 'XMRouter', '0.1.20'
end

def xmLiveSupport
    xmCommon
    pod 'pop', '********'
    
    pod 'XMIMCore', '1.0.33'
    pod 'XMCLog', '0.0.5'
    pod 'iosntp', '2.0.11'

    xmRouter
    pod 'XMCorePlayer', '********'
    pod 'XMChatBaseVC', '0.2.1'
    xmChatConstant
    
    pod 'XMFeedBack', '3.0.0'
    
    pod 'XMLitePlayer', '0.0.12'
    pod 'XMVideoCapture', '0.4.1'
    
    # 兼容YYThreadSafeArray 和 XMThirdParty/YYThreadSafeArray
    pod 'XMAccompanyPicker', '0.2.4'
    xmRecorderManager
end

def xmRecorderManager
  pod 'XMRecorderManager', '0.0.54'
  pod 'SocketRocket', '*******'
end

def xmChatConstant
  pod 'XMChatConstant', '0.2.0'
end

def xmCommon
    pod 'XMCommonUtil', '0.3.1'
    xmRouter
end

def xmSmartDraftKit
#  pod 'XMSmartDraftKit', :path => '../XMSmartDraftKit'
    pod 'XMSmartDraftKit', :git => '***********************:iphone/XMSmartDraftKit.git', :commit => 'e754cdf'
end

target 'tingLite' do
  xm3rdParty
  xmDebug
  xmCrash
  xmAPM
  xmPlayer
  xmPay
  xmAccount
  xmSecurity
  xmHybrid
  xmLog
  xm_UBT
  xmABTest
  xmConfig
  xmSocial
  xmUIKit
  xmFoundation
  xmSceneRadio
  xmAlipayAuthLite
  xmFingerPrint
  yyText
  xmBXMSDK
  xmCommon
  xmLive
  xmSmartDraftKit
  xmCommentKit
  xmHotFix
end

target 'tingLiteAPNSerEx' do
  xmNetwork
  xmBase
end

######## 注释module 意味着该module用framework方式，反之源码形式

#  HomeModule -> 依赖RouterModule、XMConfigModule、XMNetworkModule、BaseModule、ShareModule、MediaModule
target 'HomeModule' do project './HomeModule/HomeModule.xcodeproj'
  target 'HomeModuleTests' do inherit! :search_paths end
  xmSceneRadio
  xm3rdParty
end

# 看小说模块
target 'XMBookModule' do project './XMBookModule/XMBookModule.xcodeproj'
  xmComm3rdParty
  xmFoundation
end

#  MyListenModule -> 依赖BaseModule, RouterModule, XMConfigModule, ShareModule, MediaModule, XMHistoryModule, XMNetworkModule,XMUtilModule
target 'MyListenModule' do project './MyListenModule/MyListenModule.xcodeproj'
  target 'MyListenModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmFoundation
  xmSocial
end

#MineModule -> 依赖RouterModule、XMConfigModule、XMNetworkModule、BaseModule、ShareModule、AudioModule
target 'MineModule' do project './MineModule/MineModule.xcodeproj'
  target 'MineModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmAccount
  xmUIKit
  tingModel
  xm_UBT
  xmSocial
  xmConfig
  xmHotFix
end

#SearchModule -> 依赖BaseModule, RouterModule, XMConfigModule, XMNetworkModule, ShareModule, MediaModule,
target 'SearchModule' do project './SearchModule/SearchModule.xcodeproj'
  target 'SearchModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmFoundation
end

# AudioModule -> 依赖BaseModule, RouterModule, XMWebModule, ShareModule, MediaModule, XMConfigModule, XMNetworkModule
target 'AudioModule' do project './AudioModule/AudioModule.xcodeproj'
  target 'AudioModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmPay
  xmUIKit
  xmFoundation
  xmSocial
  yyText
  xmABTest
  xmSmartDraftKit
end

#  LoginModule -> 依赖RouterModule
target 'LoginModule' do project './LoginModule/LoginModule.xcodeproj'
  target 'LoginModuleTests' do inherit! :search_paths end
  xm3rdParty
  xm_eventTracker
  xmAccount
  xmSocial
end

#  XMWebModule -> 依赖BaseModule, XMNetworkModule, XMConfigModule，ShareModule
target 'XMWebModule' do project './XMWebModule/XMWebModule.xcodeproj'
  target 'XMWebModuleTests' do inherit! :search_paths end
  pod 'XM3rdParty/SwiftyJSON'
  pod 'XMPKHUD', '1.1.5'
  
  xmPlayer
  xmSocial
  xmPay
  xmHybrid
  xmFoundation
  xmAlipayAuthLite
  xmBXMSDK
  xmAPM
end

#  MediaModule -> 依赖XMUtilModule, XMNetworkModule, XMConfigModule，XMHistoryModule
target 'MediaModule' do project './MediaModule/MediaModule.xcodeproj'
  target 'MediaModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmPlayer
  xmFoundation
  xmUIKit
  xmAPM
  xmCommon
end


#  ShareModule -> 依赖BaseModule, XMNetworkModule, XMConfigModule
target 'ShareModule' do project './ShareModule/ShareModule.xcodeproj'
  target 'ShareModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmSocial
  xmFoundation
end

#  BaseModule -> 依赖RouterModule, XMHistoryModule, XMConfigModule, XMNetworkModule
target 'BaseModule' do project './BaseModule/BaseModule.xcodeproj'
  target 'BaseModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmUIKit
  xmFoundation
  xmPlayer
end

#  XMHistoryModule -> 依赖XMNetworkModule, XMConfigModule
target 'XMHistoryModule' do project './XMHistoryModule/XMHistoryModule.xcodeproj'
  target 'XMHistoryModuleTests' do inherit! :search_paths end
  pod 'XM3rdParty/SwiftyJSON'
  xmSecurity
  xmFoundation

end

#  RouterModule -> 依赖XMNetworkModule, XMConfigModule
target 'RouterModule' do project './RouterModule/RouterModule.xcodeproj'
  target 'RouterModuleTests' do inherit! :search_paths end
  xmFoundation
end

target 'XMADXModule' do project './XMADXModule/XMADXModule.xcodeproj'
  target 'XMADXModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmUIKit
  xmFoundation
  xmBXMSDK
end

#  XMConfigModule -> 依赖XMNetworkModule
target 'XMConfigModule' do project './XMConfigModule/XMConfigModule.xcodeproj'
  target 'XMConfigModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmAccount
  xmLog
  xm_UBT
  xmABTest
  xmConfig
  xmSecurity
  xmFoundation
  xmAPM
end

# XMReaderModule
target 'XMReaderModule' do project './XMReaderModule/XMReaderModule.xcodeproj'
  target 'XMReaderModuleTests' do inherit! :search_paths end
  xm_UBT
  yyText
  xmUIKit
  xmNetwork
  xmFoundation
  xm**********************
end

target 'XMNetworkModule' do project './XMNetworkModule/XMNetworkModule.xcodeproj'
  target 'XMNetworkModuleTests' do inherit! :search_paths end
  pod 'XM3rdParty/RxSwift'
  pod 'XMPKHUD', '1.1.5'
  xmNetwork
end

target 'XMUtilModule' do project './XMUtilModule/XMUtilModule.xcodeproj'
  target 'XMUtilModuleTests' do inherit! :search_paths end
  pod 'XMKingfisherWebP', '0.0.2'
  pod 'XMThirdParty/YYCache', '0.4.5'
  xmFoundation
  xmFingerPrint
end

target 'CommBusiness' do project './CommBusiness/CommBusiness.xcodeproj'
  target 'CommBusinessTests' do inherit! :search_paths end
  xmComm3rdParty
end

target 'VipModule' do project './VipModule/VipModule.xcodeproj'
  target 'VipModuleTests' do inherit! :search_paths end
  xmComm3rdParty
end

target 'XMAlertModule' do project './XMAlertModule/XMAlertModule.xcodeproj'
  target 'XMAlertModuleTests' do inherit! :search_paths end
  xm3rdParty
  xmFoundation
end

target 'ImmerseModule' do project './ImmerseModule/ImmerseModule.xcodeproj'
  xm3rdParty
  xmUIKit
  xmFoundation
  xmLive
end

target 'XMLiveModule' do project './XMLiveModule/XMLiveModule.xcodeproj'
  target 'XMLiveModuleTests' do inherit! :search_paths end
  xmUIKit
  xmFoundation
  xmLive
  xm_UBT
  pod 'XM3rdParty/Lottie'
end


target 'XMShortVideoModule' do project './XMShortVideoModule/XMShortVideoModule.xcodeproj'
  xmUIKit
  xmFoundation
  xm_UBT
  xmComm3rdParty
  xmMedia
end

target 'XMCommentModule' do project './XMCommentModule/XMCommentModule.xcodeproj'
  target 'XMCommentModuleTests' do inherit! :search_paths end
  xmUIKit
  xmFoundation
  xm_UBT
  xm3rdParty
  xmCommentKit
end

pre_install do |installer|
  Pod::Installer::Xcode::TargetValidator.send(:define_method, :verify_no_static_framework_transitive_dependencies) {}
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    #puts "#{target.name}"
    target.build_configurations.each do |config|
      config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      if ENV["XMAPMUITest_nousefb"]
          if target.name == 'XMAPM'
              config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)','DISABLE_FB_RETAIN_CYCLE']
          end
      end
    end
    
    # 针对Bundle类型的做强制不签名
    if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
      target.build_configurations.each do |config|
          config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
      end
    end
  end
end
